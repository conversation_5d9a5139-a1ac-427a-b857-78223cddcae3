﻿using System;
using System.Collections.Generic;

namespace NOBLE.API.Models.Generated;

public partial class TradeItemCategory
{
    public int TradeItemCategoryId { get; set; }

    public int TradeId { get; set; }

    public string? Name { get; set; }

    public string? ShortDescription { get; set; }

    public string? LongDescription { get; set; }

    public int? DisplayOrder { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public int CreatedPeopleId { get; set; }

    public int ModifiedPeopleId { get; set; }

    public virtual Trade Trade { get; set; } = null!;

    public virtual ICollection<TradeItem> TradeItems { get; set; } = new List<TradeItem>();
}
