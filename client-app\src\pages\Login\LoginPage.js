import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import LoginForm from '../../components/auth/LoginForm';
import { useAuth } from '../../context/AuthContext';
import './css/LoginPage.css';

const LoginPage = () => {
  const { login } = useAuth();
  const navigate = useNavigate();
  const [loginError, setLoginError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleLogin = async (formData) => {
    setIsSubmitting(true);
    setLoginError('');

    try {
      await login(formData.usernameOrEmail, formData.password, formData.rememberMe);
      navigate('/dashboard');
    } catch (err) {
      setLoginError(err.response?.data?.message || 'Login failed. Please check your credentials.');
      console.error('Login error:', err);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="login-page">
      <div className="page-container">
        <div className="left-section" style={{'--bg-image': 'url(/assets/images/logos/login-screen-bg.jpg)'}}>
          <img className="logo" src="/assets/images/logos/noble-logo.svg" alt="NOBLE Platform" />

          <div className="middle-content">
            <h1 className="hero-title">Build Smarter, Manage Faster:</h1>
            <p className="hero-subtitle">
              Your Ultimate Construction Project Management Solution
            </p>

            <a className="learn-more-button" href="#">Learn More</a>
          </div>

          <img
            className="bottom-icon"
            src="/assets/images/logos/noble-icon-mark.svg"
            alt="NOBLE Icon"
          />
        </div>

        <div className="right-section">
          <img className="company-logo" src="/assets/images/logos/stickbuild.svg" alt="Stick Build P/L" />

          <LoginForm
            onSubmit={handleLogin}
            error={loginError}
            isLoading={isSubmitting}
          />
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
