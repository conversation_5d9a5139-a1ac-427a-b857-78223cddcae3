using System.ComponentModel.DataAnnotations;

namespace NOBLE.API.Models.DTOs
{
    public class TradeDTO
    {
        public int TradeId { get; set; }
        public int? JobTypeId { get; set; }
        public int? ProjectId { get; set; }
        public int? ProcessId { get; set; }
        public int? ContractId { get; set; }
        public string? Name { get; set; }
        public string? Code { get; set; }
        public bool? TenderRequired { get; set; }
        public string? Description { get; set; }
        public string? ScopeHeader { get; set; }
        public string? ScopeFooter { get; set; }
        public int? DisplayOrder { get; set; }
        public int? DaysFromPcd { get; set; }
        public DateTime? InvitationDate { get; set; }
        public DateTime? DueDate { get; set; }
        public DateTime? ComparisonDueDate { get; set; }
        public DateTime? ContractDueDate { get; set; }
        public DateTime? ComparisonApprovalDate { get; set; }
        public decimal? ComparisonApprovalAmount { get; set; }
        public DateTime? CommencementDate { get; set; }
        public DateTime? CompletionDate { get; set; }
        public string? WorkOrderNumber { get; set; }
        public string? QuotesFile { get; set; }
        public string? PrelettingFile { get; set; }
        public string? SignedContractFile { get; set; }
        public int? CapeopleId { get; set; }
        public int? PmpeopleId { get; set; }
        public int? Flag { get; set; }
        
        public string? JobTypeName { get; set; }
        public string? ProjectName { get; set; }
        public string? ContractNumber { get; set; }
        public string? CapeopleName { get; set; }
        public string? PmpeopleName { get; set; }
    }

    public class TradeCreateDTO
    {
        public int? JobTypeId { get; set; }
        
        [Required]
        public int ProjectId { get; set; }
        
        public int? ProcessId { get; set; }
        public int? ContractId { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(25)]
        public string? Code { get; set; }
        
        public bool? TenderRequired { get; set; }
        
        [StringLength(1000)]
        public string? Description { get; set; }
        
        public string? ScopeHeader { get; set; }
        public string? ScopeFooter { get; set; }
        public int? DisplayOrder { get; set; }
        public int? DaysFromPcd { get; set; }
        public DateTime? InvitationDate { get; set; }
        public DateTime? DueDate { get; set; }
        public DateTime? ComparisonDueDate { get; set; }
        public DateTime? ContractDueDate { get; set; }
        public DateTime? ComparisonApprovalDate { get; set; }
        public decimal? ComparisonApprovalAmount { get; set; }
        public DateTime? CommencementDate { get; set; }
        public DateTime? CompletionDate { get; set; }
        
        [StringLength(25)]
        public string? WorkOrderNumber { get; set; }
        
        [StringLength(250)]
        public string? QuotesFile { get; set; }
        
        [StringLength(250)]
        public string? PrelettingFile { get; set; }
        
        [StringLength(250)]
        public string? SignedContractFile { get; set; }
        
        public int? CapeopleId { get; set; }
        public int? PmpeopleId { get; set; }
        public int? Flag { get; set; }
    }

    public class TradeUpdateDTO
    {
        public int? JobTypeId { get; set; }
        public int? ProjectId { get; set; }
        public int? ProcessId { get; set; }
        public int? ContractId { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(25)]
        public string? Code { get; set; }
        
        public bool? TenderRequired { get; set; }
        
        [StringLength(1000)]
        public string? Description { get; set; }
        
        public string? ScopeHeader { get; set; }
        public string? ScopeFooter { get; set; }
        public int? DisplayOrder { get; set; }
        public int? DaysFromPcd { get; set; }
        public DateTime? InvitationDate { get; set; }
        public DateTime? DueDate { get; set; }
        public DateTime? ComparisonDueDate { get; set; }
        public DateTime? ContractDueDate { get; set; }
        public DateTime? ComparisonApprovalDate { get; set; }
        public decimal? ComparisonApprovalAmount { get; set; }
        public DateTime? CommencementDate { get; set; }
        public DateTime? CompletionDate { get; set; }
        
        [StringLength(25)]
        public string? WorkOrderNumber { get; set; }
        
        [StringLength(250)]
        public string? QuotesFile { get; set; }
        
        [StringLength(250)]
        public string? PrelettingFile { get; set; }
        
        [StringLength(250)]
        public string? SignedContractFile { get; set; }
        
        public int? CapeopleId { get; set; }
        public int? PmpeopleId { get; set; }
        public int? Flag { get; set; }
    }
}
