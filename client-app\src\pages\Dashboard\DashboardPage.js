import MainLayout from '../../components/layout/MainLayout';
import { Link } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import './css/DashboardPage.css';

const HomeIconSvg = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
  </svg>
);

const DashboardPage = () => {
  const { currentUser } = useAuth();
  const fullName = currentUser ? `${currentUser.firstName} ${currentUser.lastName}` : 'User';

  return (
    <MainLayout>
      <div className="dashboard-container">
        <div className="header-container">
          <h1 className="welcome-text">Welcome, {fullName}</h1>
          <div className="breadcrumb-container">
            <Link to="/dashboard" className="breadcrumb-item active">
              <HomeIconSvg />
              Main Dashboard
            </Link>
            <span className="breadcrumb-separator">/</span>
            <Link to="/projects" className="breadcrumb-item">Projects Overview</Link>
          </div>
        </div>
        {/* Dashboard content will be added later */}
      </div>
    </MainLayout>
  );
};

export default DashboardPage;
