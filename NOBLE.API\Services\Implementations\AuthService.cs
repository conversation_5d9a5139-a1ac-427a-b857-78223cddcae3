using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using NOBLE.API.Infrastructure.Auth;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Models.Generated;
using NOBLE.API.Services.Interfaces;
using System.IdentityModel.Tokens.Jwt;
 
using System.Security.Claims;
using ClaimIdentity = System.Security.Claims.ClaimsIdentity;
using SecurityClaim = System.Security.Claims.Claim;
using System.Security.Cryptography;
using System.Text;

namespace NOBLE.API.Services.Implementations
{
    public class AuthService : IAuthService
    {
        private readonly NobleDbContext _context;
        private readonly JwtSettings _jwtSettings;

        public AuthService(NobleDbContext context, IOptions<JwtSettings> jwtSettings)
        {
            _context = context;
            _jwtSettings = jwtSettings.Value;
        }

        public async Task<AuthResponse> LoginAsync(LoginRequest request)
        { 
            var user = await _context.People
                .FirstOrDefaultAsync(u =>
                    (u.UserLogin != null && u.UserLogin.ToLower() == request.UsernameOrEmail.ToLower()) ||
                    (u.Email != null && u.Email.ToLower() == request.UsernameOrEmail.ToLower()));

            if (user == null)
            {
                throw new ApplicationException("Invalid credentials");
            }

    
            if (user.UserPassword != request.Password)
            {
                throw new ApplicationException("Invalid credentials");
            }
             
            if (user.Inactive)
            {
                throw new ApplicationException("Account is disabled. Please contact administrator.");
            }
             
            user.UserLastLogin = DateTime.UtcNow;
            await _context.SaveChangesAsync();
             
            var token = GenerateJwtToken(user);
            var refreshToken = GenerateRefreshToken();
            var tokenExpiration = DateTime.UtcNow.AddMinutes(_jwtSettings.AccessTokenExpirationMinutes);
             
             
            return new AuthResponse
            {
                Id = user.PeopleId,
                Username = user.UserLogin ?? string.Empty,
                Email = user.Email ?? string.Empty,
                FirstName = user.FirstName ?? string.Empty,
                LastName = user.LastName ?? string.Empty,
                UserType = user.UserType ?? string.Empty,
                Token = token,
                RefreshToken = refreshToken,
                TokenExpiration = tokenExpiration
            };
        }

        public async Task<AuthResponse> RefreshTokenAsync(RefreshTokenRequest request)
        {
            var principal = GetPrincipalFromExpiredToken(request.Token);
            var username = principal.Identity?.Name;

            if (string.IsNullOrEmpty(username))
            {
                throw new ApplicationException("Invalid token");
            }

            var user = await _context.People
                .FirstOrDefaultAsync(u => u.UserLogin == username);

            if (user == null)
            {
                throw new ApplicationException("Invalid token");
            }
 
            var newToken = GenerateJwtToken(user);
            var newRefreshToken = GenerateRefreshToken();
            var tokenExpiration = DateTime.UtcNow.AddMinutes(_jwtSettings.AccessTokenExpirationMinutes);
             
            return new AuthResponse
            {
                Id = user.PeopleId,
                Username = user.UserLogin ?? string.Empty,
                Email = user.Email ?? string.Empty,
                FirstName = user.FirstName ?? string.Empty,
                LastName = user.LastName ?? string.Empty,
                UserType = user.UserType ?? string.Empty,
                Token = newToken,
                RefreshToken = newRefreshToken,
                TokenExpiration = tokenExpiration
            };
        }

        public async Task<bool> RevokeTokenAsync(string username)
        {
            var user = await _context.People.FirstOrDefaultAsync(u => u.UserLogin == username);

            if (user == null)
                return false;

           
            return true;
        }

        private string GenerateJwtToken(Person user)
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(_jwtSettings.Secret);

            var claims = new List<SecurityClaim>
            {
                new SecurityClaim(ClaimTypes.Name, user.UserLogin ?? string.Empty),
                new SecurityClaim(ClaimTypes.NameIdentifier, user.PeopleId.ToString()),
                new SecurityClaim(ClaimTypes.Email, user.Email ?? string.Empty),
                new SecurityClaim(ClaimTypes.Role, user.UserType ?? "User")
            };

            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(claims),
                Expires = DateTime.UtcNow.AddMinutes(_jwtSettings.AccessTokenExpirationMinutes),
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature),
                Issuer = _jwtSettings.Issuer,
                Audience = _jwtSettings.Audience
            };

            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }

        private string GenerateRefreshToken()
        {
            var randomNumber = new byte[32];
            using var rng = RandomNumberGenerator.Create();
            rng.GetBytes(randomNumber);
            return Convert.ToBase64String(randomNumber);
        }

        private ClaimsPrincipal GetPrincipalFromExpiredToken(string token)
        {
            var tokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(Encoding.ASCII.GetBytes(_jwtSettings.Secret)),
                ValidateIssuer = true,
                ValidateAudience = true,
                ValidIssuer = _jwtSettings.Issuer,
                ValidAudience = _jwtSettings.Audience,
                ValidateLifetime = false // We don't care about the token's expiration date
            };

            var tokenHandler = new JwtSecurityTokenHandler();
            var principal = tokenHandler.ValidateToken(token, tokenValidationParameters, out var securityToken);

            if (securityToken is not JwtSecurityToken jwtSecurityToken ||
                !jwtSecurityToken.Header.Alg.Equals(SecurityAlgorithms.HmacSha256, StringComparison.InvariantCultureIgnoreCase))
            {
                throw new SecurityTokenException("Invalid token");
            }

            return principal;
        }
    }
}
