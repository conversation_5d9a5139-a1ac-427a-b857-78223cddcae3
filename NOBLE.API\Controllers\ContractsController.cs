using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class ContractsController : ControllerBase
    {
        private readonly IContractService _contractService;
        
        public ContractsController(IContractService contractService)
        {
            _contractService = contractService;
        }
        
        // GET: api/Contracts
        [HttpGet]
        public async Task<ActionResult<IEnumerable<ContractDTO>>> GetContracts()
        {
            var contracts = await _contractService.GetAllContractsAsync();
            return Ok(contracts);
        }
        
        // GET: api/Contracts/5
        [HttpGet("{id}")]
        public async Task<ActionResult<ContractDTO>> GetContract(int id)
        {
            try
            {
                var contract = await _contractService.GetContractByIdAsync(id);
                return Ok(contract);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // GET: api/Contracts/Trade/5
        [HttpGet("Trade/{tradeId}")]
        public async Task<ActionResult<IEnumerable<ContractDTO>>> GetContractsByTrade(int tradeId)
        {
            try
            {
                var contracts = await _contractService.GetContractsByTradeIdAsync(tradeId);
                return Ok(contracts);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // GET: api/Contracts/Children/5
        [HttpGet("Children/{parentId}")]
        public async Task<ActionResult<IEnumerable<ContractDTO>>> GetChildContracts(int parentId)
        {
            try
            {
                var childContracts = await _contractService.GetChildContractsAsync(parentId);
                return Ok(childContracts);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // POST: api/Contracts
        [HttpPost]
        public async Task<ActionResult<ContractDTO>> CreateContract(ContractCreateDTO contractDto)
        {
            try
            {
                var createdContract = await _contractService.CreateContractAsync(contractDto);
                return CreatedAtAction(nameof(GetContract), new { id = createdContract.ContractId }, createdContract);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // PUT: api/Contracts/5
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateContract(int id, ContractUpdateDTO contractDto)
        {
            try
            {
                var updatedContract = await _contractService.UpdateContractAsync(id, contractDto);
                return Ok(updatedContract);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // DELETE: api/Contracts/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteContract(int id)
        {
            try
            {
                var result = await _contractService.DeleteContractAsync(id);
                
                if (!result)
                    return NotFound(new { message = $"Contract with ID {id} not found" });
                    
                return NoContent();
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
    }
}
