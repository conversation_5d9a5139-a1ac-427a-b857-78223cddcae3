using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class HolidaysController : ControllerBase
    {
        private readonly IHolidayService _holidayService;
        
        public HolidaysController(IHolidayService holidayService)
        {
            _holidayService = holidayService;
        }
        
        // GET: api/Holidays
        [HttpGet]
        public async Task<ActionResult<IEnumerable<HolidayDTO>>> GetHolidays()
        {
            var holidays = await _holidayService.GetAllHolidaysAsync();
            return Ok(holidays);
        }
        
        // GET: api/Holidays/2023-12-25
        [HttpGet("{date}")]
        public async Task<ActionResult<HolidayDTO>> GetHoliday(DateTime date)
        {
            try
            {
                var holiday = await _holidayService.GetHolidayByDateAsync(date);
                return Ok(holiday);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // GET: api/Holidays/Year/2023
        [HttpGet("Year/{year}")]
        public async Task<ActionResult<IEnumerable<HolidayDTO>>> GetHolidaysByYear(int year)
        {
            var holidays = await _holidayService.GetHolidaysByYearAsync(year);
            return Ok(holidays);
        }
        
        // GET: api/Holidays/Year/2023/Month/12
        [HttpGet("Year/{year}/Month/{month}")]
        public async Task<ActionResult<IEnumerable<HolidayDTO>>> GetHolidaysByMonth(int year, int month)
        {
            var holidays = await _holidayService.GetHolidaysByMonthAsync(year, month);
            return Ok(holidays);
        }
        
        // GET: api/Holidays/Range?startDate=2023-01-01&endDate=2023-12-31
        [HttpGet("Range")]
        public async Task<ActionResult<IEnumerable<HolidayDTO>>> GetHolidaysByDateRange([FromQuery] DateTime startDate, [FromQuery] DateTime endDate)
        {
            var holidays = await _holidayService.GetHolidaysByDateRangeAsync(startDate, endDate);
            return Ok(holidays);
        }
        
        // GET: api/Holidays/IsHoliday/2023-12-25
        [HttpGet("IsHoliday/{date}")]
        public async Task<ActionResult<bool>> IsHoliday(DateTime date)
        {
            var isHoliday = await _holidayService.IsHolidayAsync(date);
            return Ok(isHoliday);
        }
        
        // POST: api/Holidays
        [HttpPost]
        public async Task<ActionResult<HolidayDTO>> CreateHoliday(HolidayCreateDTO holidayDto)
        {
            try
            {
                var createdHoliday = await _holidayService.CreateHolidayAsync(holidayDto);
                return CreatedAtAction(nameof(GetHoliday), new { date = createdHoliday.HolidayDate }, createdHoliday);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // DELETE: api/Holidays/2023-12-25
        [HttpDelete("{date}")]
        public async Task<IActionResult> DeleteHoliday(DateTime date)
        {
            var result = await _holidayService.DeleteHolidayAsync(date);
            
            if (!result)
                return NotFound(new { message = $"Holiday with date {date:yyyy-MM-dd} not found" });
                
            return NoContent();
        }
    }
}
