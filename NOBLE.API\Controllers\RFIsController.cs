using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class RFIsController : ControllerBase
    {
        private readonly IRFIService _rfiService;
        
        public RFIsController(IRFIService rfiService)
        {
            _rfiService = rfiService;
        }
        
        // GET: api/RFIs
        [HttpGet]
        public async Task<ActionResult<IEnumerable<RFIDTO>>> GetRFIs()
        {
            var rfis = await _rfiService.GetAllRFIsAsync();
            return Ok(rfis);
        }
        
        // GET: api/RFIs/5
        [HttpGet("{id}")]
        public async Task<ActionResult<RFIDTO>> GetRFI(int id)
        {
            try
            {
                var rfi = await _rfiService.GetRFIByIdAsync(id);
                return Ok(rfi);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // GET: api/RFIs/Project/5
        [HttpGet("Project/{projectId}")]
        public async Task<ActionResult<IEnumerable<RFIDTO>>> GetRFIsByProject(int projectId)
        {
            try
            {
                var rfis = await _rfiService.GetRFIsByProjectIdAsync(projectId);
                return Ok(rfis);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // POST: api/RFIs
        [HttpPost]
        public async Task<ActionResult<RFIDTO>> CreateRFI(RFICreateDTO rfiDto)
        {
            try
            {
                var createdRFI = await _rfiService.CreateRFIAsync(rfiDto);
                return CreatedAtAction(nameof(GetRFI), new { id = createdRFI.RFIId }, createdRFI);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // PUT: api/RFIs/5
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateRFI(int id, RFIUpdateDTO rfiDto)
        {
            try
            {
                var updatedRFI = await _rfiService.UpdateRFIAsync(id, rfiDto);
                return Ok(updatedRFI);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // DELETE: api/RFIs/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteRFI(int id)
        {
            try
            {
                var result = await _rfiService.DeleteRFIAsync(id);
                
                if (!result)
                    return NotFound(new { message = $"RFI with ID {id} not found" });
                    
                return NoContent();
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // GET: api/RFIs/5/Responses
        [HttpGet("{rfiId}/Responses")]
        public async Task<ActionResult<IEnumerable<RFIResponseDTO>>> GetRFIResponses(int rfiId)
        {
            try
            {
                var responses = await _rfiService.GetRFIResponsesByRFIIdAsync(rfiId);
                return Ok(responses);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // GET: api/RFIs/Responses/5
        [HttpGet("Responses/{id}")]
        public async Task<ActionResult<RFIResponseDTO>> GetRFIResponse(int id)
        {
            try
            {
                var response = await _rfiService.GetRFIResponseByIdAsync(id);
                return Ok(response);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // POST: api/RFIs/Responses
        [HttpPost("Responses")]
        public async Task<ActionResult<RFIResponseDTO>> CreateRFIResponse(RFIResponseCreateDTO rfiResponseDto)
        {
            try
            {
                var createdResponse = await _rfiService.CreateRFIResponseAsync(rfiResponseDto);
                return CreatedAtAction(nameof(GetRFIResponse), new { id = createdResponse.ResponseId }, createdResponse);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // PUT: api/RFIs/Responses/5
        [HttpPut("Responses/{id}")]
        public async Task<IActionResult> UpdateRFIResponse(int id, RFIResponseUpdateDTO rfiResponseDto)
        {
            try
            {
                var updatedResponse = await _rfiService.UpdateRFIResponseAsync(id, rfiResponseDto);
                return Ok(updatedResponse);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // DELETE: api/RFIs/Responses/5
        [HttpDelete("Responses/{id}")]
        public async Task<IActionResult> DeleteRFIResponse(int id)
        {
            try
            {
                var result = await _rfiService.DeleteRFIResponseAsync(id);
                
                if (!result)
                    return NotFound(new { message = $"RFI Response with ID {id} not found" });
                    
                return NoContent();
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
    }
}
