using System.ComponentModel.DataAnnotations;

namespace NOBLE.API.Models.DTOs
{
    public class TradeItemDTO
    {
        public int TradeItemId { get; set; }
        public int TradeItemCategoryId { get; set; }
        public string? Name { get; set; }
        public string? Units { get; set; }
        public string? ScopeOfWorks { get; set; }
        public int? DisplayOrder { get; set; }
        public bool? RequiresQuantityCheck { get; set; }
        public bool? RequiredInProposal { get; set; }
        
        // Navigation properties for display purposes
        public string? CategoryName { get; set; }
        public string? TradeName { get; set; }
        public string? ProjectName { get; set; }
    }

    public class TradeItemCreateDTO
    {
        [Required]
        public int TradeItemCategoryId { get; set; }
        
        [Required]
        [StringLength(250)]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(50)]
        public string? Units { get; set; }
        
        [StringLength(1000)]
        public string? ScopeOfWorks { get; set; }
        
        public int? DisplayOrder { get; set; }
        public bool? RequiresQuantityCheck { get; set; }
        public bool? RequiredInProposal { get; set; }
    }

    public class TradeItemUpdateDTO
    {
        [StringLength(250)]
        public string? Name { get; set; }
        
        [StringLength(50)]
        public string? Units { get; set; }
        
        [StringLength(1000)]
        public string? ScopeOfWorks { get; set; }
        
        public int? DisplayOrder { get; set; }
        public bool? RequiresQuantityCheck { get; set; }
        public bool? RequiredInProposal { get; set; }
    }
}
