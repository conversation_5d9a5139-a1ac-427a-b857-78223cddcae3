using NOBLE.API.Models.DTOs;

namespace NOBLE.API.Services.Interfaces
{
    public interface IInvitationTemplateService
    {
        Task<IEnumerable<InvitationTemplateDTO>> GetAllInvitationTemplatesAsync();
        Task<InvitationTemplateDTO> GetInvitationTemplateByIdAsync(int id);
        Task<InvitationTemplateDTO> CreateInvitationTemplateAsync(InvitationTemplateCreateDTO invitationTemplateDto);
        Task<InvitationTemplateDTO> UpdateInvitationTemplateAsync(int id, InvitationTemplateUpdateDTO invitationTemplateDto);
        Task<bool> DeleteInvitationTemplateAsync(int id);
    }
}
