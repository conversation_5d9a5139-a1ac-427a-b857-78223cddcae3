﻿using System;
using System.Collections.Generic;

namespace NOBLE.API.Models.Generated;

public partial class Project
{
    public int ProjectId { get; set; }

    public int BusinessUnitId { get; set; }

    public string? Name { get; set; }

    public string? Number { get; set; }

    public string? Year { get; set; }

    public string? Description { get; set; }

    public string? Street { get; set; }

    public string? City { get; set; }

    public string? State { get; set; }

    public string? PostalCode { get; set; }

    public string? AttachmentsFolder { get; set; }

    public int? DefectsLiability { get; set; }

    public string? LiquidatedDamages { get; set; }

    public string? SiteAllowances { get; set; }

    public string? Retention { get; set; }

    public string? RetentionToCertification { get; set; }

    public string? RetentionToDlp { get; set; }

    public string? Interest { get; set; }

    public string? SpecialClause { get; set; }

    public string? LawOfSubcontract { get; set; }

    public string? ProjectStatus { get; set; }

    public DateTime? CommencementDate { get; set; }

    public DateTime? CompletionDate { get; set; }

    public string? DistributionListInfo { get; set; }

    public decimal? ContractAmount { get; set; }

    public string? PaymentTerms { get; set; }

    public string? ClaimFrequency { get; set; }

    public decimal? Waranty1Amount { get; set; }

    public DateTime? Waranty1Date { get; set; }

    public decimal? Waranty2Amount { get; set; }

    public DateTime? Waranty2Date { get; set; }

    public DateTime? PracticalCompletionDate { get; set; }

    public DateTime? FirstClaimDueDate { get; set; }

    public string? Principal { get; set; }

    public string? PrincipalAbn { get; set; }

    public string? AccountName { get; set; }

    public string? Bsb { get; set; }

    public string? AccountNumber { get; set; }

    public string? Siteaddress { get; set; }

    public string? SiteSuburb { get; set; }

    public string? SiteState { get; set; }

    public string? SitePostalCode { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public int CreatedPeopleId { get; set; }

    public int ModifiedPeopleId { get; set; }

    public virtual ICollection<Budget> Budgets { get; set; } = new List<Budget>();

    public virtual BusinessUnit BusinessUnit { get; set; } = null!;

    public virtual ICollection<Claim> Claims { get; set; } = new List<Claim>();

    public virtual ICollection<ClientVariation> ClientVariations { get; set; } = new List<ClientVariation>();

    public virtual ICollection<Eot> Eots { get; set; } = new List<Eot>();

    public virtual ICollection<Rfi> Rfis { get; set; } = new List<Rfi>();

    public virtual ICollection<Trade> Trades { get; set; } = new List<Trade>();
}
