import React from 'react';
import { Navigate, Outlet } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';

const ProtectedRoute = ({ requiredRoles }) => {
  const { currentUser, isAuthenticated } = useAuth();
  
  // Check if user is authenticated
  if (!isAuthenticated()) {
    // Redirect to login page if not authenticated
    return <Navigate to="/login" replace />;
  }
  
  // Check if specific roles are required
  if (requiredRoles && requiredRoles.length > 0) {
    // Check if user has required role
    if (!currentUser || !requiredRoles.includes(currentUser.role)) {
      // Redirect to unauthorized page if user doesn't have required role
      return <Navigate to="/unauthorized" replace />;
    }
  }
  
  // Render the child routes
  return <Outlet />;
};

export default ProtectedRoute;
