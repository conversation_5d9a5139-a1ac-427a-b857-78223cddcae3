﻿using System;
using System.Collections.Generic;

namespace NOBLE.API.Models.Generated;

public partial class Person
{
    public int PeopleId { get; set; }

    public string Type { get; set; } = null!;

    public int? SubContractorId { get; set; }

    public string? FirstName { get; set; }

    public string? LastName { get; set; }

    public string? Street { get; set; }

    public string? City { get; set; }

    public string? State { get; set; }

    public string? PostCode { get; set; }

    public string? Phone { get; set; }

    public string? Mobile { get; set; }

    public string? Email { get; set; }

    public string? EmployeePosition { get; set; }

    public string? UserType { get; set; }

    public string? UserLogin { get; set; }

    public string? UserPassword { get; set; }

    public string? UserSignatureFile { get; set; }

    public int? BusinessUnitId { get; set; }

    public DateTime? Dob { get; set; }

    public string? EmergencyContactName { get; set; }

    public string? EmergencyContactNumber { get; set; }

    public DateTime? UserLastLogin { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public int CreatedPeopleId { get; set; }

    public int ModifiedPeopleId { get; set; }

    public bool Inactive { get; set; }

    public virtual BusinessUnit? BusinessUnit { get; set; }

    public virtual ICollection<Trade> TradeCapeople { get; set; } = new List<Trade>();

    public virtual ICollection<TradeParticipation> TradeParticipations { get; set; } = new List<TradeParticipation>();

    public virtual ICollection<Trade> TradePmpeople { get; set; } = new List<Trade>();
}
