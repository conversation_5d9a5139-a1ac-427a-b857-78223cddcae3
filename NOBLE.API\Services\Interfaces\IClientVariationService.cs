using NOBLE.API.Models.DTOs;

namespace NOBLE.API.Services.Interfaces
{
    public interface IClientVariationService
    {
        Task<IEnumerable<ClientVariationDTO>> GetAllClientVariationsAsync();
        Task<ClientVariationDTO> GetClientVariationByIdAsync(int id);
        Task<IEnumerable<ClientVariationDTO>> GetClientVariationsByProjectIdAsync(int projectId);
        Task<IEnumerable<ClientVariationDTO>> GetChildClientVariationsAsync(int parentId);
        Task<ClientVariationDTO> CreateClientVariationAsync(ClientVariationCreateDTO clientVariationDto);
        Task<ClientVariationDTO> UpdateClientVariationAsync(int id, ClientVariationUpdateDTO clientVariationDto);
        Task<bool> DeleteClientVariationAsync(int id);
    }
}
