﻿using System;
using System.Collections.Generic;

namespace NOBLE.API.Models.Generated;

public partial class TradeItem
{
    public int TradeItemId { get; set; }

    public int TradeItemCategoryId { get; set; }

    public string? Name { get; set; }

    public string? Units { get; set; }

    public string? ScopeOfWorks { get; set; }

    public int? DisplayOrder { get; set; }

    public bool? RequiresQuantityCheck { get; set; }

    public bool? RequiredInProposal { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public int CreatedPeopleId { get; set; }

    public int ModifiedPeopleId { get; set; }

    public virtual TradeItemCategory TradeItemCategory { get; set; } = null!;
}
