using NOBLE.API.Models.DTOs;

namespace NOBLE.API.Services.Interfaces
{
    public interface IRFIsResponseAttachmentService
    {
        Task<IEnumerable<RFIsResponseAttachmentDTO>> GetAllRFIsResponseAttachmentsAsync();
        Task<RFIsResponseAttachmentDTO> GetRFIsResponseAttachmentByIdAsync(int id);
        Task<IEnumerable<RFIsResponseAttachmentDTO>> GetRFIsResponseAttachmentsByResponseIdAsync(int responseId);
        Task<IEnumerable<RFIsResponseAttachmentDTO>> GetRFIsResponseAttachmentsByRFIIdAsync(int rfiId);
        Task<RFIsResponseAttachmentDTO> CreateRFIsResponseAttachmentAsync(RFIsResponseAttachmentCreateDTO rfisResponseAttachmentDto);
        Task<RFIsResponseAttachmentDTO> UpdateRFIsResponseAttachmentAsync(int id, RFIsResponseAttachmentUpdateDTO rfisResponseAttachmentDto);
        Task<bool> DeleteRFIsResponseAttachmentAsync(int id);
    }
}
