using Microsoft.EntityFrameworkCore;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Models.Generated;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Services.Implementations
{
    public class RFIService : IRFIService
    {
        private readonly NobleDbContext _context;
        private readonly IUserContextService _userContextService;

        public RFIService(NobleDbContext context, IUserContextService userContextService)
        {
            _context = context;
            _userContextService = userContextService;
        }

        public async Task<IEnumerable<RFIDTO>> GetAllRFIsAsync()
        {
            return await _context.Rfis
                .Include(r => r.Project)
                .Include(r => r.RfisResponses)
                .Select(r => new RFIDTO
                {
                    RFIId = r.Rfiid,
                    ProjectId = r.ProjectId,
                    Number = r.Number,
                    Subject = r.Subject,
                    Description = r.Description,
                    Status = r.Status,
                    RaiseDate = r.RaiseDate,
                    TargetAnswerDate = r.TargetAnswerDate,
                    ActualAnswerDate = r.ActualAnswerDate,
                    ReferenceFile = r.ReferenceFile,
                    ClientResponseFile = r.ClientResponseFile,
                    ClientResponseSummary = r.ClientResponseSummary,
                    ProjectName = r.Project.Name,
                    ResponseCount = r.RfisResponses.Count
                })
                .ToListAsync();
        }

        public async Task<RFIDTO> GetRFIByIdAsync(int id)
        {
            var rfi = await _context.Rfis
                .Include(r => r.Project)
                .Include(r => r.RfisResponses)
                .FirstOrDefaultAsync(r => r.Rfiid == id);
            
            if (rfi == null)
                throw new KeyNotFoundException($"RFI with ID {id} not found");
                
            return new RFIDTO
            {
                RFIId = rfi.Rfiid,
                ProjectId = rfi.ProjectId,
                Number = rfi.Number,
                Subject = rfi.Subject,
                Description = rfi.Description,
                Status = rfi.Status,
                RaiseDate = rfi.RaiseDate,
                TargetAnswerDate = rfi.TargetAnswerDate,
                ActualAnswerDate = rfi.ActualAnswerDate,
                ReferenceFile = rfi.ReferenceFile,
                ClientResponseFile = rfi.ClientResponseFile,
                ClientResponseSummary = rfi.ClientResponseSummary,
                ProjectName = rfi.Project.Name,
                ResponseCount = rfi.RfisResponses.Count
            };
        }

        public async Task<IEnumerable<RFIDTO>> GetRFIsByProjectIdAsync(int projectId)
        {
            // Check if the project exists
            var project = await _context.Projects.FindAsync(projectId);
            if (project == null)
                throw new KeyNotFoundException($"Project with ID {projectId} not found");
                
            return await _context.Rfis
                .Include(r => r.Project)
                .Include(r => r.RfisResponses)
                .Where(r => r.ProjectId == projectId)
                .Select(r => new RFIDTO
                {
                    RFIId = r.Rfiid,
                    ProjectId = r.ProjectId,
                    Number = r.Number,
                    Subject = r.Subject,
                    Description = r.Description,
                    Status = r.Status,
                    RaiseDate = r.RaiseDate,
                    TargetAnswerDate = r.TargetAnswerDate,
                    ActualAnswerDate = r.ActualAnswerDate,
                    ReferenceFile = r.ReferenceFile,
                    ClientResponseFile = r.ClientResponseFile,
                    ClientResponseSummary = r.ClientResponseSummary,
                    ProjectName = r.Project.Name,
                    ResponseCount = r.RfisResponses.Count
                })
                .ToListAsync();
        }

        public async Task<RFIDTO> CreateRFIAsync(RFICreateDTO rfiDto)
        {
            // Check if the project exists
            var project = await _context.Projects.FindAsync(rfiDto.ProjectId);
            if (project == null)
                throw new KeyNotFoundException($"Project with ID {rfiDto.ProjectId} not found");
                
            var rfi = new Rfi
            {
                ProjectId = rfiDto.ProjectId,
                Number = rfiDto.Number,
                Subject = rfiDto.Subject,
                Description = rfiDto.Description,
                Status = rfiDto.Status,
                RaiseDate = rfiDto.RaiseDate,
                TargetAnswerDate = rfiDto.TargetAnswerDate,
                ActualAnswerDate = rfiDto.ActualAnswerDate,
                ReferenceFile = rfiDto.ReferenceFile,
                ClientResponseFile = rfiDto.ClientResponseFile,
                ClientResponseSummary = rfiDto.ClientResponseSummary,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow,
                CreatedPeopleId = _userContextService.GetCurrentUserId(),
                ModifiedPeopleId = _userContextService.GetCurrentUserId()
            };
            
            _context.Rfis.Add(rfi);
            await _context.SaveChangesAsync();
            
            return await GetRFIByIdAsync(rfi.Rfiid);
        }

        public async Task<RFIDTO> UpdateRFIAsync(int id, RFIUpdateDTO rfiDto)
        {
            var rfi = await _context.Rfis.FindAsync(id);
            
            if (rfi == null)
                throw new KeyNotFoundException($"RFI with ID {id} not found");
                
            // Check if the project exists
            var project = await _context.Projects.FindAsync(rfiDto.ProjectId);
            if (project == null)
                throw new KeyNotFoundException($"Project with ID {rfiDto.ProjectId} not found");
                
            rfi.ProjectId = rfiDto.ProjectId;
            rfi.Number = rfiDto.Number;
            rfi.Subject = rfiDto.Subject;
            rfi.Description = rfiDto.Description;
            rfi.Status = rfiDto.Status;
            rfi.RaiseDate = rfiDto.RaiseDate;
            rfi.TargetAnswerDate = rfiDto.TargetAnswerDate;
            rfi.ActualAnswerDate = rfiDto.ActualAnswerDate;
            rfi.ReferenceFile = rfiDto.ReferenceFile;
            rfi.ClientResponseFile = rfiDto.ClientResponseFile;
            rfi.ClientResponseSummary = rfiDto.ClientResponseSummary;
            rfi.ModifiedDate = DateTime.UtcNow;
            rfi.ModifiedPeopleId = _userContextService.GetCurrentUserId();
            
            await _context.SaveChangesAsync();
            
            return await GetRFIByIdAsync(id);
        }

        public async Task<bool> DeleteRFIAsync(int id)
        {
            var rfi = await _context.Rfis
                .Include(r => r.RfisResponses)
                .FirstOrDefaultAsync(r => r.Rfiid == id);
            
            if (rfi == null)
                return false;
                
            // Check if there are any RFI responses
            if (rfi.RfisResponses.Any())
            {
                throw new InvalidOperationException("Cannot delete RFI that has responses. Delete the responses first.");
            }
            
            _context.Rfis.Remove(rfi);
            await _context.SaveChangesAsync();
            
            return true;
        }

        // RFI Response methods
        public async Task<IEnumerable<RFIResponseDTO>> GetRFIResponsesByRFIIdAsync(int rfiId)
        {
            // Check if the RFI exists
            var rfi = await _context.Rfis.FindAsync(rfiId);
            if (rfi == null)
                throw new KeyNotFoundException($"RFI with ID {rfiId} not found");
                
            return await _context.RfisResponses
                .Where(r => r.Rfiid == rfiId)
                .Select(r => new RFIResponseDTO
                {
                    ResponseId = r.ResponseId,
                    RFIId = r.Rfiid,
                    RFINumber = r.Rfinumber,
                    ResponseNumber = r.ResponseNumber,
                    ResponseFrom = r.ResponseFrom,
                    ResponseMessage = r.ResponseMessage,
                    ResponseDate = r.ResponseDate,
                    ResponseFolderPath = r.ResponseFolderPath,
                    CreatedDate = r.CreatedDate,
                    CreatedBy = r.CreatedBy,
                    ModifiedDate = r.ModifiedDate,
                    ModifiedBy = r.ModifiedBy
                })
                .ToListAsync();
        }

        public async Task<RFIResponseDTO> GetRFIResponseByIdAsync(int id)
        {
            var rfiResponse = await _context.RfisResponses
                .FirstOrDefaultAsync(r => r.ResponseId == id);
            
            if (rfiResponse == null)
                throw new KeyNotFoundException($"RFI Response with ID {id} not found");
                
            return new RFIResponseDTO
            {
                ResponseId = rfiResponse.ResponseId,
                RFIId = rfiResponse.Rfiid,
                RFINumber = rfiResponse.Rfinumber,
                ResponseNumber = rfiResponse.ResponseNumber,
                ResponseFrom = rfiResponse.ResponseFrom,
                ResponseMessage = rfiResponse.ResponseMessage,
                ResponseDate = rfiResponse.ResponseDate,
                ResponseFolderPath = rfiResponse.ResponseFolderPath,
                CreatedDate = rfiResponse.CreatedDate,
                CreatedBy = rfiResponse.CreatedBy,
                ModifiedDate = rfiResponse.ModifiedDate,
                ModifiedBy = rfiResponse.ModifiedBy
            };
        }

        public async Task<RFIResponseDTO> CreateRFIResponseAsync(RFIResponseCreateDTO rfiResponseDto)
        {
            // Check if the RFI exists
            var rfi = await _context.Rfis.FindAsync(rfiResponseDto.RFIId);
            if (rfi == null)
                throw new KeyNotFoundException($"RFI with ID {rfiResponseDto.RFIId} not found");
                
            var currentUser = _userContextService.GetCurrentUserName();
            
            var rfiResponse = new RfisResponse
            {
                Rfiid = rfiResponseDto.RFIId,
                Rfinumber = rfiResponseDto.RFINumber,
                ResponseNumber = rfiResponseDto.ResponseNumber,
                ResponseFrom = rfiResponseDto.ResponseFrom,
                ResponseMessage = rfiResponseDto.ResponseMessage,
                ResponseDate = rfiResponseDto.ResponseDate,
                ResponseFolderPath = rfiResponseDto.ResponseFolderPath,
                CreatedDate = DateTime.UtcNow,
                CreatedBy = currentUser,
                ModifiedDate = DateTime.UtcNow,
                ModifiedBy = currentUser
            };
            
            _context.RfisResponses.Add(rfiResponse);
            await _context.SaveChangesAsync();
            
            return await GetRFIResponseByIdAsync(rfiResponse.ResponseId);
        }

        public async Task<RFIResponseDTO> UpdateRFIResponseAsync(int id, RFIResponseUpdateDTO rfiResponseDto)
        {
            var rfiResponse = await _context.RfisResponses.FindAsync(id);
            
            if (rfiResponse == null)
                throw new KeyNotFoundException($"RFI Response with ID {id} not found");
                
            var currentUser = _userContextService.GetCurrentUserName();
                
            rfiResponse.Rfinumber = rfiResponseDto.RFINumber;
            rfiResponse.ResponseNumber = rfiResponseDto.ResponseNumber;
            rfiResponse.ResponseFrom = rfiResponseDto.ResponseFrom;
            rfiResponse.ResponseMessage = rfiResponseDto.ResponseMessage;
            rfiResponse.ResponseDate = rfiResponseDto.ResponseDate;
            rfiResponse.ResponseFolderPath = rfiResponseDto.ResponseFolderPath;
            rfiResponse.ModifiedDate = DateTime.UtcNow;
            rfiResponse.ModifiedBy = currentUser;
            
            await _context.SaveChangesAsync();
            
            return await GetRFIResponseByIdAsync(id);
        }

        public async Task<bool> DeleteRFIResponseAsync(int id)
        {
            var rfiResponse = await _context.RfisResponses.FindAsync(id);
            
            if (rfiResponse == null)
                return false;
                
            _context.RfisResponses.Remove(rfiResponse);
            await _context.SaveChangesAsync();
            
            return true;
        }
    }
}
