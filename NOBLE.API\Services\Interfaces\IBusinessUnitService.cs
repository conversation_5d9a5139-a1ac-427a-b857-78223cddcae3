using NOBLE.API.Models.DTOs;

namespace NOBLE.API.Services.Interfaces
{
    public interface IBusinessUnitService
    {
        Task<IEnumerable<BusinessUnitDTO>> GetAllBusinessUnitsAsync();
        Task<BusinessUnitDTO> GetBusinessUnitByIdAsync(int id);
        Task<BusinessUnitDTO> CreateBusinessUnitAsync(BusinessUnitCreateDTO businessUnitDto);
        Task<BusinessUnitDTO> UpdateBusinessUnitAsync(int id, BusinessUnitUpdateDTO businessUnitDto);
        Task<bool> DeleteBusinessUnitAsync(int id);
    }
}
