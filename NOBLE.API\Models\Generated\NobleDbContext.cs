﻿using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;

namespace NOBLE.API.Models.Generated;

public partial class NobleDbContext : DbContext
{
    public NobleDbContext(DbContextOptions<NobleDbContext> options)
        : base(options)
    {
    }

    public virtual DbSet<Budget> Budgets { get; set; }

    public virtual DbSet<BudgetsTrade> BudgetsTrades { get; set; }

    public virtual DbSet<BusinessUnit> BusinessUnits { get; set; }

    public virtual DbSet<Claim> Claims { get; set; }

    public virtual DbSet<ClientVariation> ClientVariations { get; set; }

    public virtual DbSet<ClientVariationsDetail> ClientVariationsDetails { get; set; }

    public virtual DbSet<ClientVariationsTrade> ClientVariationsTrades { get; set; }

    public virtual DbSet<ClientVariationsTradesTrade> ClientVariationsTradesTrades { get; set; }

    public virtual DbSet<Contract> Contracts { get; set; }

    public virtual DbSet<Eot> Eots { get; set; }

    public virtual DbSet<Holiday> Holidays { get; set; }

    public virtual DbSet<InvitationTemplate> InvitationTemplates { get; set; }

    public virtual DbSet<JobType> JobTypes { get; set; }

    public virtual DbSet<Person> People { get; set; }

    public virtual DbSet<Process> Processes { get; set; }

    public virtual DbSet<ProcessStep> ProcessSteps { get; set; }

    public virtual DbSet<Project> Projects { get; set; }

    public virtual DbSet<Rfi> Rfis { get; set; }

    public virtual DbSet<RfisResponse> RfisResponses { get; set; }

    public virtual DbSet<RfisResponseAttachment> RfisResponseAttachments { get; set; }

    public virtual DbSet<SubContractor> SubContractors { get; set; }

    public virtual DbSet<Trade> Trades { get; set; }

    public virtual DbSet<TradeItem> TradeItems { get; set; }

    public virtual DbSet<TradeItemCategory> TradeItemCategories { get; set; }

    public virtual DbSet<TradeParticipation> TradeParticipations { get; set; }

    public virtual DbSet<UserType> UserTypes { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Budget>(entity =>
        {
            entity.Property(e => e.Amount).HasColumnType("money");
            entity.Property(e => e.Code)
                .HasMaxLength(25)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.ModifiedDate).HasColumnType("smalldatetime");

            entity.HasOne(d => d.Project).WithMany(p => p.Budgets)
                .HasForeignKey(d => d.ProjectId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Budgets_Projects");
        });

        modelBuilder.Entity<BudgetsTrade>(entity =>
        {
            entity.HasKey(e => new { e.BudgetId, e.TradeId }).HasName("PK_BudgetsTrades_1");

            entity.Property(e => e.BudgetAmount).HasColumnType("money");
            entity.Property(e => e.BudgetAmountAllowance).HasColumnType("money");
            entity.Property(e => e.BudgetAmountInitial).HasColumnType("money");
            entity.Property(e => e.BudgetAmountTradeInitial).HasColumnType("money");
            entity.Property(e => e.BudgetDate).HasColumnType("smalldatetime");
            entity.Property(e => e.CreatedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.ModifiedDate).HasColumnType("smalldatetime");

            entity.HasOne(d => d.Budget).WithMany(p => p.BudgetsTrades)
                .HasForeignKey(d => d.BudgetId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_BudgetsTrades_Budgets");

            entity.HasOne(d => d.Trade).WithMany(p => p.BudgetsTrades)
                .HasForeignKey(d => d.TradeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_BudgetsTrades_Trades");
        });

        modelBuilder.Entity<BusinessUnit>(entity =>
        {
            entity.Property(e => e.ClaimSpecialNote)
                .HasMaxLength(1000)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.EdpeopleId).HasColumnName("EDPeopleId");
            entity.Property(e => e.ModifiedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.Name)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.ProjectNumberFormat)
                .HasMaxLength(10)
                .IsUnicode(false);
            entity.Property(e => e.TradeAmountApproval).HasColumnType("money");
            entity.Property(e => e.TradeComAmountApproval).HasColumnType("money");
            entity.Property(e => e.TradeDaamountApproval)
                .HasColumnType("money")
                .HasColumnName("TradeDAAmountApproval");
            entity.Property(e => e.TradeOverbudgetApproval).HasColumnType("money");
            entity.Property(e => e.TradeUmoverbudgetApproval)
                .HasColumnType("money")
                .HasColumnName("TradeUMOverbudgetApproval");
            entity.Property(e => e.UmpeopleId).HasColumnName("UMPeopleId");
            entity.Property(e => e.VariationSepAccUmapproval)
                .HasColumnType("money")
                .HasColumnName("VariationSepAccUMApproval");
            entity.Property(e => e.VariationUmboqvcvdvapproval)
                .HasColumnType("money")
                .HasColumnName("VariationUMBOQVCVDVApproval");
            entity.Property(e => e.VariationUmdaoverAmtApproval)
                .HasColumnType("money")
                .HasColumnName("VariationUMDAOverAmtApproval");
        });

        modelBuilder.Entity<Claim>(entity =>
        {
            entity.Property(e => e.AdjustmentNoteAmount).HasColumnType("money");
            entity.Property(e => e.AdjustmentNoteName)
                .HasMaxLength(25)
                .IsUnicode(false);
            entity.Property(e => e.ApprovalDate).HasColumnType("smalldatetime");
            entity.Property(e => e.BackupFile1)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.BackupFile2)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.ClientDueDate).HasColumnType("smalldatetime");
            entity.Property(e => e.CreatedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.DraftApprovalDate).HasColumnType("smalldatetime");
            entity.Property(e => e.DueDate).HasColumnType("smalldatetime");
            entity.Property(e => e.GoodsServicesTax).HasColumnType("money");
            entity.Property(e => e.InternalApprovalDate).HasColumnType("smalldatetime");
            entity.Property(e => e.ModifiedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.WriteDate).HasColumnType("smalldatetime");

            entity.HasOne(d => d.Project).WithMany(p => p.Claims)
                .HasForeignKey(d => d.ProjectId)
                .HasConstraintName("FK_Claims_Projects");
        });

        modelBuilder.Entity<ClientVariation>(entity =>
        {
            entity.Property(e => e.ApprovalDate).HasColumnType("smalldatetime");
            entity.Property(e => e.BackupFile)
                .HasMaxLength(250)
                .IsUnicode(false);
            entity.Property(e => e.CancelDate).HasColumnType("smalldatetime");
            entity.Property(e => e.ClientApprovalFile)
                .HasMaxLength(250)
                .IsUnicode(false);
            entity.Property(e => e.Comments)
                .HasMaxLength(1000)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.GoodsServicesTax).HasColumnType("money");
            entity.Property(e => e.InternalApprovalDate).HasColumnType("smalldatetime");
            entity.Property(e => e.InvoiceDate).HasColumnType("smalldatetime");
            entity.Property(e => e.InvoiceDueDate).HasColumnType("smalldatetime");
            entity.Property(e => e.InvoicePaidDate).HasColumnType("smalldatetime");
            entity.Property(e => e.InvoiceSentDate).HasColumnType("smalldatetime");
            entity.Property(e => e.ModifiedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.Name)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.QuotesFile)
                .HasMaxLength(250)
                .IsUnicode(false);
            entity.Property(e => e.Type)
                .HasMaxLength(3)
                .IsUnicode(false);
            entity.Property(e => e.VerbalApprovalDate).HasColumnType("smalldatetime");
            entity.Property(e => e.WorksCompletedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.WriteDate).HasColumnType("smalldatetime");

            entity.HasOne(d => d.ParentClientVariation).WithMany(p => p.InverseParentClientVariation)
                .HasForeignKey(d => d.ParentClientVariationId)
                .HasConstraintName("FK_ClientVariations_ClientVariations");

            entity.HasOne(d => d.Project).WithMany(p => p.ClientVariations)
                .HasForeignKey(d => d.ProjectId)
                .HasConstraintName("FK_ClientVariations_Projects");
        });

        modelBuilder.Entity<ClientVariationsDetail>(entity =>
        {
            entity.HasKey(e => e.ClientVariationDetailId);

            entity.Property(e => e.Amount).HasColumnType("money");
            entity.Property(e => e.CreatedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.Description).HasColumnType("text");
            entity.Property(e => e.ModifiedDate).HasColumnType("smalldatetime");

            entity.HasOne(d => d.ClientVariation).WithMany(p => p.ClientVariationsDetails)
                .HasForeignKey(d => d.ClientVariationId)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_ClientVariationsDetails_ClientVariations");
        });

        modelBuilder.Entity<ClientVariationsTrade>(entity =>
        {
            entity.HasKey(e => e.ClientVariationTradeId);

            entity.Property(e => e.Amount).HasColumnType("money");
            entity.Property(e => e.CreatedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.ModifiedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.TradeCode)
                .HasMaxLength(25)
                .IsUnicode(false);

            entity.HasOne(d => d.ClientVariation).WithMany(p => p.ClientVariationsTrades)
                .HasForeignKey(d => d.ClientVariationId)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_ClientVariationsTrades_ClientVariations");
        });

        modelBuilder.Entity<ClientVariationsTradesTrade>(entity =>
        {
            entity.HasKey(e => new { e.ClientVariationTradeId, e.TradeId });

            entity.Property(e => e.BudgetAmount).HasColumnType("money");
            entity.Property(e => e.BudgetAmountAllowance).HasColumnType("money");
            entity.Property(e => e.BudgetAmountInitial).HasColumnType("money");
            entity.Property(e => e.BudgetAmountTradeInitial).HasColumnType("money");
            entity.Property(e => e.BudgetDate).HasColumnType("smalldatetime");
            entity.Property(e => e.CreatedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.ModifiedDate).HasColumnType("smalldatetime");
        });

        modelBuilder.Entity<Contract>(entity =>
        {
            entity.Property(e => e.ApprovalDate).HasColumnType("smalldatetime");
            entity.Property(e => e.Comments)
                .HasMaxLength(1000)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.Description)
                .HasMaxLength(1000)
                .IsUnicode(false);
            entity.Property(e => e.GoodsServicesTax).HasColumnType("money");
            entity.Property(e => e.ModifiedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.Number)
                .HasMaxLength(25)
                .IsUnicode(false);
            entity.Property(e => e.QuotesFile)
                .HasMaxLength(250)
                .IsUnicode(false);
            entity.Property(e => e.SiteInstruction)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.SiteInstructionDate).HasColumnType("smalldatetime");
            entity.Property(e => e.SubcontractorReference)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.SubcontractorReferenceDate).HasColumnType("smalldatetime");
            entity.Property(e => e.Template).HasColumnType("text");
            entity.Property(e => e.WriteDate).HasColumnType("smalldatetime");

            entity.HasOne(d => d.ParentContract).WithMany(p => p.InverseParentContract)
                .HasForeignKey(d => d.ParentContractId)
                .HasConstraintName("FK_Contracts_Contracts");

            entity.HasOne(d => d.Trade).WithMany(p => p.Contracts)
                .HasForeignKey(d => d.TradeId)
                .HasConstraintName("FK_Contracts_Trades");
        });

        modelBuilder.Entity<Eot>(entity =>
        {
            entity.ToTable("EOTs");

            entity.Property(e => e.Eotid).HasColumnName("EOTId");
            entity.Property(e => e.ApprovalDate).HasColumnType("smalldatetime");
            entity.Property(e => e.Cause)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.ClientApprovalFile)
                .HasMaxLength(250)
                .IsUnicode(false);
            entity.Property(e => e.ClientBackUpFile)
                .HasMaxLength(250)
                .IsUnicode(false);
            entity.Property(e => e.CostCode)
                .HasMaxLength(25)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.EndDate).HasColumnType("smalldatetime");
            entity.Property(e => e.EotType)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.FirstNoticeDate).HasColumnType("smalldatetime");
            entity.Property(e => e.ModifiedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.Nature).IsUnicode(false);
            entity.Property(e => e.Period).IsUnicode(false);
            entity.Property(e => e.SendDate).HasColumnType("smalldatetime");
            entity.Property(e => e.StartDate).HasColumnType("smalldatetime");
            entity.Property(e => e.Status)
                .HasMaxLength(1)
                .IsUnicode(false)
                .IsFixedLength();
            entity.Property(e => e.Works).IsUnicode(false);
            entity.Property(e => e.WriteDate).HasColumnType("smalldatetime");

            entity.HasOne(d => d.Project).WithMany(p => p.Eots)
                .HasForeignKey(d => d.ProjectId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_EOTs_Projects");
        });

        modelBuilder.Entity<Holiday>(entity =>
        {
            entity.HasKey(e => e.HolidayDate);

            entity.Property(e => e.HolidayDate).HasColumnType("smalldatetime");
        });

        modelBuilder.Entity<InvitationTemplate>(entity =>
        {
            entity.Property(e => e.CreatedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.ModifiedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.Template).HasColumnType("text");
        });

        modelBuilder.Entity<JobType>(entity =>
        {
            entity.Property(e => e.CreatedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.ModifiedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.Name)
                .HasMaxLength(100)
                .IsUnicode(false);
        });

        modelBuilder.Entity<Person>(entity =>
        {
            entity.HasKey(e => e.PeopleId);

            entity.Property(e => e.City)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.Dob)
                .HasColumnType("smalldatetime")
                .HasColumnName("DOB");
            entity.Property(e => e.Email)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.EmergencyContactName)
                .HasMaxLength(150)
                .IsUnicode(false);
            entity.Property(e => e.EmergencyContactNumber)
                .HasMaxLength(25)
                .IsUnicode(false);
            entity.Property(e => e.EmployeePosition)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.FirstName)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.LastName)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.Mobile)
                .HasMaxLength(25)
                .IsUnicode(false);
            entity.Property(e => e.ModifiedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.Phone)
                .HasMaxLength(25)
                .IsUnicode(false);
            entity.Property(e => e.PostCode)
                .HasMaxLength(10)
                .IsUnicode(false);
            entity.Property(e => e.State)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.Street)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.Type)
                .HasMaxLength(2)
                .IsUnicode(false)
                .IsFixedLength();
            entity.Property(e => e.UserLastLogin).HasColumnType("smalldatetime");
            entity.Property(e => e.UserLogin)
                .HasMaxLength(25)
                .IsUnicode(false);
            entity.Property(e => e.UserPassword)
                .HasMaxLength(25)
                .IsUnicode(false);
            entity.Property(e => e.UserSignatureFile)
                .HasMaxLength(250)
                .IsUnicode(false);
            entity.Property(e => e.UserType)
                .HasMaxLength(2)
                .IsUnicode(false)
                .IsFixedLength();

            entity.HasOne(d => d.BusinessUnit).WithMany(p => p.People)
                .HasForeignKey(d => d.BusinessUnitId)
                .HasConstraintName("FK_People_BusinessUnits");
        });

        modelBuilder.Entity<Process>(entity =>
        {
            entity.Property(e => e.CreatedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.ModifiedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.Name)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.StepComparisonApproval)
                .HasMaxLength(5)
                .IsUnicode(false)
                .IsFixedLength();
            entity.Property(e => e.StepContractApproval)
                .HasMaxLength(5)
                .IsUnicode(false)
                .IsFixedLength();
            entity.Property(e => e.TemplateType)
                .HasMaxLength(1)
                .IsUnicode(false)
                .IsFixedLength();
        });

        modelBuilder.Entity<ProcessStep>(entity =>
        {
            entity.Property(e => e.ActualDate).HasColumnType("smalldatetime");
            entity.Property(e => e.Comments)
                .HasMaxLength(1000)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.ModifiedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.Name)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.TargetDate).HasColumnType("smalldatetime");
            entity.Property(e => e.Type)
                .HasMaxLength(5)
                .IsUnicode(false)
                .IsFixedLength();
            entity.Property(e => e.UserType)
                .HasMaxLength(2)
                .IsUnicode(false)
                .IsFixedLength();

            entity.HasOne(d => d.Process).WithMany(p => p.ProcessSteps)
                .HasForeignKey(d => d.ProcessId)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_ProcessSteps_Processes");
        });

        modelBuilder.Entity<Project>(entity =>
        {
            entity.Property(e => e.AccountName)
                .HasMaxLength(250)
                .IsUnicode(false);
            entity.Property(e => e.AccountNumber)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.AttachmentsFolder)
                .HasMaxLength(250)
                .IsUnicode(false);
            entity.Property(e => e.Bsb)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("BSB");
            entity.Property(e => e.City)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.ClaimFrequency)
                .HasMaxLength(1)
                .IsUnicode(false)
                .IsFixedLength();
            entity.Property(e => e.CommencementDate).HasColumnType("smalldatetime");
            entity.Property(e => e.CompletionDate).HasColumnType("smalldatetime");
            entity.Property(e => e.ContractAmount).HasColumnType("money");
            entity.Property(e => e.CreatedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.Description)
                .HasMaxLength(1000)
                .IsUnicode(false);
            entity.Property(e => e.DistributionListInfo)
                .HasMaxLength(5)
                .IsUnicode(false)
                .IsFixedLength();
            entity.Property(e => e.FirstClaimDueDate).HasColumnType("smalldatetime");
            entity.Property(e => e.Interest)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.LawOfSubcontract).HasColumnType("text");
            entity.Property(e => e.LiquidatedDamages)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.ModifiedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.Name)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.Number)
                .HasMaxLength(25)
                .IsUnicode(false);
            entity.Property(e => e.PaymentTerms)
                .HasMaxLength(1)
                .IsUnicode(false)
                .IsFixedLength();
            entity.Property(e => e.PostalCode)
                .HasMaxLength(10)
                .IsUnicode(false);
            entity.Property(e => e.PracticalCompletionDate).HasColumnType("smalldatetime");
            entity.Property(e => e.Principal)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.PrincipalAbn)
                .HasMaxLength(25)
                .IsUnicode(false)
                .HasColumnName("PrincipalABN");
            entity.Property(e => e.ProjectStatus)
                .HasMaxLength(2)
                .IsUnicode(false)
                .IsFixedLength();
            entity.Property(e => e.Retention)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.RetentionToCertification)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.RetentionToDlp)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("RetentionToDLP");
            entity.Property(e => e.SiteAllowances)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.SitePostalCode)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.SiteState)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.SiteSuburb)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.Siteaddress)
                .HasMaxLength(150)
                .IsUnicode(false);
            entity.Property(e => e.SpecialClause).HasColumnType("text");
            entity.Property(e => e.State)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.Street)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.Waranty1Amount).HasColumnType("money");
            entity.Property(e => e.Waranty1Date).HasColumnType("smalldatetime");
            entity.Property(e => e.Waranty2Amount).HasColumnType("money");
            entity.Property(e => e.Waranty2Date).HasColumnType("smalldatetime");
            entity.Property(e => e.Year)
                .HasMaxLength(4)
                .IsUnicode(false);

            entity.HasOne(d => d.BusinessUnit).WithMany(p => p.Projects)
                .HasForeignKey(d => d.BusinessUnitId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Projects_BusinessUnits");
        });

        modelBuilder.Entity<Rfi>(entity =>
        {
            entity.ToTable("RFIs");

            entity.Property(e => e.Rfiid).HasColumnName("RFIId");
            entity.Property(e => e.ActualAnswerDate).HasColumnType("smalldatetime");
            entity.Property(e => e.ClientResponseFile)
                .HasMaxLength(250)
                .IsUnicode(false);
            entity.Property(e => e.ClientResponseSummary)
                .HasMaxLength(1024)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.Description).IsUnicode(false);
            entity.Property(e => e.ModifiedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.RaiseDate).HasColumnType("smalldatetime");
            entity.Property(e => e.ReferenceFile)
                .HasMaxLength(250)
                .IsUnicode(false);
            entity.Property(e => e.Status)
                .HasMaxLength(1)
                .IsUnicode(false)
                .IsFixedLength();
            entity.Property(e => e.Subject)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.TargetAnswerDate).HasColumnType("smalldatetime");

            entity.HasOne(d => d.Project).WithMany(p => p.Rfis)
                .HasForeignKey(d => d.ProjectId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RFIs_Projects");
        });

        modelBuilder.Entity<RfisResponse>(entity =>
        {
            entity.HasKey(e => e.ResponseId);

            entity.ToTable("RFIsResponse");

            entity.Property(e => e.CreatedBy)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.ModifiedBy)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.ModifiedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.ResponseDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.ResponseFolderPath)
                .HasMaxLength(150)
                .IsUnicode(false);
            entity.Property(e => e.ResponseFrom)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.ResponseMessage).IsUnicode(false);
            entity.Property(e => e.Rfiid).HasColumnName("RFIId");
            entity.Property(e => e.Rfinumber).HasColumnName("RFINumber");

            entity.HasOne(d => d.Rfi).WithMany(p => p.RfisResponses)
                .HasForeignKey(d => d.Rfiid)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RFIsResponse_RFIs");
        });

        modelBuilder.Entity<RfisResponseAttachment>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("RFIsResponseAttachment");

            entity.Property(e => e.CreatedBy)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.CreatedOn)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.FileName)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.FileType)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.Id).ValueGeneratedOnAdd();
            entity.Property(e => e.ModefiedBy)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.ModefiedOn).HasColumnType("datetime");
            entity.Property(e => e.Rfiid).HasColumnName("RFIId");
        });

        modelBuilder.Entity<SubContractor>(entity =>
        {
            entity.Property(e => e.Abn)
                .HasMaxLength(25)
                .IsUnicode(false)
                .HasColumnName("ABN");
            entity.Property(e => e.Account)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.Acn)
                .HasMaxLength(25)
                .IsUnicode(false)
                .HasColumnName("ACN");
            entity.Property(e => e.Comments)
                .HasMaxLength(1000)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.Dccontractor).HasColumnName("DCContractor");
            entity.Property(e => e.Fax)
                .HasMaxLength(25)
                .IsUnicode(false);
            entity.Property(e => e.LicenceNumber)
                .HasMaxLength(25)
                .IsUnicode(false);
            entity.Property(e => e.Locality)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.ModifiedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.Name)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.Phone)
                .HasMaxLength(25)
                .IsUnicode(false);
            entity.Property(e => e.PostalCode)
                .HasMaxLength(10)
                .IsUnicode(false);
            entity.Property(e => e.PrequalifiedForm)
                .HasMaxLength(150)
                .IsUnicode(false);
            entity.Property(e => e.ProfessionalIndemnityInsurance)
                .HasMaxLength(150)
                .IsUnicode(false);
            entity.Property(e => e.PublicLiabilityInsurance)
                .HasMaxLength(150)
                .IsUnicode(false);
            entity.Property(e => e.ShortName)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.State)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.Street)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.Website)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.WorkCoverInsurance)
                .HasMaxLength(150)
                .IsUnicode(false);

            entity.HasOne(d => d.BusinessUnit).WithMany(p => p.SubContractors)
                .HasForeignKey(d => d.BusinessUnitId)
                .HasConstraintName("FK_SubContractors_BusinessUnits");
        });

        modelBuilder.Entity<Trade>(entity =>
        {
            entity.Property(e => e.CapeopleId).HasColumnName("CAPeopleId");
            entity.Property(e => e.Code)
                .HasMaxLength(25)
                .IsUnicode(false);
            entity.Property(e => e.CommencementDate).HasColumnType("smalldatetime");
            entity.Property(e => e.ComparisonApprovalAmount).HasColumnType("money");
            entity.Property(e => e.ComparisonApprovalDate).HasColumnType("smalldatetime");
            entity.Property(e => e.ComparisonDueDate).HasColumnType("smalldatetime");
            entity.Property(e => e.CompletionDate).HasColumnType("smalldatetime");
            entity.Property(e => e.ContractDueDate).HasColumnType("smalldatetime");
            entity.Property(e => e.CreatedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.DaysFromPcd).HasColumnName("DaysFromPCD");
            entity.Property(e => e.Description)
                .HasMaxLength(1000)
                .IsUnicode(false);
            entity.Property(e => e.DueDate).HasColumnType("smalldatetime");
            entity.Property(e => e.InvitationDate).HasColumnType("smalldatetime");
            entity.Property(e => e.ModifiedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.Name)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.PmpeopleId).HasColumnName("PMPeopleId");
            entity.Property(e => e.PrelettingFile)
                .HasMaxLength(250)
                .IsUnicode(false);
            entity.Property(e => e.QuotesFile)
                .HasMaxLength(250)
                .IsUnicode(false);
            entity.Property(e => e.ScopeFooter).HasColumnType("text");
            entity.Property(e => e.ScopeHeader).HasColumnType("text");
            entity.Property(e => e.SignedContractFile)
                .HasMaxLength(250)
                .IsUnicode(false);
            entity.Property(e => e.WorkOrderNumber)
                .HasMaxLength(25)
                .IsUnicode(false);

            entity.HasOne(d => d.Capeople).WithMany(p => p.TradeCapeople)
                .HasForeignKey(d => d.CapeopleId)
                .HasConstraintName("FK_Trades_People");

            entity.HasOne(d => d.Contract).WithMany(p => p.Trades)
                .HasForeignKey(d => d.ContractId)
                .HasConstraintName("FK_Trades_Contracts");

            entity.HasOne(d => d.JobType).WithMany(p => p.Trades)
                .HasForeignKey(d => d.JobTypeId)
                .HasConstraintName("FK_Trades_JobTypes");

            entity.HasOne(d => d.Pmpeople).WithMany(p => p.TradePmpeople)
                .HasForeignKey(d => d.PmpeopleId)
                .HasConstraintName("FK_Trades_People1");

            entity.HasOne(d => d.Project).WithMany(p => p.Trades)
                .HasForeignKey(d => d.ProjectId)
                .HasConstraintName("FK_Trades_Projects");
        });

        modelBuilder.Entity<TradeItem>(entity =>
        {
            entity.Property(e => e.CreatedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.ModifiedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.Name)
                .HasMaxLength(250)
                .IsUnicode(false);
            entity.Property(e => e.ScopeOfWorks)
                .HasMaxLength(1000)
                .IsUnicode(false);
            entity.Property(e => e.Units)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.HasOne(d => d.TradeItemCategory).WithMany(p => p.TradeItems)
                .HasForeignKey(d => d.TradeItemCategoryId)
                .HasConstraintName("FK_TradeItems_TradeItemCategories");
        });

        modelBuilder.Entity<TradeItemCategory>(entity =>
        {
            entity.Property(e => e.CreatedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.LongDescription)
                .HasMaxLength(1000)
                .IsUnicode(false);
            entity.Property(e => e.ModifiedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.Name)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.ShortDescription)
                .HasMaxLength(100)
                .IsUnicode(false);

            entity.HasOne(d => d.Trade).WithMany(p => p.TradeItemCategories)
                .HasForeignKey(d => d.TradeId)
                .HasConstraintName("FK_TradeItemCategories_Trades");
        });

        modelBuilder.Entity<TradeParticipation>(entity =>
        {
            entity.Property(e => e.Amount).HasColumnType("money");
            entity.Property(e => e.Comments)
                .HasMaxLength(1000)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.InternalComments).IsUnicode(false);
            entity.Property(e => e.InvitationDate).HasColumnType("smalldatetime");
            entity.Property(e => e.ModifiedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.OpenDate).HasColumnType("smalldatetime");
            entity.Property(e => e.PaymentTerms)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasDefaultValue("30");
            entity.Property(e => e.QuoteDate).HasColumnType("smalldatetime");
            entity.Property(e => e.QuoteDueDate).HasColumnType("smalldatetime");
            entity.Property(e => e.QuoteFile)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.ReminderDate).HasColumnType("smalldatetime");
            entity.Property(e => e.SafetyRating)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.Status)
                .HasMaxLength(2)
                .IsUnicode(false)
                .IsFixedLength();
            entity.Property(e => e.TradeParticipationType)
                .HasMaxLength(1)
                .IsUnicode(false)
                .IsFixedLength();

            entity.HasOne(d => d.ComparisonTradeParticipation).WithMany(p => p.InverseComparisonTradeParticipation)
                .HasForeignKey(d => d.ComparisonTradeParticipationId)
                .HasConstraintName("FK_TradeParticipations_TradeParticipations");

            entity.HasOne(d => d.ContactPeople).WithMany(p => p.TradeParticipations)
                .HasForeignKey(d => d.ContactPeopleId)
                .HasConstraintName("FK_TradeParticipations_People");

            entity.HasOne(d => d.Trade).WithMany(p => p.TradeParticipations)
                .HasForeignKey(d => d.TradeId)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_TradeParticipations_Trades");
        });

        modelBuilder.Entity<UserType>(entity =>
        {
            entity.Property(e => e.CreatedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.Description)
                .HasMaxLength(25)
                .IsUnicode(false);
            entity.Property(e => e.ModifiedDate).HasColumnType("smalldatetime");
            entity.Property(e => e.Name)
                .HasMaxLength(2)
                .IsUnicode(false);
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
