using Microsoft.EntityFrameworkCore;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Models.Generated;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Services.Implementations
{
    public class ClientVariationsDetailService : IClientVariationsDetailService
    {
        private readonly NobleDbContext _context;
        private readonly IUserContextService _userContextService;

        public ClientVariationsDetailService(NobleDbContext context, IUserContextService userContextService)
        {
            _context = context;
            _userContextService = userContextService;
        }

        public async Task<IEnumerable<ClientVariationsDetailDTO>> GetAllClientVariationsDetailsAsync()
        {
            return await _context.ClientVariationsDetails
                .Include(cvd => cvd.ClientVariation)
                .Include(cvd => cvd.ClientVariation.Project)
                .Select(cvd => new ClientVariationsDetailDTO
                {
                    ClientVariationDetailId = cvd.ClientVariationDetailId,
                    ClientVariationId = cvd.ClientVariationId,
                    Amount = cvd.Amount,
                    Description = cvd.Description,
                    DisplayOrder = cvd.DisplayOrder,
                    ClientVariationName = cvd.ClientVariation.Name,
                    ProjectName = cvd.ClientVariation.Project.Name
                })
                .ToListAsync();
        }

        public async Task<ClientVariationsDetailDTO> GetClientVariationsDetailByIdAsync(int id)
        {
            var clientVariationsDetail = await _context.ClientVariationsDetails
                .Include(cvd => cvd.ClientVariation)
                .Include(cvd => cvd.ClientVariation.Project)
                .FirstOrDefaultAsync(cvd => cvd.ClientVariationDetailId == id);
            
            if (clientVariationsDetail == null)
                throw new KeyNotFoundException($"ClientVariationsDetail with ID {id} not found");
                
            return new ClientVariationsDetailDTO
            {
                ClientVariationDetailId = clientVariationsDetail.ClientVariationDetailId,
                ClientVariationId = clientVariationsDetail.ClientVariationId,
                Amount = clientVariationsDetail.Amount,
                Description = clientVariationsDetail.Description,
                DisplayOrder = clientVariationsDetail.DisplayOrder,
                ClientVariationName = clientVariationsDetail.ClientVariation.Name,
                ProjectName = clientVariationsDetail.ClientVariation.Project.Name
            };
        }

        public async Task<IEnumerable<ClientVariationsDetailDTO>> GetClientVariationsDetailsByClientVariationIdAsync(int clientVariationId)
        {
            // Check if the client variation exists
            var clientVariation = await _context.ClientVariations.FindAsync(clientVariationId);
            if (clientVariation == null)
                throw new KeyNotFoundException($"ClientVariation with ID {clientVariationId} not found");
                
            return await _context.ClientVariationsDetails
                .Include(cvd => cvd.ClientVariation)
                .Include(cvd => cvd.ClientVariation.Project)
                .Where(cvd => cvd.ClientVariationId == clientVariationId)
                .Select(cvd => new ClientVariationsDetailDTO
                {
                    ClientVariationDetailId = cvd.ClientVariationDetailId,
                    ClientVariationId = cvd.ClientVariationId,
                    Amount = cvd.Amount,
                    Description = cvd.Description,
                    DisplayOrder = cvd.DisplayOrder,
                    ClientVariationName = cvd.ClientVariation.Name,
                    ProjectName = cvd.ClientVariation.Project.Name
                })
                .ToListAsync();
        }

        public async Task<ClientVariationsDetailDTO> CreateClientVariationsDetailAsync(ClientVariationsDetailCreateDTO clientVariationsDetailDto)
        {
            // Check if the client variation exists
            var clientVariation = await _context.ClientVariations.FindAsync(clientVariationsDetailDto.ClientVariationId);
            if (clientVariation == null)
                throw new KeyNotFoundException($"ClientVariation with ID {clientVariationsDetailDto.ClientVariationId} not found");
                
            var clientVariationsDetail = new ClientVariationsDetail
            {
                ClientVariationId = clientVariationsDetailDto.ClientVariationId,
                Amount = clientVariationsDetailDto.Amount,
                Description = clientVariationsDetailDto.Description,
                DisplayOrder = clientVariationsDetailDto.DisplayOrder,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow,
                CreatedPeopleId = _userContextService.GetCurrentUserId(),
                ModifiedPeopleId = _userContextService.GetCurrentUserId()
            };
            
            _context.ClientVariationsDetails.Add(clientVariationsDetail);
            await _context.SaveChangesAsync();
            
            return await GetClientVariationsDetailByIdAsync(clientVariationsDetail.ClientVariationDetailId);
        }

        public async Task<ClientVariationsDetailDTO> UpdateClientVariationsDetailAsync(int id, ClientVariationsDetailUpdateDTO clientVariationsDetailDto)
        {
            var clientVariationsDetail = await _context.ClientVariationsDetails.FindAsync(id);
            
            if (clientVariationsDetail == null)
                throw new KeyNotFoundException($"ClientVariationsDetail with ID {id} not found");
                
            // Check if the client variation exists
            var clientVariation = await _context.ClientVariations.FindAsync(clientVariationsDetailDto.ClientVariationId);
            if (clientVariation == null)
                throw new KeyNotFoundException($"ClientVariation with ID {clientVariationsDetailDto.ClientVariationId} not found");
                
            clientVariationsDetail.ClientVariationId = clientVariationsDetailDto.ClientVariationId;
            clientVariationsDetail.Amount = clientVariationsDetailDto.Amount;
            clientVariationsDetail.Description = clientVariationsDetailDto.Description;
            clientVariationsDetail.DisplayOrder = clientVariationsDetailDto.DisplayOrder;
            clientVariationsDetail.ModifiedDate = DateTime.UtcNow;
            clientVariationsDetail.ModifiedPeopleId = _userContextService.GetCurrentUserId();
            
            await _context.SaveChangesAsync();
            
            return await GetClientVariationsDetailByIdAsync(id);
        }

        public async Task<bool> DeleteClientVariationsDetailAsync(int id)
        {
            var clientVariationsDetail = await _context.ClientVariationsDetails.FindAsync(id);
            
            if (clientVariationsDetail == null)
                return false;
                
            _context.ClientVariationsDetails.Remove(clientVariationsDetail);
            await _context.SaveChangesAsync();
            
            return true;
        }
    }
}
