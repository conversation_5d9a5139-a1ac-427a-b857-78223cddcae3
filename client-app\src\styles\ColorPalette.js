/**
 * Color Palette for Noble Construction App
 * This file contains all the colors used throughout the application
 */

const ColorPalette = {
  // Primary Colors
  primary: {
    main: '#0056B3', // Main blue color from the Noble logo
    light: '#3378C5',
    dark: '#003F82',
    contrastText: '#FFFFFF',
  },
  
  // Secondary Colors
  secondary: {
    main: '#FF9800', // Orange accent color
    light: '#FFB74D',
    dark: '#F57C00',
    contrastText: '#FFFFFF',
  },
  
  // Neutral Colors
  neutral: {
    white: '#FFFFFF',
    offWhite: '#F5F7FA',
    lightGrey: '#E1E5EB',
    grey: '#8A94A6',
    darkGrey: '#3E4C59',
    black: '#1F2933',
  },
  
  // Status Colors
  status: {
    success: '#4CAF50',
    warning: '#FFC107',
    error: '#F44336',
    info: '#2196F3',
  },
  
  // Background Colors
  background: {
    default: '#F5F7FA',
    paper: '#FFFFFF',
    dark: '#1F2933',
  },
  
  // Text Colors
  text: {
    primary: '#1F2933',
    secondary: '#3E4C59',
    disabled: '#8A94A6',
    hint: '#8A94A6',
    white: '#FFFFFF',
  },
  
  // Border Colors
  border: {
    light: '#E1E5EB',
    main: '#C4CDD5',
    dark: '#8A94A6',
  },
  
  // Gradient Colors
  gradient: {
    primary: 'linear-gradient(135deg, #0056B3 0%, #003F82 100%)',
    secondary: 'linear-gradient(135deg, #FF9800 0%, #F57C00 100%)',
  },
};

export default ColorPalette;
