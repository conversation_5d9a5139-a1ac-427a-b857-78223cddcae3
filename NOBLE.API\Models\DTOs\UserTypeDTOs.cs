using System.ComponentModel.DataAnnotations;

namespace NOBLE.API.Models.DTOs
{
    public class UserTypeDTO
    {
        public int UserTypeId { get; set; }
        public string? Name { get; set; }
        public string? Description { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        
        // Navigation properties for display purposes
        public string? CreatedByName { get; set; }
        public string? ModifiedByName { get; set; }
    }

    public class UserTypeCreateDTO
    {
        [Required]
        [StringLength(50)]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(255)]
        public string? Description { get; set; }
    }

    public class UserTypeUpdateDTO
    {
        [StringLength(50)]
        public string? Name { get; set; }
        
        [StringLength(255)]
        public string? Description { get; set; }
    }
}
