using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class ClientVariationsController : ControllerBase
    {
        private readonly IClientVariationService _clientVariationService;
        
        public ClientVariationsController(IClientVariationService clientVariationService)
        {
            _clientVariationService = clientVariationService;
        }
        
        // GET: api/ClientVariations
        [HttpGet]
        public async Task<ActionResult<IEnumerable<ClientVariationDTO>>> GetClientVariations()
        {
            var clientVariations = await _clientVariationService.GetAllClientVariationsAsync();
            return Ok(clientVariations);
        }
        
        // GET: api/ClientVariations/5
        [HttpGet("{id}")]
        public async Task<ActionResult<ClientVariationDTO>> GetClientVariation(int id)
        {
            try
            {
                var clientVariation = await _clientVariationService.GetClientVariationByIdAsync(id);
                return Ok(clientVariation);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // GET: api/ClientVariations/Project/5
        [HttpGet("Project/{projectId}")]
        public async Task<ActionResult<IEnumerable<ClientVariationDTO>>> GetClientVariationsByProject(int projectId)
        {
            try
            {
                var clientVariations = await _clientVariationService.GetClientVariationsByProjectIdAsync(projectId);
                return Ok(clientVariations);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // GET: api/ClientVariations/Children/5
        [HttpGet("Children/{parentId}")]
        public async Task<ActionResult<IEnumerable<ClientVariationDTO>>> GetChildClientVariations(int parentId)
        {
            try
            {
                var childClientVariations = await _clientVariationService.GetChildClientVariationsAsync(parentId);
                return Ok(childClientVariations);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // POST: api/ClientVariations
        [HttpPost]
        public async Task<ActionResult<ClientVariationDTO>> CreateClientVariation(ClientVariationCreateDTO clientVariationDto)
        {
            try
            {
                var createdClientVariation = await _clientVariationService.CreateClientVariationAsync(clientVariationDto);
                return CreatedAtAction(nameof(GetClientVariation), new { id = createdClientVariation.ClientVariationId }, createdClientVariation);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // PUT: api/ClientVariations/5
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateClientVariation(int id, ClientVariationUpdateDTO clientVariationDto)
        {
            try
            {
                var updatedClientVariation = await _clientVariationService.UpdateClientVariationAsync(id, clientVariationDto);
                return Ok(updatedClientVariation);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // DELETE: api/ClientVariations/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteClientVariation(int id)
        {
            try
            {
                var result = await _clientVariationService.DeleteClientVariationAsync(id);
                
                if (!result)
                    return NotFound(new { message = $"ClientVariation with ID {id} not found" });
                    
                return NoContent();
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
    }
}
