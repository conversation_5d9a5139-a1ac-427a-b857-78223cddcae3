import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import Input from '../common/Input';
import Button from '../common/Button';
import Checkbox from '../common/Checkbox';
import Divider from '../common/Divider';

const LoginFormContainer = styled.div`
  width: 100%;
  max-width: 100%;
  padding: 0;
  background-color: transparent;
`;

const FormTitle = styled.h3`
  font-family: 'Poppins', sans-serif;
  font-size: 20px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 4px;
  text-align: left;
`;

const FormSubtitle = styled.p`
  font-family: 'Poppins', sans-serif;
  font-size: 13px;
  color: #6B7280;
  margin-bottom: 24px;
  text-align: left;
`;

const ForgotPasswordLink = styled(Link)`
  font-family: 'Poppins', sans-serif;
  font-size: 13px;
  color: #374151;
  text-align: right;
  display: block;
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
`;

const SocialButtonsContainer = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: center;
  gap: 48px;
  margin-top: 24px;
  margin-bottom: 24px;
`;

const SocialButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s;

  &:hover {
    background-color: #F3F4F6;
    transform: translateY(-1px);
  }
`;

const SocialIcon = styled.img`
  width: 24px;
  height: 24px;
`;

const SignUpText = styled.p`
  font-family: 'Poppins', sans-serif;
  font-size: 13px;
  color: #6B7280;
  text-align: center;
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
`;

const ErrorMessageContainer = styled.div`
  background-color: rgba(220, 53, 69, 0.1);
  color: #dc3545;
  padding: 0.75rem;
  border-radius: 4px;
  margin-bottom: 1.5rem;
  font-size: 13px;
`;

const ButtonWrapper = styled.div`
  margin-bottom: 24px;
`;

const CheckboxRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
`;

const LoginForm = ({ onSubmit, error, isLoading }) => {
  const [formData, setFormData] = useState({
    usernameOrEmail: '',
    password: '',
    rememberMe: false
  });

  const [errors, setErrors] = useState({});

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));

    // Clear error when user types
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.usernameOrEmail.trim()) {
      newErrors.usernameOrEmail = 'Username or email is required';
    }

    if (!formData.password) {
      newErrors.password = 'Password is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (validateForm()) {
      try {
        await onSubmit?.(formData);
      } catch (err) {
        console.error('Login error:', err);
      }
    }
  };

  return (
    <LoginFormContainer>
      <FormTitle>Welcome Stick Build P/L</FormTitle>
      <FormSubtitle>Sign in to Access Project Management Dashboard</FormSubtitle>

      {error && (
        <ErrorMessageContainer>
          {error}
        </ErrorMessageContainer>
      )}

      <form onSubmit={handleSubmit} style={{ width: '100%' }}>
        <Input
          label="Username"
          id="usernameOrEmail"
          name="usernameOrEmail"
          type="text"
          placeholder="<EMAIL>"
          value={formData.usernameOrEmail}
          onChange={handleChange}
          error={errors.usernameOrEmail}
        />

        <Input
          label="Password"
          id="password"
          name="password"
          type="password"
          placeholder="********"
          value={formData.password}
          onChange={handleChange}
          error={errors.password}
        />

        <CheckboxRow>
          <Checkbox
            id="rememberMe"
            name="rememberMe"
            checked={formData.rememberMe}
            onChange={handleChange}
            label="Remember this Device"
          />
          <ForgotPasswordLink to="/forgot-password">Forgot Password ?</ForgotPasswordLink>
        </CheckboxRow>

        <ButtonWrapper>
          <Button
            type="submit"
            fullWidth
            disabled={isLoading}
          >
            {isLoading ? 'Logging in...' : (
              <>
                <span>Login</span>
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M11 7L17 12L11 17" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </>
            )}
          </Button>
        </ButtonWrapper>
      </form>

      <Divider text="Sign in with email account or Enter your details" />

      <SocialButtonsContainer>
        <SocialButton>
          <SocialIcon src="/assets/images/logos/google-icon.svg" alt="Google" />
        </SocialButton>

        <SocialButton>
          <SocialIcon src="/assets/images/logos/apple-icon.svg" alt="Apple" />
        </SocialButton>
      </SocialButtonsContainer>

      <SignUpText>
        Create a new account
      </SignUpText>
    </LoginFormContainer>
  );
};

export default LoginForm;
