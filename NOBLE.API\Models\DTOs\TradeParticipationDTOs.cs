using System.ComponentModel.DataAnnotations;

namespace NOBLE.API.Models.DTOs
{
    public class TradeParticipationDTO
    {
        public int TradeParticipationId { get; set; }
        public int? TradeId { get; set; }
        public int? SubContractorId { get; set; }
        public int? ContactPeopleId { get; set; }
        public DateTime? InvitationDate { get; set; }
        public DateTime? QuoteDate { get; set; }
        public DateTime? QuoteDueDate { get; set; }
        public decimal? Amount { get; set; }
        public string? Status { get; set; }
        public int? Rank { get; set; }
        public string? Comments { get; set; }
        public string? InternalComments { get; set; }
        public bool? PulledOut { get; set; }
        public DateTime? OpenDate { get; set; }
        public DateTime? ReminderDate { get; set; }
        public string? TradeParticipationType { get; set; }
        public int? ComparisonTradeParticipationId { get; set; }
        public string? QuoteFile { get; set; }
        public int? TransmittalId { get; set; }
        public string? SafetyRating { get; set; }
        public string? PaymentTerms { get; set; }
        
        // Navigation properties for display purposes
        public string? TradeName { get; set; }
        public string? ProjectName { get; set; }
        public string? SubContractorName { get; set; }
        public string? ContactPersonName { get; set; }
        public int ChildParticipationsCount { get; set; }
    }

    public class TradeParticipationCreateDTO
    {
        [Required]
        public int TradeId { get; set; }
        
        public int? SubContractorId { get; set; }
        public int? ContactPeopleId { get; set; }
        public DateTime? InvitationDate { get; set; }
        public DateTime? QuoteDate { get; set; }
        public DateTime? QuoteDueDate { get; set; }
        public decimal? Amount { get; set; }
        
        [StringLength(2)]
        public string? Status { get; set; }
        
        public int? Rank { get; set; }
        
        [StringLength(1000)]
        public string? Comments { get; set; }
        
        public string? InternalComments { get; set; }
        public bool? PulledOut { get; set; }
        public DateTime? OpenDate { get; set; }
        public DateTime? ReminderDate { get; set; }
        
        [Required]
        [StringLength(1)]
        public string TradeParticipationType { get; set; } = "S"; // Default to Subcontractor
        
        public int? ComparisonTradeParticipationId { get; set; }
        
        [StringLength(255)]
        public string? QuoteFile { get; set; }
        
        public int? TransmittalId { get; set; }
        
        [StringLength(50)]
        public string? SafetyRating { get; set; }
        
        [StringLength(10)]
        public string? PaymentTerms { get; set; }
    }

    public class TradeParticipationUpdateDTO
    {
        public int? SubContractorId { get; set; }
        public int? ContactPeopleId { get; set; }
        public DateTime? InvitationDate { get; set; }
        public DateTime? QuoteDate { get; set; }
        public DateTime? QuoteDueDate { get; set; }
        public decimal? Amount { get; set; }
        
        [StringLength(2)]
        public string? Status { get; set; }
        
        public int? Rank { get; set; }
        
        [StringLength(1000)]
        public string? Comments { get; set; }
        
        public string? InternalComments { get; set; }
        public bool? PulledOut { get; set; }
        public DateTime? OpenDate { get; set; }
        public DateTime? ReminderDate { get; set; }
        
        [StringLength(1)]
        public string? TradeParticipationType { get; set; }
        
        public int? ComparisonTradeParticipationId { get; set; }
        
        [StringLength(255)]
        public string? QuoteFile { get; set; }
        
        public int? TransmittalId { get; set; }
        
        [StringLength(50)]
        public string? SafetyRating { get; set; }
        
        [StringLength(10)]
        public string? PaymentTerms { get; set; }
    }
}
