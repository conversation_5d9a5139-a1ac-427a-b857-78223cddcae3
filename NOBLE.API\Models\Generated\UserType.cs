﻿using System;
using System.Collections.Generic;

namespace NOBLE.API.Models.Generated;

public partial class UserType
{
    public int UserTypeId { get; set; }

    public string? Name { get; set; }

    public string? Description { get; set; }

    public DateTime? CreatedDate { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public int? CreatedPeopleId { get; set; }

    public int? ModifiedPeopleId { get; set; }
}
