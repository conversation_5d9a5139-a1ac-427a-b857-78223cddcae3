using Microsoft.EntityFrameworkCore;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Models.Generated;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Services.Implementations
{
    public class ProjectService : IProjectService
    {
        private readonly NobleDbContext _context;
        private readonly IUserContextService _userContextService;

        public ProjectService(NobleDbContext context, IUserContextService userContextService)
        {
            _context = context;
            _userContextService = userContextService;
        }

        public async Task<IEnumerable<ProjectDTO>> GetAllProjectsAsync()
        {
            return await _context.Projects
                .Include(p => p.BusinessUnit)
                .Select(p => MapToProjectDTO(p))
                .ToListAsync();
        }

        public async Task<ProjectDTO> GetProjectByIdAsync(int id)
        {
            var project = await _context.Projects
                .Include(p => p.BusinessUnit)
                .FirstOrDefaultAsync(p => p.ProjectId == id);

            if (project == null)
                throw new KeyNotFoundException($"Project with ID {id} not found");

            return MapToProjectDTO(project);
        }

        public async Task<IEnumerable<ProjectDTO>> GetProjectsByBusinessUnitAsync(int businessUnitId)
        {
            return await _context.Projects
                .Include(p => p.BusinessUnit)
                .Where(p => p.BusinessUnitId == businessUnitId)
                .Select(p => MapToProjectDTO(p))
                .ToListAsync();
        }

        public async Task<ProjectDTO> CreateProjectAsync(ProjectCreateDTO projectDto)
        {
          
            var businessUnit = await _context.BusinessUnits.FindAsync(projectDto.BusinessUnitId);
            if (businessUnit == null)
                throw new KeyNotFoundException($"Business Unit with ID {projectDto.BusinessUnitId} not found");

            var project = new Project
            {
                BusinessUnitId = projectDto.BusinessUnitId,
                Name = projectDto.Name,
                Number = projectDto.Number,
                Year = projectDto.Year,
                Description = projectDto.Description,
                Street = projectDto.Street,
                City = projectDto.City,
                State = projectDto.State,
                PostalCode = projectDto.PostalCode,
                AttachmentsFolder = projectDto.AttachmentsFolder,
                DefectsLiability = projectDto.DefectsLiability,
                LiquidatedDamages = projectDto.LiquidatedDamages,
                SiteAllowances = projectDto.SiteAllowances,
                Retention = projectDto.Retention,
                RetentionToCertification = projectDto.RetentionToCertification,
                RetentionToDlp = projectDto.RetentionToDlp,
                Interest = projectDto.Interest,
                SpecialClause = projectDto.SpecialClause,
                LawOfSubcontract = projectDto.LawOfSubcontract,
                ProjectStatus = projectDto.ProjectStatus,
                CommencementDate = projectDto.CommencementDate,
                CompletionDate = projectDto.CompletionDate,
                DistributionListInfo = projectDto.DistributionListInfo,
                ContractAmount = projectDto.ContractAmount,
                PaymentTerms = projectDto.PaymentTerms,
                ClaimFrequency = projectDto.ClaimFrequency,
                Waranty1Amount = projectDto.Waranty1Amount,
                Waranty1Date = projectDto.Waranty1Date,
                Waranty2Amount = projectDto.Waranty2Amount,
                Waranty2Date = projectDto.Waranty2Date,
                PracticalCompletionDate = projectDto.PracticalCompletionDate,
                FirstClaimDueDate = projectDto.FirstClaimDueDate,
                Principal = projectDto.Principal,
                PrincipalAbn = projectDto.PrincipalAbn,
                AccountName = projectDto.AccountName,
                Bsb = projectDto.Bsb,
                AccountNumber = projectDto.AccountNumber,
                Siteaddress = projectDto.Siteaddress,
                SiteSuburb = projectDto.SiteSuburb,
                SiteState = projectDto.SiteState,
                SitePostalCode = projectDto.SitePostalCode,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow,
                CreatedPeopleId = _userContextService.GetCurrentUserId(),
                ModifiedPeopleId = _userContextService.GetCurrentUserId()
            };

            _context.Projects.Add(project);
            await _context.SaveChangesAsync();

            return await GetProjectByIdAsync(project.ProjectId);
        }

        public async Task<ProjectDTO> UpdateProjectAsync(int id, ProjectUpdateDTO projectDto)
        {
            var project = await _context.Projects.FindAsync(id);

            if (project == null)
                throw new KeyNotFoundException($"Project with ID {id} not found");
             
            var businessUnit = await _context.BusinessUnits.FindAsync(projectDto.BusinessUnitId);
            if (businessUnit == null)
                throw new KeyNotFoundException($"Business Unit with ID {projectDto.BusinessUnitId} not found");

            project.BusinessUnitId = projectDto.BusinessUnitId;
            project.Name = projectDto.Name;
            project.Number = projectDto.Number;
            project.Year = projectDto.Year;
            project.Description = projectDto.Description;
            project.Street = projectDto.Street;
            project.City = projectDto.City;
            project.State = projectDto.State;
            project.PostalCode = projectDto.PostalCode;
            project.AttachmentsFolder = projectDto.AttachmentsFolder;
            project.DefectsLiability = projectDto.DefectsLiability;
            project.LiquidatedDamages = projectDto.LiquidatedDamages;
            project.SiteAllowances = projectDto.SiteAllowances;
            project.Retention = projectDto.Retention;
            project.RetentionToCertification = projectDto.RetentionToCertification;
            project.RetentionToDlp = projectDto.RetentionToDlp;
            project.Interest = projectDto.Interest;
            project.SpecialClause = projectDto.SpecialClause;
            project.LawOfSubcontract = projectDto.LawOfSubcontract;
            project.ProjectStatus = projectDto.ProjectStatus;
            project.CommencementDate = projectDto.CommencementDate;
            project.CompletionDate = projectDto.CompletionDate;
            project.DistributionListInfo = projectDto.DistributionListInfo;
            project.ContractAmount = projectDto.ContractAmount;
            project.PaymentTerms = projectDto.PaymentTerms;
            project.ClaimFrequency = projectDto.ClaimFrequency;
            project.Waranty1Amount = projectDto.Waranty1Amount;
            project.Waranty1Date = projectDto.Waranty1Date;
            project.Waranty2Amount = projectDto.Waranty2Amount;
            project.Waranty2Date = projectDto.Waranty2Date;
            project.PracticalCompletionDate = projectDto.PracticalCompletionDate;
            project.FirstClaimDueDate = projectDto.FirstClaimDueDate;
            project.Principal = projectDto.Principal;
            project.PrincipalAbn = projectDto.PrincipalAbn;
            project.AccountName = projectDto.AccountName;
            project.Bsb = projectDto.Bsb;
            project.AccountNumber = projectDto.AccountNumber;
            project.Siteaddress = projectDto.Siteaddress;
            project.SiteSuburb = projectDto.SiteSuburb;
            project.SiteState = projectDto.SiteState;
            project.SitePostalCode = projectDto.SitePostalCode;
            project.ModifiedDate = DateTime.UtcNow;
            project.ModifiedPeopleId = _userContextService.GetCurrentUserId();

            await _context.SaveChangesAsync();

            return await GetProjectByIdAsync(id);
        }

        public async Task<bool> DeleteProjectAsync(int id)
        {
            var project = await _context.Projects.FindAsync(id);

            if (project == null)
                return false;
             
            var hasTrades = await _context.Trades.AnyAsync(t => t.ProjectId == id);
            var hasBudgets = await _context.Budgets.AnyAsync(b => b.ProjectId == id);
            var hasClaims = await _context.Claims.AnyAsync(c => c.ProjectId == id);
            var hasClientVariations = await _context.ClientVariations.AnyAsync(cv => cv.ProjectId == id);
            var hasEOTs = await _context.Eots.AnyAsync(e => e.ProjectId == id);
            var hasRFIs = await _context.Rfis.AnyAsync(r => r.ProjectId == id);

            if (hasTrades || hasBudgets || hasClaims || hasClientVariations || hasEOTs || hasRFIs)
            { 
                throw new InvalidOperationException("Cannot delete project with related entities");
            }

            _context.Projects.Remove(project);
            await _context.SaveChangesAsync();

            return true;
        }

        private static ProjectDTO MapToProjectDTO(Project project)
        {
            return new ProjectDTO
            {
                ProjectId = project.ProjectId,
                BusinessUnitId = project.BusinessUnitId,
                Name = project.Name,
                Number = project.Number,
                Year = project.Year,
                Description = project.Description,
                Street = project.Street,
                City = project.City,
                State = project.State,
                PostalCode = project.PostalCode,
                AttachmentsFolder = project.AttachmentsFolder,
                DefectsLiability = project.DefectsLiability,
                LiquidatedDamages = project.LiquidatedDamages,
                SiteAllowances = project.SiteAllowances,
                Retention = project.Retention,
                RetentionToCertification = project.RetentionToCertification,
                RetentionToDlp = project.RetentionToDlp,
                Interest = project.Interest,
                SpecialClause = project.SpecialClause,
                LawOfSubcontract = project.LawOfSubcontract,
                ProjectStatus = project.ProjectStatus,
                CommencementDate = project.CommencementDate,
                CompletionDate = project.CompletionDate,
                DistributionListInfo = project.DistributionListInfo,
                ContractAmount = project.ContractAmount,
                PaymentTerms = project.PaymentTerms,
                ClaimFrequency = project.ClaimFrequency,
                Waranty1Amount = project.Waranty1Amount,
                Waranty1Date = project.Waranty1Date,
                Waranty2Amount = project.Waranty2Amount,
                Waranty2Date = project.Waranty2Date,
                PracticalCompletionDate = project.PracticalCompletionDate,
                FirstClaimDueDate = project.FirstClaimDueDate,
                Principal = project.Principal,
                PrincipalAbn = project.PrincipalAbn,
                AccountName = project.AccountName,
                Bsb = project.Bsb,
                AccountNumber = project.AccountNumber,
                Siteaddress = project.Siteaddress,
                SiteSuburb = project.SiteSuburb,
                SiteState = project.SiteState,
                SitePostalCode = project.SitePostalCode,
                BusinessUnitName = project.BusinessUnit?.Name
            };
        }


    }
}
