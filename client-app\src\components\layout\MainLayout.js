import { useState } from 'react';
import styled from 'styled-components';
import Sidebar from './Sidebar';
import Navbar from './Navbar';
import ColorPalette from '../../styles/ColorPalette';

const LayoutContainer = styled.div`
  display: flex;
  min-height: 100vh;
  background-color: ${ColorPalette.background.default};
`;

const MainContent = styled.main`
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  background-color: ${ColorPalette.background.default};
  margin-left: ${props => props.sidebarCollapsed ? '70px' : '240px'};
  transition: margin-left 0.3s ease;
`;

const ContentWrapper = styled.div`
  padding: 24px;
  flex: 1;
`;

const MainLayout = ({ children }) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  return (
    <LayoutContainer>
      <Sidebar collapsed={sidebarCollapsed} toggleSidebar={toggleSidebar} />
      <MainContent sidebarCollapsed={sidebarCollapsed}>
        <Navbar />
        <ContentWrapper>
          {children}
        </ContentWrapper>
      </MainContent>
    </LayoutContainer>
  );
};

export default MainLayout;
