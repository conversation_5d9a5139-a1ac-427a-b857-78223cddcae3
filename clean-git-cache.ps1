# This script removes files from Git tracking that are now in .gitignore
# but were previously committed to the repository.
# It does NOT delete the files from your filesystem.

Write-Host "Cleaning Git cache to remove tracked files that are now in .gitignore..." -ForegroundColor Yellow

# Remove all files from the Git index
git rm -r --cached .

# Re-add all files that are not ignored by .gitignore
git add .

Write-Host "Done! Now you can commit these changes with a message like:" -ForegroundColor Green
Write-Host "git commit -m 'Remove ignored files from Git tracking'" -ForegroundColor Cyan
Write-Host ""
Write-Host "After committing, these files will no longer be tracked by Git, but they will remain on your filesystem." -ForegroundColor Green
