using Microsoft.EntityFrameworkCore;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Models.Generated;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Services.Implementations
{
    public class HolidayService : IHolidayService
    {
        private readonly NobleDbContext _context;

        public HolidayService(NobleDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<HolidayDTO>> GetAllHolidaysAsync()
        {
            return await _context.Holidays
                .OrderBy(h => h.HolidayDate)
                .Select(h => new HolidayDTO
                {
                    HolidayDate = h.HolidayDate
                })
                .ToListAsync();
        }

        public async Task<HolidayDTO> GetHolidayByDateAsync(DateTime date)
        {
            // Normalize the date to remove time component
            var normalizedDate = date.Date;
            
            var holiday = await _context.Holidays
                .FirstOrDefaultAsync(h => h.HolidayDate == normalizedDate);
            
            if (holiday == null)
                throw new KeyNotFoundException($"Holiday with date {normalizedDate:yyyy-MM-dd} not found");
                
            return new HolidayDTO
            {
                HolidayDate = holiday.HolidayDate
            };
        }

        public async Task<IEnumerable<HolidayDTO>> GetHolidaysByYearAsync(int year)
        {
            var startDate = new DateTime(year, 1, 1);
            var endDate = new DateTime(year, 12, 31);
            
            return await _context.Holidays
                .Where(h => h.HolidayDate >= startDate && h.HolidayDate <= endDate)
                .OrderBy(h => h.HolidayDate)
                .Select(h => new HolidayDTO
                {
                    HolidayDate = h.HolidayDate
                })
                .ToListAsync();
        }

        public async Task<IEnumerable<HolidayDTO>> GetHolidaysByMonthAsync(int year, int month)
        {
            var startDate = new DateTime(year, month, 1);
            var endDate = startDate.AddMonths(1).AddDays(-1);
            
            return await _context.Holidays
                .Where(h => h.HolidayDate >= startDate && h.HolidayDate <= endDate)
                .OrderBy(h => h.HolidayDate)
                .Select(h => new HolidayDTO
                {
                    HolidayDate = h.HolidayDate
                })
                .ToListAsync();
        }

        public async Task<IEnumerable<HolidayDTO>> GetHolidaysByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            // Normalize the dates to remove time component
            var normalizedStartDate = startDate.Date;
            var normalizedEndDate = endDate.Date;
            
            return await _context.Holidays
                .Where(h => h.HolidayDate >= normalizedStartDate && h.HolidayDate <= normalizedEndDate)
                .OrderBy(h => h.HolidayDate)
                .Select(h => new HolidayDTO
                {
                    HolidayDate = h.HolidayDate
                })
                .ToListAsync();
        }

        public async Task<HolidayDTO> CreateHolidayAsync(HolidayCreateDTO holidayDto)
        {
            // Normalize the date to remove time component
            var normalizedDate = holidayDto.HolidayDate.Date;
            
            // Check if the holiday already exists
            var existingHoliday = await _context.Holidays.FindAsync(normalizedDate);
            if (existingHoliday != null)
                throw new InvalidOperationException($"Holiday with date {normalizedDate:yyyy-MM-dd} already exists");
                
            var holiday = new Holiday
            {
                HolidayDate = normalizedDate
            };
            
            _context.Holidays.Add(holiday);
            await _context.SaveChangesAsync();
            
            return new HolidayDTO
            {
                HolidayDate = holiday.HolidayDate
            };
        }

        public async Task<bool> DeleteHolidayAsync(DateTime date)
        {
            // Normalize the date to remove time component
            var normalizedDate = date.Date;
            
            var holiday = await _context.Holidays.FindAsync(normalizedDate);
            
            if (holiday == null)
                return false;
                
            _context.Holidays.Remove(holiday);
            await _context.SaveChangesAsync();
            
            return true;
        }

        public async Task<bool> IsHolidayAsync(DateTime date)
        {
            // Normalize the date to remove time component
            var normalizedDate = date.Date;
            
            return await _context.Holidays.AnyAsync(h => h.HolidayDate == normalizedDate);
        }
    }
}
