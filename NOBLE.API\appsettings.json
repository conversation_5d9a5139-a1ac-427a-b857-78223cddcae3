{"ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=Noble;Trusted_Connection=True;MultipleActiveResultSets=true;TrustServerCertificate=True"}, "JwtSettings": {"Secret": "YourSecretKeyHereMakeItLongAndComplex_NobleConstructionSecretKey", "Issuer": "NobleConstructionApp", "Audience": "NobleConstructionAppClients", "AccessTokenExpirationMinutes": 60, "RefreshTokenExpirationDays": 7}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}