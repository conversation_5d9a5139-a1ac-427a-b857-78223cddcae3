﻿using System;
using System.Collections.Generic;

namespace NOBLE.API.Models.Generated;

public partial class BudgetsTrade
{
    public int BudgetId { get; set; }

    public int TradeId { get; set; }

    public decimal? BudgetAmount { get; set; }

    public decimal? BudgetAmountInitial { get; set; }

    public decimal? BudgetAmountAllowance { get; set; }

    public decimal? BudgetAmountTradeInitial { get; set; }

    public DateTime? BudgetDate { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public int CreatedPeopleId { get; set; }

    public int ModifiedPeopleId { get; set; }

    public virtual Budget Budget { get; set; } = null!;

    public virtual Trade Trade { get; set; } = null!;
}
