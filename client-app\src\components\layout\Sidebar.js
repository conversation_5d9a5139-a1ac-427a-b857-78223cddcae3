
import { Link, useLocation } from 'react-router-dom';
import styled from 'styled-components';
import ColorPalette from '../../styles/ColorPalette';
import Typography from '../../styles/Typography';

const SidebarContainer = styled.aside`
  width: ${props => props.collapsed ? '70px' : '240px'};
  min-width: ${props => props.collapsed ? '70px' : '240px'};
  height: 100vh;
  background-color: ${ColorPalette.neutral.white};
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  transition: width 0.3s ease;
`;

const LogoContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: ${props => props.collapsed ? 'center' : 'flex-start'};
  padding: 16px;
  border-bottom: 1px solid ${ColorPalette.border.light};
  position: relative;
  height: 64px;
`;

const Logo = styled.div`
  display: flex;
  align-items: center;
  overflow: hidden;
  width: ${props => props.collapsed ? '32px' : '100%'};
  position: relative;
`;

const LogoImage = styled.img`
  height: 32px;
  margin-right: 8px;
  flex-shrink: 0;
`;

const LogoText = styled.div`
  font-family: ${Typography.fontFamily.secondary};
  font-weight: ${Typography.fontWeight.bold};
  font-size: ${Typography.fontSize.md};
  color: ${ColorPalette.text.primary};
  line-height: 1;
  white-space: nowrap;
  overflow: hidden;
  opacity: ${props => props.collapsed ? 0 : 1};
  max-width: ${props => props.collapsed ? 0 : '200px'};
  transition: opacity 0.2s ease, max-width 0.3s ease;

  span {
    display: block;
    font-size: ${Typography.fontSize.xs};
    color: ${ColorPalette.text.secondary};
    margin-top: 4px;
  }
`;

const HamburgerButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  color: ${ColorPalette.text.secondary};
  position: absolute;
  left: ${props => props.collapsed ? '22px' : 'auto'};
  right: ${props => props.collapsed ? 'auto' : '16px'};
  top: 50%;
  transform: translateY(-50%);
  z-index: 101;

  svg {
    width: 24px;
    height: 24px;
  }
`;

const MenuContainer = styled.nav`
  display: flex;
  flex-direction: column;
  padding: 16px 0;
  flex: 1;
`;

const MenuItem = styled(Link)`
  display: flex;
  align-items: center;
  padding: 12px 16px;
  color: ${props => props.active ? ColorPalette.primary.main : ColorPalette.text.primary};
  text-decoration: none;
  font-family: ${Typography.fontFamily.primary};
  font-size: ${Typography.fontSize.sm};
  font-weight: ${props => props.active ? Typography.fontWeight.medium : Typography.fontWeight.regular};
  transition: background-color 0.2s ease;
  border-left: 3px solid ${props => props.active ? ColorPalette.primary.main : 'transparent'};
  background-color: ${props => props.active ? ColorPalette.primary.light + '10' : 'transparent'};
  justify-content: ${props => props.collapsed ? 'center' : 'flex-start'};

  &:hover {
    background-color: ${ColorPalette.neutral.offWhite};
    color: ${ColorPalette.primary.main};
  }

  svg {
    margin-right: ${props => props.collapsed ? '0' : '12px'};
    width: 20px;
    height: 20px;
    transition: margin-right 0.3s ease;
  }
`;

const MenuItemText = styled.span`
  white-space: nowrap;
  overflow: hidden;
  opacity: ${props => props.collapsed ? 0 : 1};
  max-width: ${props => props.collapsed ? 0 : '200px'};
  transition: opacity 0.2s ease, max-width 0.3s ease;
`;

const LogoutButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: ${props => props.collapsed ? 'center' : 'flex-start'};
  padding: 12px 16px;
  color: ${ColorPalette.text.primary};
  background: none;
  border: none;
  border-left: 3px solid transparent;
  text-decoration: none;
  font-family: ${Typography.fontFamily.primary};
  font-size: ${Typography.fontSize.sm};
  font-weight: ${Typography.fontWeight.regular};
  transition: background-color 0.2s ease;
  cursor: pointer;
  width: 100%;
  text-align: left;

  &:hover {
    background-color: ${ColorPalette.neutral.offWhite};
    color: ${ColorPalette.primary.main};
  }

  svg {
    margin-right: ${props => props.collapsed ? '0' : '12px'};
    width: 20px;
    height: 20px;
    transition: margin-right 0.3s ease;
  }
`;

const LogoutText = styled.span`
  white-space: nowrap;
  overflow: hidden;
  opacity: ${props => props.collapsed ? 0 : 1};
  max-width: ${props => props.collapsed ? 0 : '200px'};
  transition: opacity 0.2s ease, max-width 0.3s ease;
`;

// SVG Icons
const DashboardIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
  </svg>
);

const ProjectsIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
  </svg>
);

const UsersIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
  </svg>
);

const SubcontractorIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
  </svg>
);

const ReportsIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
  </svg>
);

const AdminIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
  </svg>
);

const LogoutIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
  </svg>
);
 
const HamburgerIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
  </svg>
);

const Sidebar = ({ collapsed, toggleSidebar }) => {
  const location = useLocation();
  const currentPath = location.pathname;

  return (
    <SidebarContainer collapsed={collapsed}>
      <LogoContainer collapsed={collapsed}>
        <Logo collapsed={collapsed}>
          <LogoImage src="/assets/images/logos/noble-icon-mark.svg" alt="Stick Build P/L" />
          <LogoText collapsed={collapsed}>
            STICK BUILD P/L
            <span>EST. 2021</span>
          </LogoText>
        </Logo>
        <HamburgerButton onClick={toggleSidebar} collapsed={collapsed}>
          <HamburgerIcon />
        </HamburgerButton>
      </LogoContainer>

      <MenuContainer>
        <MenuItem to="/dashboard" active={currentPath === '/dashboard' ? 'true' : undefined} collapsed={collapsed}>
          <DashboardIcon />
          <MenuItemText collapsed={collapsed}>My Dashboard</MenuItemText>
        </MenuItem>

        <MenuItem to="/projects" active={currentPath.includes('/projects') ? 'true' : undefined} collapsed={collapsed}>
          <ProjectsIcon />
          <MenuItemText collapsed={collapsed}>Projects</MenuItemText>
        </MenuItem>

        <MenuItem to="/users" active={currentPath.includes('/users') ? 'true' : undefined} collapsed={collapsed}>
          <UsersIcon />
          <MenuItemText collapsed={collapsed}>Users</MenuItemText>
        </MenuItem>

        <MenuItem to="/subcontractor" active={currentPath.includes('/subcontractor') ? 'true' : undefined} collapsed={collapsed}>
          <SubcontractorIcon />
          <MenuItemText collapsed={collapsed}>Subcontractor</MenuItemText>
        </MenuItem>

        <MenuItem to="/reports" active={currentPath.includes('/reports') ? 'true' : undefined} collapsed={collapsed}>
          <ReportsIcon />
          <MenuItemText collapsed={collapsed}>Reports</MenuItemText>
        </MenuItem>

        <MenuItem to="/admin" active={currentPath.includes('/admin') ? 'true' : undefined} collapsed={collapsed}>
          <AdminIcon />
          <MenuItemText collapsed={collapsed}>Admin</MenuItemText>
        </MenuItem>
      </MenuContainer>

      <LogoutButton collapsed={collapsed}>
        <LogoutIcon />
        <LogoutText collapsed={collapsed}>Logout</LogoutText>
      </LogoutButton>
    </SidebarContainer>
  );
};

export default Sidebar;
