using System.ComponentModel.DataAnnotations;

namespace NOBLE.API.Models.DTOs
{
    public class RFIsResponseAttachmentDTO
    {
        public int Id { get; set; }
        public int? RFIId { get; set; }
        public int? ResponseId { get; set; }
        public string? FileName { get; set; }
        public string? FileType { get; set; }
        public byte[]? FileData { get; set; }
        public DateTime? CreatedOn { get; set; }
        public string? CreatedBy { get; set; }
        public DateTime? ModifiedOn { get; set; }
        public string? ModifiedBy { get; set; }
        
        // Navigation properties for display purposes
        public string? RFISubject { get; set; }
        public string? ResponseFrom { get; set; }
    }

    public class RFIsResponseAttachmentCreateDTO
    {
        public int? RFIId { get; set; }
        
        [Required]
        public int ResponseId { get; set; }
        
        [Required]
        [StringLength(255)]
        public string FileName { get; set; } = string.Empty;
        
        [StringLength(100)]
        public string? FileType { get; set; }
        
        [Required]
        public byte[] FileData { get; set; } = Array.Empty<byte>();
    }

    public class RFIsResponseAttachmentUpdateDTO
    {
        [StringLength(255)]
        public string? FileName { get; set; }
        
        [StringLength(100)]
        public string? FileType { get; set; }
        
        public byte[]? FileData { get; set; }
    }
}
