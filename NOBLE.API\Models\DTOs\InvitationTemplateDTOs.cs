using System.ComponentModel.DataAnnotations;

namespace NOBLE.API.Models.DTOs
{
    public class InvitationTemplateDTO
    {
        public int InvitationTemplateId { get; set; }
        public string? Template { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime ModifiedDate { get; set; }
        
        // Navigation properties for display purposes
        public string? CreatedByName { get; set; }
        public string? ModifiedByName { get; set; }
    }

    public class InvitationTemplateCreateDTO
    {
        [Required]
        public string Template { get; set; } = string.Empty;
    }

    public class InvitationTemplateUpdateDTO
    {
        [Required]
        public string Template { get; set; } = string.Empty;
    }
}
