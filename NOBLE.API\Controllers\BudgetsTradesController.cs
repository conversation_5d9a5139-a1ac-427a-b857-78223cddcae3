using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class BudgetsTradesController : ControllerBase
    {
        private readonly IBudgetsTradeService _budgetsTradeService;
        
        public BudgetsTradesController(IBudgetsTradeService budgetsTradeService)
        {
            _budgetsTradeService = budgetsTradeService;
        }
        
        // GET: api/BudgetsTrades
        [HttpGet]
        public async Task<ActionResult<IEnumerable<BudgetsTradeDTO>>> GetBudgetsTrades()
        {
            var budgetsTrades = await _budgetsTradeService.GetAllBudgetsTradesAsync();
            return Ok(budgetsTrades);
        }
        
        // GET: api/BudgetsTrades/Budget/5/Trade/10
        [HttpGet("Budget/{budgetId}/Trade/{tradeId}")]
        public async Task<ActionResult<BudgetsTradeDTO>> GetBudgetsTrade(int budgetId, int tradeId)
        {
            try
            {
                var budgetsTrade = await _budgetsTradeService.GetBudgetsTradeByIdsAsync(budgetId, tradeId);
                return Ok(budgetsTrade);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // GET: api/BudgetsTrades/Budget/5
        [HttpGet("Budget/{budgetId}")]
        public async Task<ActionResult<IEnumerable<BudgetsTradeDTO>>> GetBudgetsTradesByBudget(int budgetId)
        {
            try
            {
                var budgetsTrades = await _budgetsTradeService.GetBudgetsTradesByBudgetIdAsync(budgetId);
                return Ok(budgetsTrades);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // GET: api/BudgetsTrades/Trade/5
        [HttpGet("Trade/{tradeId}")]
        public async Task<ActionResult<IEnumerable<BudgetsTradeDTO>>> GetBudgetsTradesByTrade(int tradeId)
        {
            try
            {
                var budgetsTrades = await _budgetsTradeService.GetBudgetsTradesByTradeIdAsync(tradeId);
                return Ok(budgetsTrades);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // POST: api/BudgetsTrades
        [HttpPost]
        public async Task<ActionResult<BudgetsTradeDTO>> CreateBudgetsTrade(BudgetsTradeCreateDTO budgetsTradeDto)
        {
            try
            {
                var createdBudgetsTrade = await _budgetsTradeService.CreateBudgetsTradeAsync(budgetsTradeDto);
                return CreatedAtAction(
                    nameof(GetBudgetsTrade), 
                    new { budgetId = createdBudgetsTrade.BudgetId, tradeId = createdBudgetsTrade.TradeId }, 
                    createdBudgetsTrade);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // PUT: api/BudgetsTrades/Budget/5/Trade/10
        [HttpPut("Budget/{budgetId}/Trade/{tradeId}")]
        public async Task<IActionResult> UpdateBudgetsTrade(int budgetId, int tradeId, BudgetsTradeUpdateDTO budgetsTradeDto)
        {
            try
            {
                var updatedBudgetsTrade = await _budgetsTradeService.UpdateBudgetsTradeAsync(budgetId, tradeId, budgetsTradeDto);
                return Ok(updatedBudgetsTrade);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // DELETE: api/BudgetsTrades/Budget/5/Trade/10
        [HttpDelete("Budget/{budgetId}/Trade/{tradeId}")]
        public async Task<IActionResult> DeleteBudgetsTrade(int budgetId, int tradeId)
        {
            try
            {
                var result = await _budgetsTradeService.DeleteBudgetsTradeAsync(budgetId, tradeId);
                
                if (!result)
                    return NotFound(new { message = $"BudgetsTrade with BudgetId {budgetId} and TradeId {tradeId} not found" });
                    
                return NoContent();
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
    }
}
