using Microsoft.EntityFrameworkCore;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Models.Generated;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Services.Implementations
{
    public class ClientVariationsTradesTradeService : IClientVariationsTradesTradeService
    {
        private readonly NobleDbContext _context;
        private readonly IUserContextService _userContextService;

        public ClientVariationsTradesTradeService(NobleDbContext context, IUserContextService userContextService)
        {
            _context = context;
            _userContextService = userContextService;
        }

        public async Task<IEnumerable<ClientVariationsTradesTradeDTO>> GetAllClientVariationsTradesTradesAsync()
        {
            var clientVariationsTradesTrades = await _context.ClientVariationsTradesTrades.ToListAsync();
            var result = new List<ClientVariationsTradesTradeDTO>();
            
            foreach (var cvtt in clientVariationsTradesTrades)
            {
                var dto = new ClientVariationsTradesTradeDTO
                {
                    ClientVariationTradeId = cvtt.ClientVariationTradeId,
                    TradeId = cvtt.TradeId,
                    BudgetAmount = cvtt.BudgetAmount,
                    BudgetAmountInitial = cvtt.BudgetAmountInitial,
                    BudgetAmountAllowance = cvtt.BudgetAmountAllowance,
                    BudgetAmountTradeInitial = cvtt.BudgetAmountTradeInitial,
                    BudgetDate = cvtt.BudgetDate
                };
                
                // Get ClientVariationTrade name
                var clientVariationTrade = await _context.ClientVariationsTrades.FindAsync(cvtt.ClientVariationTradeId);
                if (clientVariationTrade != null)
                {
                    dto.ClientVariationTradeName = clientVariationTrade.TradeCode;
                }
                
                // Get Trade name and Project name
                var trade = await _context.Trades
                    .Include(t => t.Project)
                    .FirstOrDefaultAsync(t => t.TradeId == cvtt.TradeId);
                    
                if (trade != null)
                {
                    dto.TradeName = trade.Name;
                    dto.ProjectName = trade.Project?.Name;
                }
                
                result.Add(dto);
            }
            
            return result;
        }

        public async Task<ClientVariationsTradesTradeDTO> GetClientVariationsTradesTradeByIdsAsync(int clientVariationTradeId, int tradeId)
        {
            var cvtt = await _context.ClientVariationsTradesTrades
                .FirstOrDefaultAsync(c => c.ClientVariationTradeId == clientVariationTradeId && c.TradeId == tradeId);
            
            if (cvtt == null)
                throw new KeyNotFoundException($"ClientVariationsTradesTrade with ClientVariationTradeId {clientVariationTradeId} and TradeId {tradeId} not found");
                
            var dto = new ClientVariationsTradesTradeDTO
            {
                ClientVariationTradeId = cvtt.ClientVariationTradeId,
                TradeId = cvtt.TradeId,
                BudgetAmount = cvtt.BudgetAmount,
                BudgetAmountInitial = cvtt.BudgetAmountInitial,
                BudgetAmountAllowance = cvtt.BudgetAmountAllowance,
                BudgetAmountTradeInitial = cvtt.BudgetAmountTradeInitial,
                BudgetDate = cvtt.BudgetDate
            };
            
            // Get ClientVariationTrade name
            var clientVariationTrade = await _context.ClientVariationsTrades.FindAsync(cvtt.ClientVariationTradeId);
            if (clientVariationTrade != null)
            {
                dto.ClientVariationTradeName = clientVariationTrade.TradeCode;
            }
            
            // Get Trade name and Project name
            var trade = await _context.Trades
                .Include(t => t.Project)
                .FirstOrDefaultAsync(t => t.TradeId == cvtt.TradeId);
                
            if (trade != null)
            {
                dto.TradeName = trade.Name;
                dto.ProjectName = trade.Project?.Name;
            }
            
            return dto;
        }

        public async Task<IEnumerable<ClientVariationsTradesTradeDTO>> GetClientVariationsTradesTradesByClientVariationTradeIdAsync(int clientVariationTradeId)
        {
            // Check if the client variation trade exists
            var clientVariationTrade = await _context.ClientVariationsTrades.FindAsync(clientVariationTradeId);
            if (clientVariationTrade == null)
                throw new KeyNotFoundException($"ClientVariationTrade with ID {clientVariationTradeId} not found");
                
            var clientVariationsTradesTrades = await _context.ClientVariationsTradesTrades
                .Where(c => c.ClientVariationTradeId == clientVariationTradeId)
                .ToListAsync();
                
            var result = new List<ClientVariationsTradesTradeDTO>();
            
            foreach (var cvtt in clientVariationsTradesTrades)
            {
                var dto = new ClientVariationsTradesTradeDTO
                {
                    ClientVariationTradeId = cvtt.ClientVariationTradeId,
                    TradeId = cvtt.TradeId,
                    BudgetAmount = cvtt.BudgetAmount,
                    BudgetAmountInitial = cvtt.BudgetAmountInitial,
                    BudgetAmountAllowance = cvtt.BudgetAmountAllowance,
                    BudgetAmountTradeInitial = cvtt.BudgetAmountTradeInitial,
                    BudgetDate = cvtt.BudgetDate,
                    ClientVariationTradeName = clientVariationTrade.TradeCode
                };
                
                // Get Trade name and Project name
                var trade = await _context.Trades
                    .Include(t => t.Project)
                    .FirstOrDefaultAsync(t => t.TradeId == cvtt.TradeId);
                    
                if (trade != null)
                {
                    dto.TradeName = trade.Name;
                    dto.ProjectName = trade.Project?.Name;
                }
                
                result.Add(dto);
            }
            
            return result;
        }

        public async Task<IEnumerable<ClientVariationsTradesTradeDTO>> GetClientVariationsTradesTradesByTradeIdAsync(int tradeId)
        {
            // Check if the trade exists
            var trade = await _context.Trades
                .Include(t => t.Project)
                .FirstOrDefaultAsync(t => t.TradeId == tradeId);
                
            if (trade == null)
                throw new KeyNotFoundException($"Trade with ID {tradeId} not found");
                
            var clientVariationsTradesTrades = await _context.ClientVariationsTradesTrades
                .Where(c => c.TradeId == tradeId)
                .ToListAsync();
                
            var result = new List<ClientVariationsTradesTradeDTO>();
            
            foreach (var cvtt in clientVariationsTradesTrades)
            {
                var dto = new ClientVariationsTradesTradeDTO
                {
                    ClientVariationTradeId = cvtt.ClientVariationTradeId,
                    TradeId = cvtt.TradeId,
                    BudgetAmount = cvtt.BudgetAmount,
                    BudgetAmountInitial = cvtt.BudgetAmountInitial,
                    BudgetAmountAllowance = cvtt.BudgetAmountAllowance,
                    BudgetAmountTradeInitial = cvtt.BudgetAmountTradeInitial,
                    BudgetDate = cvtt.BudgetDate,
                    TradeName = trade.Name,
                    ProjectName = trade.Project?.Name
                };
                
                // Get ClientVariationTrade name
                var clientVariationTrade = await _context.ClientVariationsTrades.FindAsync(cvtt.ClientVariationTradeId);
                if (clientVariationTrade != null)
                {
                    dto.ClientVariationTradeName = clientVariationTrade.TradeCode;
                }
                
                result.Add(dto);
            }
            
            return result;
        }

        public async Task<ClientVariationsTradesTradeDTO> CreateClientVariationsTradesTradeAsync(ClientVariationsTradesTradeCreateDTO clientVariationsTradesTradeDto)
        {
            // Check if the client variation trade exists
            var clientVariationTrade = await _context.ClientVariationsTrades.FindAsync(clientVariationsTradesTradeDto.ClientVariationTradeId);
            if (clientVariationTrade == null)
                throw new KeyNotFoundException($"ClientVariationTrade with ID {clientVariationsTradesTradeDto.ClientVariationTradeId} not found");
                
            // Check if the trade exists
            var trade = await _context.Trades.FindAsync(clientVariationsTradesTradeDto.TradeId);
            if (trade == null)
                throw new KeyNotFoundException($"Trade with ID {clientVariationsTradesTradeDto.TradeId} not found");
                
            // Check if the client variation trade and trade are already linked
            var existingLink = await _context.ClientVariationsTradesTrades
                .FirstOrDefaultAsync(c => c.ClientVariationTradeId == clientVariationsTradesTradeDto.ClientVariationTradeId && c.TradeId == clientVariationsTradesTradeDto.TradeId);
                
            if (existingLink != null)
                throw new InvalidOperationException($"ClientVariationTrade with ID {clientVariationsTradesTradeDto.ClientVariationTradeId} is already linked to Trade with ID {clientVariationsTradesTradeDto.TradeId}");
                
            var clientVariationsTradesTrade = new ClientVariationsTradesTrade
            {
                ClientVariationTradeId = clientVariationsTradesTradeDto.ClientVariationTradeId,
                TradeId = clientVariationsTradesTradeDto.TradeId,
                BudgetAmount = clientVariationsTradesTradeDto.BudgetAmount,
                BudgetAmountInitial = clientVariationsTradesTradeDto.BudgetAmountInitial,
                BudgetAmountAllowance = clientVariationsTradesTradeDto.BudgetAmountAllowance,
                BudgetAmountTradeInitial = clientVariationsTradesTradeDto.BudgetAmountTradeInitial,
                BudgetDate = clientVariationsTradesTradeDto.BudgetDate,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow,
                CreatedPeopleId = _userContextService.GetCurrentUserId(),
                ModifiedPeopleId = _userContextService.GetCurrentUserId()
            };
            
            _context.ClientVariationsTradesTrades.Add(clientVariationsTradesTrade);
            await _context.SaveChangesAsync();
            
            return await GetClientVariationsTradesTradeByIdsAsync(clientVariationsTradesTrade.ClientVariationTradeId, clientVariationsTradesTrade.TradeId);
        }

        public async Task<ClientVariationsTradesTradeDTO> UpdateClientVariationsTradesTradeAsync(int clientVariationTradeId, int tradeId, ClientVariationsTradesTradeUpdateDTO clientVariationsTradesTradeDto)
        {
            var clientVariationsTradesTrade = await _context.ClientVariationsTradesTrades
                .FirstOrDefaultAsync(c => c.ClientVariationTradeId == clientVariationTradeId && c.TradeId == tradeId);
            
            if (clientVariationsTradesTrade == null)
                throw new KeyNotFoundException($"ClientVariationsTradesTrade with ClientVariationTradeId {clientVariationTradeId} and TradeId {tradeId} not found");
                
            clientVariationsTradesTrade.BudgetAmount = clientVariationsTradesTradeDto.BudgetAmount;
            clientVariationsTradesTrade.BudgetAmountInitial = clientVariationsTradesTradeDto.BudgetAmountInitial;
            clientVariationsTradesTrade.BudgetAmountAllowance = clientVariationsTradesTradeDto.BudgetAmountAllowance;
            clientVariationsTradesTrade.BudgetAmountTradeInitial = clientVariationsTradesTradeDto.BudgetAmountTradeInitial;
            clientVariationsTradesTrade.BudgetDate = clientVariationsTradesTradeDto.BudgetDate;
            clientVariationsTradesTrade.ModifiedDate = DateTime.UtcNow;
            clientVariationsTradesTrade.ModifiedPeopleId = _userContextService.GetCurrentUserId();
            
            await _context.SaveChangesAsync();
            
            return await GetClientVariationsTradesTradeByIdsAsync(clientVariationTradeId, tradeId);
        }

        public async Task<bool> DeleteClientVariationsTradesTradeAsync(int clientVariationTradeId, int tradeId)
        {
            var clientVariationsTradesTrade = await _context.ClientVariationsTradesTrades
                .FirstOrDefaultAsync(c => c.ClientVariationTradeId == clientVariationTradeId && c.TradeId == tradeId);
            
            if (clientVariationsTradesTrade == null)
                return false;
                
            _context.ClientVariationsTradesTrades.Remove(clientVariationsTradesTrade);
            await _context.SaveChangesAsync();
            
            return true;
        }
    }
}
