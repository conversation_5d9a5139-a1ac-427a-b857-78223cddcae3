﻿using System;
using System.Collections.Generic;

namespace NOBLE.API.Models.Generated;

public partial class ClientVariationsDetail
{
    public int ClientVariationDetailId { get; set; }

    public int? ClientVariationId { get; set; }

    public decimal? Amount { get; set; }

    public string? Description { get; set; }

    public int ModifiedPeopleId { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public int CreatedPeopleId { get; set; }

    public int? DisplayOrder { get; set; }

    public virtual ClientVariation? ClientVariation { get; set; }
}
