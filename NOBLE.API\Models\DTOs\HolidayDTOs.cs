using System.ComponentModel.DataAnnotations;

namespace NOBLE.API.Models.DTOs
{
    public class HolidayDTO
    {
        public DateTime HolidayDate { get; set; }
        
        // Additional properties for display purposes
        public string DayOfWeek => HolidayDate.DayOfWeek.ToString();
        public string FormattedDate => HolidayDate.ToString("yyyy-MM-dd");
    }

    public class HolidayCreateDTO
    {
        [Required]
        public DateTime HolidayDate { get; set; }
    }
}
