using NOBLE.API.Models.DTOs;

namespace NOBLE.API.Services.Interfaces
{
    public interface IProcessService
    {
        Task<IEnumerable<ProcessDTO>> GetAllProcessesAsync();
        Task<ProcessDTO> GetProcessByIdAsync(int id);
        Task<IEnumerable<ProcessDTO>> GetProcessesByTemplateTypeAsync(string templateType);
        Task<IEnumerable<ProcessDTO>> GetVisibleProcessesAsync();
        Task<ProcessDTO> CreateProcessAsync(ProcessCreateDTO processDto);
        Task<ProcessDTO> UpdateProcessAsync(int id, ProcessUpdateDTO processDto);
        Task<bool> DeleteProcessAsync(int id);
    }
}
