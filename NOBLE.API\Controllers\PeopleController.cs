using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class PeopleController : ControllerBase
    {
        private readonly IPersonService _personService;
        
        public PeopleController(IPersonService personService)
        {
            _personService = personService;
        }
        
        // GET: api/People
        [HttpGet]
        public async Task<ActionResult<IEnumerable<PersonDTO>>> GetPeople()
        {
            var people = await _personService.GetAllPeopleAsync();
            return Ok(people);
        }
        
        // GET: api/People/5
        [HttpGet("{id}")]
        public async Task<ActionResult<PersonDTO>> GetPerson(int id)
        {
            try
            {
                var person = await _personService.GetPersonByIdAsync(id);
                return Ok(person);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // GET: api/People/BusinessUnit/5
        [HttpGet("BusinessUnit/{businessUnitId}")]
        public async Task<ActionResult<IEnumerable<PersonDTO>>> GetPeopleByBusinessUnit(int businessUnitId)
        {
            try
            {
                var people = await _personService.GetPeopleByBusinessUnitIdAsync(businessUnitId);
                return Ok(people);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // GET: api/People/SubContractor/5
        [HttpGet("SubContractor/{subContractorId}")]
        public async Task<ActionResult<IEnumerable<PersonDTO>>> GetPeopleBySubContractor(int subContractorId)
        {
            try
            {
                var people = await _personService.GetPeopleBySubContractorIdAsync(subContractorId);
                return Ok(people);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // GET: api/People/Type/E
        [HttpGet("Type/{type}")]
        public async Task<ActionResult<IEnumerable<PersonDTO>>> GetPeopleByType(string type)
        {
            var people = await _personService.GetPeopleByTypeAsync(type);
            return Ok(people);
        }
        
        // POST: api/People
        [HttpPost]
        public async Task<ActionResult<PersonDTO>> CreatePerson(PersonCreateDTO personDto)
        {
            try
            {
                var createdPerson = await _personService.CreatePersonAsync(personDto);
                return CreatedAtAction(nameof(GetPerson), new { id = createdPerson.PeopleId }, createdPerson);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // PUT: api/People/5
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdatePerson(int id, PersonUpdateDTO personDto)
        {
            try
            {
                var updatedPerson = await _personService.UpdatePersonAsync(id, personDto);
                return Ok(updatedPerson);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // DELETE: api/People/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeletePerson(int id)
        {
            try
            {
                var result = await _personService.DeletePersonAsync(id);
                
                if (!result)
                    return NotFound(new { message = $"Person with ID {id} not found" });
                    
                return NoContent();
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
    }
}
