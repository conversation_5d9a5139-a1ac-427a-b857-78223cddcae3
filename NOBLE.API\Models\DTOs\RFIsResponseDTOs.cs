using System.ComponentModel.DataAnnotations;

namespace NOBLE.API.Models.DTOs
{
    public class RFIsResponseDTO
    {
        public int ResponseId { get; set; }
        public int RFIId { get; set; }
        public int? RFINumber { get; set; }
        public int? ResponseNumber { get; set; }
        public string? ResponseFrom { get; set; }
        public string? ResponseMessage { get; set; }
        public DateTime? ResponseDate { get; set; }
        public string? ResponseFolderPath { get; set; }
        public DateTime? CreatedDate { get; set; }
        public string? CreatedBy { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string? ModifiedBy { get; set; }
        
        // Navigation properties for display purposes
        public string? RFISubject { get; set; }
        public string? ProjectName { get; set; }
        public int AttachmentCount { get; set; }
    }

    public class RFIsResponseCreateDTO
    {
        [Required]
        public int RFIId { get; set; }
        
        public int? RFINumber { get; set; }
        public int? ResponseNumber { get; set; }
        
        [StringLength(100)]
        public string? ResponseFrom { get; set; }
        
        public string? ResponseMessage { get; set; }
        
        public DateTime? ResponseDate { get; set; }
        
        [StringLength(255)]
        public string? ResponseFolderPath { get; set; }
    }

    public class RFIsResponseUpdateDTO
    {
        public int? RFINumber { get; set; }
        public int? ResponseNumber { get; set; }
        
        [StringLength(100)]
        public string? ResponseFrom { get; set; }
        
        public string? ResponseMessage { get; set; }
        
        public DateTime? ResponseDate { get; set; }
        
        [StringLength(255)]
        public string? ResponseFolderPath { get; set; }
    }
}
