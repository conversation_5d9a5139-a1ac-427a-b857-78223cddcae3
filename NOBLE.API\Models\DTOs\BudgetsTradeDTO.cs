using System.ComponentModel.DataAnnotations;

namespace NOBLE.API.Models.DTOs
{
    public class BudgetsTradeDTO
    {
        public int BudgetId { get; set; }
        public int TradeId { get; set; }
        public decimal? BudgetAmount { get; set; }
        public decimal? BudgetAmountInitial { get; set; }
        public decimal? BudgetAmountAllowance { get; set; }
        public decimal? BudgetAmountTradeInitial { get; set; }
        public DateTime? BudgetDate { get; set; }
        
        // Navigation properties for display purposes
        public string? BudgetCode { get; set; }
        public string? TradeName { get; set; }
        public string? ProjectName { get; set; }
    }

    public class BudgetsTradeCreateDTO
    {
        [Required]
        public int BudgetId { get; set; }
        
        [Required]
        public int TradeId { get; set; }
        
        public decimal? BudgetAmount { get; set; }
        public decimal? BudgetAmountInitial { get; set; }
        public decimal? BudgetAmountAllowance { get; set; }
        public decimal? BudgetAmountTradeInitial { get; set; }
        public DateTime? BudgetDate { get; set; }
    }

    public class BudgetsTradeUpdateDTO
    {
        public decimal? BudgetAmount { get; set; }
        public decimal? BudgetAmountInitial { get; set; }
        public decimal? BudgetAmountAllowance { get; set; }
        public decimal? BudgetAmountTradeInitial { get; set; }
        public DateTime? BudgetDate { get; set; }
    }
}
