﻿using System;
using System.Collections.Generic;

namespace NOBLE.API.Models.Generated;

public partial class RfisResponseAttachment
{
    public int Id { get; set; }

    public int? Rfiid { get; set; }

    public int? ResponseId { get; set; }

    public string? FileName { get; set; }

    public string? FileType { get; set; }

    public byte[]? Filedata { get; set; }

    public DateTime? CreatedOn { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? ModefiedOn { get; set; }

    public string? ModefiedBy { get; set; }
}
