using System.ComponentModel.DataAnnotations;

namespace NOBLE.API.Models.DTOs
{
    public class ClaimDTO
    {
        public int ClaimId { get; set; }
        public int? ProjectId { get; set; }
        public int? ProcessId { get; set; }
        public int? Number { get; set; }
        public DateTime? WriteDate { get; set; }
        public DateTime? DraftApprovalDate { get; set; }
        public DateTime? InternalApprovalDate { get; set; }
        public DateTime? ApprovalDate { get; set; }
        public DateTime? DueDate { get; set; }
        public DateTime? ClientDueDate { get; set; }
        public decimal? GoodsServicesTax { get; set; }
        public decimal? AdjustmentNoteAmount { get; set; }
        public string? AdjustmentNoteName { get; set; }
        public string? BackupFile1 { get; set; }
        public string? BackupFile2 { get; set; }
        
        // Navigation properties for display purposes
        public string? ProjectName { get; set; }
    }

    public class ClaimCreateDTO
    {
        [Required]
        public int ProjectId { get; set; }
        
        public int? ProcessId { get; set; }
        public int? Number { get; set; }
        public DateTime? WriteDate { get; set; }
        public DateTime? DraftApprovalDate { get; set; }
        public DateTime? InternalApprovalDate { get; set; }
        public DateTime? ApprovalDate { get; set; }
        public DateTime? DueDate { get; set; }
        public DateTime? ClientDueDate { get; set; }
        public decimal? GoodsServicesTax { get; set; }
        public decimal? AdjustmentNoteAmount { get; set; }
        
        [StringLength(25)]
        public string? AdjustmentNoteName { get; set; }
        
        [StringLength(255)]
        public string? BackupFile1 { get; set; }
        
        [StringLength(255)]
        public string? BackupFile2 { get; set; }
    }

    public class ClaimUpdateDTO
    {
        [Required]
        public int ProjectId { get; set; }
        
        public int? ProcessId { get; set; }
        public int? Number { get; set; }
        public DateTime? WriteDate { get; set; }
        public DateTime? DraftApprovalDate { get; set; }
        public DateTime? InternalApprovalDate { get; set; }
        public DateTime? ApprovalDate { get; set; }
        public DateTime? DueDate { get; set; }
        public DateTime? ClientDueDate { get; set; }
        public decimal? GoodsServicesTax { get; set; }
        public decimal? AdjustmentNoteAmount { get; set; }
        
        [StringLength(25)]
        public string? AdjustmentNoteName { get; set; }
        
        [StringLength(255)]
        public string? BackupFile1 { get; set; }
        
        [StringLength(255)]
        public string? BackupFile2 { get; set; }
    }
}
