using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class BusinessUnitsController : ControllerBase
    {
        private readonly IBusinessUnitService _businessUnitService;

        public BusinessUnitsController(IBusinessUnitService businessUnitService)
        {
            _businessUnitService = businessUnitService;
        }
         
        [HttpGet]
        public async Task<ActionResult<IEnumerable<BusinessUnitDTO>>> GetBusinessUnits()
        {
            var businessUnits = await _businessUnitService.GetAllBusinessUnitsAsync();
            return Ok(businessUnits);
        }
         
        [HttpGet("{id}")]
        public async Task<ActionResult<BusinessUnitDTO>> GetBusinessUnit(int id)
        {
            try
            {
                var businessUnit = await _businessUnitService.GetBusinessUnitByIdAsync(id);
                return Ok(businessUnit);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
         
        [HttpPost]
        public async Task<ActionResult<BusinessUnitDTO>> CreateBusinessUnit(BusinessUnitCreateDTO businessUnitDto)
        {
            try
            {
                var createdBusinessUnit = await _businessUnitService.CreateBusinessUnitAsync(businessUnitDto);
                return CreatedAtAction(nameof(GetBusinessUnit), new { id = createdBusinessUnit.BusinessUnitId }, createdBusinessUnit);
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
 
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateBusinessUnit(int id, BusinessUnitUpdateDTO businessUnitDto)
        {
            try
            {
                var updatedBusinessUnit = await _businessUnitService.UpdateBusinessUnitAsync(id, businessUnitDto);
                return Ok(updatedBusinessUnit);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
         
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteBusinessUnit(int id)
        {
            try
            {
                var result = await _businessUnitService.DeleteBusinessUnitAsync(id);

                if (!result)
                    return NotFound(new { message = $"Business Unit with ID {id} not found" });

                return NoContent();
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
    }
}
