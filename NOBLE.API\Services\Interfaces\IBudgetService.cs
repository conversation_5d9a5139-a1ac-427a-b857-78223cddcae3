using NOBLE.API.Models.DTOs;

namespace NOBLE.API.Services.Interfaces
{
    public interface IBudgetService
    {
        Task<IEnumerable<BudgetDTO>> GetAllBudgetsAsync();
        Task<BudgetDTO> GetBudgetByIdAsync(int id);
        Task<IEnumerable<BudgetDTO>> GetBudgetsByProjectIdAsync(int projectId);
        Task<BudgetDTO> CreateBudgetAsync(BudgetCreateDTO budgetDto);
        Task<BudgetDTO> UpdateBudgetAsync(int id, BudgetUpdateDTO budgetDto);
        Task<bool> DeleteBudgetAsync(int id);
    }
}
