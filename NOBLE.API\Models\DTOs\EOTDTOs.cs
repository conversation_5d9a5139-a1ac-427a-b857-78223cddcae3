using System.ComponentModel.DataAnnotations;

namespace NOBLE.API.Models.DTOs
{
    public class EOTDTO
    {
        public int EOTId { get; set; }
        public int ProjectId { get; set; }
        public int? Number { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public DateTime? FirstNoticeDate { get; set; }
        public DateTime? WriteDate { get; set; }
        public DateTime? SendDate { get; set; }
        public DateTime? ApprovalDate { get; set; }
        public double? DaysClaimed { get; set; }
        public double? DaysApproved { get; set; }
        public string? Cause { get; set; }
        public string? Nature { get; set; }
        public string? Period { get; set; }
        public string? Works { get; set; }
        public string? CostCode { get; set; }
        public string? Status { get; set; }
        public string? ClientApprovalFile { get; set; }
        public string? ClientBackUpFile { get; set; }
        public string? EOTType { get; set; }
        
        // Navigation properties for display purposes
        public string? ProjectName { get; set; }
    }

    public class EOTCreateDTO
    {
        [Required]
        public int ProjectId { get; set; }
        
        public int? Number { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public DateTime? FirstNoticeDate { get; set; }
        public DateTime? WriteDate { get; set; }
        public DateTime? SendDate { get; set; }
        public DateTime? ApprovalDate { get; set; }
        public double? DaysClaimed { get; set; }
        public double? DaysApproved { get; set; }
        
        [StringLength(255)]
        public string? Cause { get; set; }
        
        [StringLength(255)]
        public string? Nature { get; set; }
        
        [StringLength(255)]
        public string? Period { get; set; }
        
        [StringLength(255)]
        public string? Works { get; set; }
        
        [StringLength(50)]
        public string? CostCode { get; set; }
        
        [StringLength(50)]
        public string? Status { get; set; }
        
        [StringLength(255)]
        public string? ClientApprovalFile { get; set; }
        
        [StringLength(255)]
        public string? ClientBackUpFile { get; set; }
        
        [StringLength(50)]
        public string? EOTType { get; set; }
    }

    public class EOTUpdateDTO
    {
        [Required]
        public int ProjectId { get; set; }
        
        public int? Number { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public DateTime? FirstNoticeDate { get; set; }
        public DateTime? WriteDate { get; set; }
        public DateTime? SendDate { get; set; }
        public DateTime? ApprovalDate { get; set; }
        public double? DaysClaimed { get; set; }
        public double? DaysApproved { get; set; }
        
        [StringLength(255)]
        public string? Cause { get; set; }
        
        [StringLength(255)]
        public string? Nature { get; set; }
        
        [StringLength(255)]
        public string? Period { get; set; }
        
        [StringLength(255)]
        public string? Works { get; set; }
        
        [StringLength(50)]
        public string? CostCode { get; set; }
        
        [StringLength(50)]
        public string? Status { get; set; }
        
        [StringLength(255)]
        public string? ClientApprovalFile { get; set; }
        
        [StringLength(255)]
        public string? ClientBackUpFile { get; set; }
        
        [StringLength(50)]
        public string? EOTType { get; set; }
    }
}
