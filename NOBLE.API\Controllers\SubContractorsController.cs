using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class SubContractorsController : ControllerBase
    {
        private readonly ISubContractorService _subContractorService;
        
        public SubContractorsController(ISubContractorService subContractorService)
        {
            _subContractorService = subContractorService;
        }
        
        // GET: api/SubContractors
        [HttpGet]
        public async Task<ActionResult<IEnumerable<SubContractorDTO>>> GetSubContractors()
        {
            var subContractors = await _subContractorService.GetAllSubContractorsAsync();
            return Ok(subContractors);
        }
        
        // GET: api/SubContractors/5
        [HttpGet("{id}")]
        public async Task<ActionResult<SubContractorDTO>> GetSubContractor(int id)
        {
            try
            {
                var subContractor = await _subContractorService.GetSubContractorByIdAsync(id);
                return Ok(subContractor);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // GET: api/SubContractors/BusinessUnit/5
        [HttpGet("BusinessUnit/{businessUnitId}")]
        public async Task<ActionResult<IEnumerable<SubContractorDTO>>> GetSubContractorsByBusinessUnit(int businessUnitId)
        {
            try
            {
                var subContractors = await _subContractorService.GetSubContractorsByBusinessUnitIdAsync(businessUnitId);
                return Ok(subContractors);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // POST: api/SubContractors
        [HttpPost]
        public async Task<ActionResult<SubContractorDTO>> CreateSubContractor(SubContractorCreateDTO subContractorDto)
        {
            try
            {
                var createdSubContractor = await _subContractorService.CreateSubContractorAsync(subContractorDto);
                return CreatedAtAction(nameof(GetSubContractor), new { id = createdSubContractor.SubContractorId }, createdSubContractor);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // PUT: api/SubContractors/5
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateSubContractor(int id, SubContractorUpdateDTO subContractorDto)
        {
            try
            {
                var updatedSubContractor = await _subContractorService.UpdateSubContractorAsync(id, subContractorDto);
                return Ok(updatedSubContractor);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // DELETE: api/SubContractors/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteSubContractor(int id)
        {
            try
            {
                var result = await _subContractorService.DeleteSubContractorAsync(id);
                
                if (!result)
                    return NotFound(new { message = $"SubContractor with ID {id} not found" });
                    
                return NoContent();
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
    }
}
