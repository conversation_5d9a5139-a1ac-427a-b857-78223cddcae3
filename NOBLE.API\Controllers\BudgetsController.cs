using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class BudgetsController : ControllerBase
    {
        private readonly IBudgetService _budgetService;
        
        public BudgetsController(IBudgetService budgetService)
        {
            _budgetService = budgetService;
        }
         
        [HttpGet]
        public async Task<ActionResult<IEnumerable<BudgetDTO>>> GetBudgets()
        {
            var budgets = await _budgetService.GetAllBudgetsAsync();
            return Ok(budgets);
        }
         
        [HttpGet("{id}")]
        public async Task<ActionResult<BudgetDTO>> GetBudget(int id)
        {
            try
            {
                var budget = await _budgetService.GetBudgetByIdAsync(id);
                return Ok(budget);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
         
        [HttpGet("Project/{projectId}")]
        public async Task<ActionResult<IEnumerable<BudgetDTO>>> GetBudgetsByProject(int projectId)
        {
            try
            {
                var budgets = await _budgetService.GetBudgetsByProjectIdAsync(projectId);
                return Ok(budgets);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
         
        [HttpPost]
        public async Task<ActionResult<BudgetDTO>> CreateBudget(BudgetCreateDTO budgetDto)
        {
            try
            {
                var createdBudget = await _budgetService.CreateBudgetAsync(budgetDto);
                return CreatedAtAction(nameof(GetBudget), new { id = createdBudget.BudgetId }, createdBudget);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
         
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateBudget(int id, BudgetUpdateDTO budgetDto)
        {
            try
            {
                var updatedBudget = await _budgetService.UpdateBudgetAsync(id, budgetDto);
                return Ok(updatedBudget);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
         
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteBudget(int id)
        {
            try
            {
                var result = await _budgetService.DeleteBudgetAsync(id);
                
                if (!result)
                    return NotFound(new { message = $"Budget with ID {id} not found" });
                    
                return NoContent();
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
    }
}
