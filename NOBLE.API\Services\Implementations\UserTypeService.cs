using Microsoft.EntityFrameworkCore;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Models.Generated;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Services.Implementations
{
    public class UserTypeService : IUserTypeService
    {
        private readonly NobleDbContext _context;
        private readonly IUserContextService _userContextService;

        public UserTypeService(NobleDbContext context, IUserContextService userContextService)
        {
            _context = context;
            _userContextService = userContextService;
        }

        public async Task<IEnumerable<UserTypeDTO>> GetAllUserTypesAsync()
        {
            var userTypes = await _context.UserTypes.ToListAsync();
            var result = new List<UserTypeDTO>();
            
            foreach (var userType in userTypes)
            {
                var dto = new UserTypeDTO
                {
                    UserTypeId = userType.UserTypeId,
                    Name = userType.Name,
                    Description = userType.Description,
                    CreatedDate = userType.CreatedDate,
                    ModifiedDate = userType.ModifiedDate
                };
                
                // Get created by person name
                if (userType.CreatedPeopleId.HasValue)
                {
                    var createdByPerson = await _context.People.FindAsync(userType.CreatedPeopleId.Value);
                    if (createdByPerson != null)
                    {
                        dto.CreatedByName = $"{createdByPerson.FirstName} {createdByPerson.LastName}".Trim();
                    }
                }
                
                // Get modified by person name
                if (userType.ModifiedPeopleId.HasValue)
                {
                    var modifiedByPerson = await _context.People.FindAsync(userType.ModifiedPeopleId.Value);
                    if (modifiedByPerson != null)
                    {
                        dto.ModifiedByName = $"{modifiedByPerson.FirstName} {modifiedByPerson.LastName}".Trim();
                    }
                }
                
                result.Add(dto);
            }
            
            return result;
        }

        public async Task<UserTypeDTO> GetUserTypeByIdAsync(int id)
        {
            var userType = await _context.UserTypes.FindAsync(id);
            
            if (userType == null)
                throw new KeyNotFoundException($"UserType with ID {id} not found");
                
            var dto = new UserTypeDTO
            {
                UserTypeId = userType.UserTypeId,
                Name = userType.Name,
                Description = userType.Description,
                CreatedDate = userType.CreatedDate,
                ModifiedDate = userType.ModifiedDate
            };
            
            // Get created by person name
            if (userType.CreatedPeopleId.HasValue)
            {
                var createdByPerson = await _context.People.FindAsync(userType.CreatedPeopleId.Value);
                if (createdByPerson != null)
                {
                    dto.CreatedByName = $"{createdByPerson.FirstName} {createdByPerson.LastName}".Trim();
                }
            }
            
            // Get modified by person name
            if (userType.ModifiedPeopleId.HasValue)
            {
                var modifiedByPerson = await _context.People.FindAsync(userType.ModifiedPeopleId.Value);
                if (modifiedByPerson != null)
                {
                    dto.ModifiedByName = $"{modifiedByPerson.FirstName} {modifiedByPerson.LastName}".Trim();
                }
            }
            
            return dto;
        }

        public async Task<UserTypeDTO> CreateUserTypeAsync(UserTypeCreateDTO userTypeDto)
        {
            // Check if a user type with the same name already exists
            var existingUserType = await _context.UserTypes.FirstOrDefaultAsync(ut => ut.Name == userTypeDto.Name);
            if (existingUserType != null)
                throw new InvalidOperationException($"UserType with name '{userTypeDto.Name}' already exists");
                
            var currentUserId = _userContextService.GetCurrentUserId();
            
            var userType = new UserType
            {
                Name = userTypeDto.Name,
                Description = userTypeDto.Description,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow,
                CreatedPeopleId = currentUserId,
                ModifiedPeopleId = currentUserId
            };
            
            _context.UserTypes.Add(userType);
            await _context.SaveChangesAsync();
            
            return await GetUserTypeByIdAsync(userType.UserTypeId);
        }

        public async Task<UserTypeDTO> UpdateUserTypeAsync(int id, UserTypeUpdateDTO userTypeDto)
        {
            var userType = await _context.UserTypes.FindAsync(id);
            
            if (userType == null)
                throw new KeyNotFoundException($"UserType with ID {id} not found");
                
            // Check if the name is being changed and if a user type with the new name already exists
            if (userTypeDto.Name != null && userTypeDto.Name != userType.Name)
            {
                var existingUserType = await _context.UserTypes.FirstOrDefaultAsync(ut => ut.Name == userTypeDto.Name);
                if (existingUserType != null)
                    throw new InvalidOperationException($"UserType with name '{userTypeDto.Name}' already exists");
                    
                userType.Name = userTypeDto.Name;
            }
            
            if (userTypeDto.Description != null)
                userType.Description = userTypeDto.Description;
                
            userType.ModifiedDate = DateTime.UtcNow;
            userType.ModifiedPeopleId = _userContextService.GetCurrentUserId();
            
            await _context.SaveChangesAsync();
            
            return await GetUserTypeByIdAsync(id);
        }

        public async Task<bool> DeleteUserTypeAsync(int id)
        {
            var userType = await _context.UserTypes.FindAsync(id);
            
            if (userType == null)
                return false;
                
            // Check if the user type is referenced by any people
            var peopleUsingUserType = await _context.People.AnyAsync(p => p.UserType == userType.Name);
            if (peopleUsingUserType)
                throw new InvalidOperationException($"Cannot delete UserType with ID {id} because it is being used by one or more people");
                
            _context.UserTypes.Remove(userType);
            await _context.SaveChangesAsync();
            
            return true;
        }
    }
}
