using System.ComponentModel.DataAnnotations;

namespace NOBLE.API.Models.DTOs
{
    public class TradeItemCategoryDTO
    {
        public int TradeItemCategoryId { get; set; }
        public int TradeId { get; set; }
        public string? Name { get; set; }
        public string? ShortDescription { get; set; }
        public string? LongDescription { get; set; }
        public int? DisplayOrder { get; set; }
        
        // Navigation properties for display purposes
        public string? TradeName { get; set; }
        public string? ProjectName { get; set; }
        public int TradeItemsCount { get; set; }
    }

    public class TradeItemCategoryCreateDTO
    {
        [Required]
        public int TradeId { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(100)]
        public string? ShortDescription { get; set; }
        
        [StringLength(1000)]
        public string? LongDescription { get; set; }
        
        public int? DisplayOrder { get; set; }
    }

    public class TradeItemCategoryUpdateDTO
    {
        [StringLength(100)]
        public string? Name { get; set; }
        
        [StringLength(100)]
        public string? ShortDescription { get; set; }
        
        [StringLength(1000)]
        public string? LongDescription { get; set; }
        
        public int? DisplayOrder { get; set; }
    }
}
