using Microsoft.EntityFrameworkCore;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Models.Generated;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Services.Implementations
{
    public class RFIsResponseAttachmentService : IRFIsResponseAttachmentService
    {
        private readonly NobleDbContext _context;
        private readonly IUserContextService _userContextService;

        public RFIsResponseAttachmentService(NobleDbContext context, IUserContextService userContextService)
        {
            _context = context;
            _userContextService = userContextService;
        }

        public async Task<IEnumerable<RFIsResponseAttachmentDTO>> GetAllRFIsResponseAttachmentsAsync()
        {
            var attachments = await _context.RfisResponseAttachments.ToListAsync();
            var result = new List<RFIsResponseAttachmentDTO>();

            foreach (var attachment in attachments)
            {
                var dto = new RFIsResponseAttachmentDTO
                {
                    Id = attachment.Id,
                    RFIId = attachment.Rfiid,
                    ResponseId = attachment.ResponseId,
                    FileName = attachment.FileName,
                    FileType = attachment.FileType,
                    FileData = attachment.Filedata,
                    CreatedOn = attachment.CreatedOn,
                    CreatedBy = attachment.CreatedBy,
                    ModifiedOn = attachment.ModefiedOn,
                    ModifiedBy = attachment.ModefiedBy
                };

                // Get RFI subject if RFI ID is provided
                if (attachment.Rfiid.HasValue)
                {
                    var rfi = await _context.Rfis.FindAsync(attachment.Rfiid.Value);
                    if (rfi != null)
                    {
                        dto.RFISubject = rfi.Subject;
                    }
                }

                // Get Response from if Response ID is provided
                if (attachment.ResponseId.HasValue)
                {
                    var response = await _context.RfisResponses.FindAsync(attachment.ResponseId.Value);
                    if (response != null)
                    {
                        dto.ResponseFrom = response.ResponseFrom;
                    }
                }

                result.Add(dto);
            }

            return result;
        }

        public async Task<RFIsResponseAttachmentDTO> GetRFIsResponseAttachmentByIdAsync(int id)
        {
            var attachment = await _context.RfisResponseAttachments.FindAsync(id);

            if (attachment == null)
                throw new KeyNotFoundException($"RFIsResponseAttachment with ID {id} not found");

            var dto = new RFIsResponseAttachmentDTO
            {
                Id = attachment.Id,
                RFIId = attachment.Rfiid,
                ResponseId = attachment.ResponseId,
                FileName = attachment.FileName,
                FileType = attachment.FileType,
                FileData = attachment.Filedata,
                CreatedOn = attachment.CreatedOn,
                CreatedBy = attachment.CreatedBy,
                ModifiedOn = attachment.ModefiedOn,
                ModifiedBy = attachment.ModefiedBy
            };

            // Get RFI subject if RFI ID is provided
            if (attachment.Rfiid.HasValue)
            {
                var rfi = await _context.Rfis.FindAsync(attachment.Rfiid.Value);
                if (rfi != null)
                {
                    dto.RFISubject = rfi.Subject;
                }
            }

            // Get Response from if Response ID is provided
            if (attachment.ResponseId.HasValue)
            {
                var response = await _context.RfisResponses.FindAsync(attachment.ResponseId.Value);
                if (response != null)
                {
                    dto.ResponseFrom = response.ResponseFrom;
                }
            }

            return dto;
        }

        public async Task<IEnumerable<RFIsResponseAttachmentDTO>> GetRFIsResponseAttachmentsByResponseIdAsync(int responseId)
        {
            // Check if the response exists
            var response = await _context.RfisResponses.FindAsync(responseId);
            if (response == null)
                throw new KeyNotFoundException($"RFIsResponse with ID {responseId} not found");

            var attachments = await _context.RfisResponseAttachments
                .Where(a => a.ResponseId == responseId)
                .ToListAsync();

            var result = new List<RFIsResponseAttachmentDTO>();

            foreach (var attachment in attachments)
            {
                var dto = new RFIsResponseAttachmentDTO
                {
                    Id = attachment.Id,
                    RFIId = attachment.Rfiid,
                    ResponseId = attachment.ResponseId,
                    FileName = attachment.FileName,
                    FileType = attachment.FileType,
                    FileData = attachment.Filedata,
                    CreatedOn = attachment.CreatedOn,
                    CreatedBy = attachment.CreatedBy,
                    ModifiedOn = attachment.ModefiedOn,
                    ModifiedBy = attachment.ModefiedBy,
                    ResponseFrom = response.ResponseFrom
                };

                // Get RFI subject if RFI ID is provided
                if (attachment.Rfiid.HasValue)
                {
                    var rfi = await _context.Rfis.FindAsync(attachment.Rfiid.Value);
                    if (rfi != null)
                    {
                        dto.RFISubject = rfi.Subject;
                    }
                }

                result.Add(dto);
            }

            return result;
        }

        public async Task<IEnumerable<RFIsResponseAttachmentDTO>> GetRFIsResponseAttachmentsByRFIIdAsync(int rfiId)
        {
            // Check if the RFI exists
            var rfi = await _context.Rfis.FindAsync(rfiId);
            if (rfi == null)
                throw new KeyNotFoundException($"RFI with ID {rfiId} not found");

            var attachments = await _context.RfisResponseAttachments
                .Where(a => a.Rfiid == rfiId)
                .ToListAsync();

            var result = new List<RFIsResponseAttachmentDTO>();

            foreach (var attachment in attachments)
            {
                var dto = new RFIsResponseAttachmentDTO
                {
                    Id = attachment.Id,
                    RFIId = attachment.Rfiid,
                    ResponseId = attachment.ResponseId,
                    FileName = attachment.FileName,
                    FileType = attachment.FileType,
                    FileData = attachment.Filedata,
                    CreatedOn = attachment.CreatedOn,
                    CreatedBy = attachment.CreatedBy,
                    ModifiedOn = attachment.ModefiedOn,
                    ModifiedBy = attachment.ModefiedBy,
                    RFISubject = rfi.Subject
                };

                // Get Response from if Response ID is provided
                if (attachment.ResponseId.HasValue)
                {
                    var response = await _context.RfisResponses.FindAsync(attachment.ResponseId.Value);
                    if (response != null)
                    {
                        dto.ResponseFrom = response.ResponseFrom;
                    }
                }

                result.Add(dto);
            }

            return result;
        }

        public async Task<RFIsResponseAttachmentDTO> CreateRFIsResponseAttachmentAsync(RFIsResponseAttachmentCreateDTO rfisResponseAttachmentDto)
        {
            // Check if the response exists
            var response = await _context.RfisResponses.FindAsync(rfisResponseAttachmentDto.ResponseId);
            if (response == null)
                throw new KeyNotFoundException($"RFIsResponse with ID {rfisResponseAttachmentDto.ResponseId} not found");

            // If RFI ID is not provided, get it from the response
            int? rfiId = rfisResponseAttachmentDto.RFIId;
            if (rfiId == null)
            {
                rfiId = response.Rfiid;
            }

            var currentUser = _userContextService.GetCurrentUserName();

            var attachment = new RfisResponseAttachment
            {
                Rfiid = rfiId,
                ResponseId = rfisResponseAttachmentDto.ResponseId,
                FileName = rfisResponseAttachmentDto.FileName,
                FileType = rfisResponseAttachmentDto.FileType,
                Filedata = rfisResponseAttachmentDto.FileData,
                CreatedOn = DateTime.UtcNow,
                CreatedBy = currentUser,
                ModefiedOn = DateTime.UtcNow,
                ModefiedBy = currentUser
            };

            _context.RfisResponseAttachments.Add(attachment);
            await _context.SaveChangesAsync();

            return await GetRFIsResponseAttachmentByIdAsync(attachment.Id);
        }

        public async Task<RFIsResponseAttachmentDTO> UpdateRFIsResponseAttachmentAsync(int id, RFIsResponseAttachmentUpdateDTO rfisResponseAttachmentDto)
        {
            var attachment = await _context.RfisResponseAttachments.FindAsync(id);

            if (attachment == null)
                throw new KeyNotFoundException($"RFIsResponseAttachment with ID {id} not found");

            var currentUser = _userContextService.GetCurrentUserName();

            if (rfisResponseAttachmentDto.FileName != null)
                attachment.FileName = rfisResponseAttachmentDto.FileName;

            if (rfisResponseAttachmentDto.FileType != null)
                attachment.FileType = rfisResponseAttachmentDto.FileType;

            if (rfisResponseAttachmentDto.FileData != null)
                attachment.Filedata = rfisResponseAttachmentDto.FileData;

            attachment.ModefiedOn = DateTime.UtcNow;
            attachment.ModefiedBy = currentUser;

            await _context.SaveChangesAsync();

            return await GetRFIsResponseAttachmentByIdAsync(id);
        }

        public async Task<bool> DeleteRFIsResponseAttachmentAsync(int id)
        {
            var attachment = await _context.RfisResponseAttachments.FindAsync(id);

            if (attachment == null)
                return false;

            _context.RfisResponseAttachments.Remove(attachment);
            await _context.SaveChangesAsync();

            return true;
        }
    }
}
