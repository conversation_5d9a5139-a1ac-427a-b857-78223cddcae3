using System.ComponentModel.DataAnnotations;

namespace NOBLE.API.Models.DTOs
{
    public class PersonDTO
    {
        public int PeopleId { get; set; }
        public string Type { get; set; } = string.Empty;
        public int? SubContractorId { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? Street { get; set; }
        public string? City { get; set; }
        public string? State { get; set; }
        public string? PostCode { get; set; }
        public string? Phone { get; set; }
        public string? Mobile { get; set; }
        public string? Email { get; set; }
        public string? EmployeePosition { get; set; }
        public string? UserType { get; set; }
        public string? UserLogin { get; set; }
        public string? UserSignatureFile { get; set; }
        public int? BusinessUnitId { get; set; }
        public DateTime? Dob { get; set; }
        public string? EmergencyContactName { get; set; }
        public string? EmergencyContactNumber { get; set; }
        public DateTime? UserLastLogin { get; set; }
        public bool Inactive { get; set; }
        
        // Navigation properties for display purposes
        public string? BusinessUnitName { get; set; }
        public string? SubContractorName { get; set; }
        public string FullName => $"{FirstName} {LastName}".Trim();
        public int TradesCount { get; set; }
        public int TradeParticipationsCount { get; set; }
    }

    public class PersonCreateDTO
    {
        [Required]
        [StringLength(1)]
        public string Type { get; set; } = "E"; // Default to Employee
        
        public int? SubContractorId { get; set; }
        
        [StringLength(50)]
        public string? FirstName { get; set; }
        
        [StringLength(50)]
        public string? LastName { get; set; }
        
        [StringLength(100)]
        public string? Street { get; set; }
        
        [StringLength(50)]
        public string? City { get; set; }
        
        [StringLength(50)]
        public string? State { get; set; }
        
        [StringLength(10)]
        public string? PostCode { get; set; }
        
        [StringLength(20)]
        public string? Phone { get; set; }
        
        [StringLength(20)]
        public string? Mobile { get; set; }
        
        [StringLength(100)]
        [EmailAddress]
        public string? Email { get; set; }
        
        [StringLength(50)]
        public string? EmployeePosition { get; set; }
        
        [StringLength(50)]
        public string? UserType { get; set; }
        
        [StringLength(50)]
        public string? UserLogin { get; set; }
        
        [StringLength(255)]
        public string? UserSignatureFile { get; set; }
        
        public int? BusinessUnitId { get; set; }
        
        public DateTime? Dob { get; set; }
        
        [StringLength(50)]
        public string? EmergencyContactName { get; set; }
        
        [StringLength(20)]
        public string? EmergencyContactNumber { get; set; }
        
        public bool Inactive { get; set; } = false;
    }

    public class PersonUpdateDTO
    {
        [StringLength(1)]
        public string? Type { get; set; }
        
        public int? SubContractorId { get; set; }
        
        [StringLength(50)]
        public string? FirstName { get; set; }
        
        [StringLength(50)]
        public string? LastName { get; set; }
        
        [StringLength(100)]
        public string? Street { get; set; }
        
        [StringLength(50)]
        public string? City { get; set; }
        
        [StringLength(50)]
        public string? State { get; set; }
        
        [StringLength(10)]
        public string? PostCode { get; set; }
        
        [StringLength(20)]
        public string? Phone { get; set; }
        
        [StringLength(20)]
        public string? Mobile { get; set; }
        
        [StringLength(100)]
        [EmailAddress]
        public string? Email { get; set; }
        
        [StringLength(50)]
        public string? EmployeePosition { get; set; }
        
        [StringLength(50)]
        public string? UserType { get; set; }
        
        [StringLength(50)]
        public string? UserLogin { get; set; }
        
        [StringLength(255)]
        public string? UserSignatureFile { get; set; }
        
        public int? BusinessUnitId { get; set; }
        
        public DateTime? Dob { get; set; }
        
        [StringLength(50)]
        public string? EmergencyContactName { get; set; }
        
        [StringLength(20)]
        public string? EmergencyContactNumber { get; set; }
        
        public bool? Inactive { get; set; }
    }
}
