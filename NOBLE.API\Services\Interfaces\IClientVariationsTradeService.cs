using NOBLE.API.Models.DTOs;

namespace NOBLE.API.Services.Interfaces
{
    public interface IClientVariationsTradeService
    {
        Task<IEnumerable<ClientVariationsTradeDTO>> GetAllClientVariationsTradesAsync();
        Task<ClientVariationsTradeDTO> GetClientVariationsTradeByIdAsync(int id);
        Task<IEnumerable<ClientVariationsTradeDTO>> GetClientVariationsTradesByClientVariationIdAsync(int clientVariationId);
        Task<ClientVariationsTradeDTO> CreateClientVariationsTradeAsync(ClientVariationsTradeCreateDTO clientVariationsTradeDto);
        Task<ClientVariationsTradeDTO> UpdateClientVariationsTradeAsync(int id, ClientVariationsTradeUpdateDTO clientVariationsTradeDto);
        Task<bool> DeleteClientVariationsTradeAsync(int id);
    }
}
