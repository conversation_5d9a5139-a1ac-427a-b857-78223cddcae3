using System.ComponentModel.DataAnnotations;

namespace NOBLE.API.Models.DTOs
{
    public class ProjectDTO
    {
        public int ProjectId { get; set; }
        public int BusinessUnitId { get; set; }
        public string? Name { get; set; }
        public string? Number { get; set; }
        public string? Year { get; set; }
        public string? Description { get; set; }
        public string? Street { get; set; }
        public string? City { get; set; }
        public string? State { get; set; }
        public string? PostalCode { get; set; }
        public string? AttachmentsFolder { get; set; }
        public int? DefectsLiability { get; set; }
        public string? LiquidatedDamages { get; set; }
        public string? SiteAllowances { get; set; }
        public string? Retention { get; set; }
        public string? RetentionToCertification { get; set; }
        public string? RetentionToDlp { get; set; }
        public string? Interest { get; set; }
        public string? SpecialClause { get; set; }
        public string? LawOfSubcontract { get; set; }
        public string? ProjectStatus { get; set; }
        public DateTime? CommencementDate { get; set; }
        public DateTime? CompletionDate { get; set; }
        public string? DistributionListInfo { get; set; }
        public decimal? ContractAmount { get; set; }
        public string? PaymentTerms { get; set; }
        public string? ClaimFrequency { get; set; }
        public decimal? Waranty1Amount { get; set; }
        public DateTime? Waranty1Date { get; set; }
        public decimal? Waranty2Amount { get; set; }
        public DateTime? Waranty2Date { get; set; }
        public DateTime? PracticalCompletionDate { get; set; }
        public DateTime? FirstClaimDueDate { get; set; }
        public string? Principal { get; set; }
        public string? PrincipalAbn { get; set; }
        public string? AccountName { get; set; }
        public string? Bsb { get; set; }
        public string? AccountNumber { get; set; }
        public string? Siteaddress { get; set; }
        public string? SiteSuburb { get; set; }
        public string? SiteState { get; set; }
        public string? SitePostalCode { get; set; }
         
        public string? BusinessUnitName { get; set; }
    }

    public class ProjectCreateDTO
    {
        [Required]
        public int BusinessUnitId { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [StringLength(25)]
        public string? Number { get; set; }

        [StringLength(4)]
        public string? Year { get; set; }

        [StringLength(1000)]
        public string? Description { get; set; }

        [StringLength(100)]
        public string? Street { get; set; }

        [StringLength(50)]
        public string? City { get; set; }

        [StringLength(50)]
        public string? State { get; set; }

        [StringLength(10)]
        public string? PostalCode { get; set; }

        [StringLength(250)]
        public string? AttachmentsFolder { get; set; }

        public int? DefectsLiability { get; set; }

        [StringLength(100)]
        public string? LiquidatedDamages { get; set; }

        [StringLength(100)]
        public string? SiteAllowances { get; set; }

        [StringLength(100)]
        public string? Retention { get; set; }

        [StringLength(100)]
        public string? RetentionToCertification { get; set; }

        [StringLength(100)]
        public string? RetentionToDlp { get; set; }

        [StringLength(100)]
        public string? Interest { get; set; }

        public string? SpecialClause { get; set; }

        public string? LawOfSubcontract { get; set; }

        [StringLength(2)]
        public string? ProjectStatus { get; set; }

        public DateTime? CommencementDate { get; set; }

        public DateTime? CompletionDate { get; set; }

        [StringLength(5)]
        public string? DistributionListInfo { get; set; }

        public decimal? ContractAmount { get; set; }

        [StringLength(1)]
        public string? PaymentTerms { get; set; }

        [StringLength(1)]
        public string? ClaimFrequency { get; set; }

        public decimal? Waranty1Amount { get; set; }

        public DateTime? Waranty1Date { get; set; }

        public decimal? Waranty2Amount { get; set; }

        public DateTime? Waranty2Date { get; set; }

        public DateTime? PracticalCompletionDate { get; set; }

        public DateTime? FirstClaimDueDate { get; set; }

        [StringLength(100)]
        public string? Principal { get; set; }

        [StringLength(25)]
        public string? PrincipalAbn { get; set; }

        [StringLength(250)]
        public string? AccountName { get; set; }

        [StringLength(50)]
        public string? Bsb { get; set; }

        [StringLength(50)]
        public string? AccountNumber { get; set; }

        [StringLength(150)]
        public string? Siteaddress { get; set; }

        [StringLength(50)]
        public string? SiteSuburb { get; set; }

        [StringLength(50)]
        public string? SiteState { get; set; }

        [StringLength(50)]
        public string? SitePostalCode { get; set; }
    }

    public class ProjectUpdateDTO
    {
        [Required]
        public int BusinessUnitId { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [StringLength(25)]
        public string? Number { get; set; }

        [StringLength(4)]
        public string? Year { get; set; }

        [StringLength(1000)]
        public string? Description { get; set; }

        [StringLength(100)]
        public string? Street { get; set; }

        [StringLength(50)]
        public string? City { get; set; }

        [StringLength(50)]
        public string? State { get; set; }

        [StringLength(10)]
        public string? PostalCode { get; set; }

        [StringLength(250)]
        public string? AttachmentsFolder { get; set; }

        public int? DefectsLiability { get; set; }

        [StringLength(100)]
        public string? LiquidatedDamages { get; set; }

        [StringLength(100)]
        public string? SiteAllowances { get; set; }

        [StringLength(100)]
        public string? Retention { get; set; }

        [StringLength(100)]
        public string? RetentionToCertification { get; set; }

        [StringLength(100)]
        public string? RetentionToDlp { get; set; }

        [StringLength(100)]
        public string? Interest { get; set; }

        public string? SpecialClause { get; set; }

        public string? LawOfSubcontract { get; set; }

        [StringLength(2)]
        public string? ProjectStatus { get; set; }

        public DateTime? CommencementDate { get; set; }

        public DateTime? CompletionDate { get; set; }

        [StringLength(5)]
        public string? DistributionListInfo { get; set; }

        public decimal? ContractAmount { get; set; }

        [StringLength(1)]
        public string? PaymentTerms { get; set; }

        [StringLength(1)]
        public string? ClaimFrequency { get; set; }

        public decimal? Waranty1Amount { get; set; }

        public DateTime? Waranty1Date { get; set; }

        public decimal? Waranty2Amount { get; set; }

        public DateTime? Waranty2Date { get; set; }

        public DateTime? PracticalCompletionDate { get; set; }

        public DateTime? FirstClaimDueDate { get; set; }

        [StringLength(100)]
        public string? Principal { get; set; }

        [StringLength(25)]
        public string? PrincipalAbn { get; set; }

        [StringLength(250)]
        public string? AccountName { get; set; }

        [StringLength(50)]
        public string? Bsb { get; set; }

        [StringLength(50)]
        public string? AccountNumber { get; set; }

        [StringLength(150)]
        public string? Siteaddress { get; set; }

        [StringLength(50)]
        public string? SiteSuburb { get; set; }

        [StringLength(50)]
        public string? SiteState { get; set; }

        [StringLength(50)]
        public string? SitePostalCode { get; set; }
    }
}
