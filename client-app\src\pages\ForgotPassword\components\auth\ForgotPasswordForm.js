import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import Input from '../../../../components/common/Input';
import Button from '../../../../components/common/Button';
import './css/ForgotPasswordForm.css';

const ForgotPasswordForm = ({ onSubmit }) => {
  const [email, setEmail] = useState('');
  const [error, setError] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleChange = (e) => {
    setEmail(e.target.value);
    setError('');
  };

  const validateForm = () => {
    if (!email.trim()) {
      setError('Email is required');
      return false;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('Please enter a valid email address');
      return false;
    }

    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (validateForm()) {
      setIsLoading(true);
      try {
        await onSubmit?.(email);
        setIsSubmitted(true);
      } catch (err) {
        setError(err.response?.data?.message || 'Failed to send password reset email.');
      } finally {
        setIsLoading(false);
      }
    }
  };

  return (
    <div className="forgot-password-container">
      <h3 className="form-title">Forgot Password</h3>
      <p className="form-subtitle">
        Enter your email address and we'll send you a link to reset your password
      </p>

      {isSubmitted ? (
        <>
          <div className="success-message">
            Password reset instructions have been sent to your email address.
            Please check your inbox and follow the instructions to reset your password.
          </div>
          <Link to="/login" className="back-to-login-link">Back to Login</Link>
        </>
      ) : (
        <>
          <form onSubmit={handleSubmit}>
            <Input
              label="Email Address"
              id="email"
              name="email"
              type="email"
              placeholder="Enter your email address"
              value={email}
              onChange={handleChange}
              error={error}
            />

            <Button type="submit" fullWidth disabled={isLoading}>
              {isLoading ? 'Sending...' : 'Send Reset Link'}
            </Button>
          </form>

          <Link to="/login" className="back-to-login-link">Back to Login</Link>
        </>
      )}
    </div>
  );
};

export default ForgotPasswordForm;
