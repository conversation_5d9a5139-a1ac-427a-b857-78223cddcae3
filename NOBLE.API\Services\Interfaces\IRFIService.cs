using NOBLE.API.Models.DTOs;

namespace NOBLE.API.Services.Interfaces
{
    public interface IRFIService
    {
        Task<IEnumerable<RFIDTO>> GetAllRFIsAsync();
        Task<RFIDTO> GetRFIByIdAsync(int id);
        Task<IEnumerable<RFIDTO>> GetRFIsByProjectIdAsync(int projectId);
        Task<RFIDTO> CreateRFIAsync(RFICreateDTO rfiDto);
        Task<RFIDTO> UpdateRFIAsync(int id, RFIUpdateDTO rfiDto);
        Task<bool> DeleteRFIAsync(int id);
        
        // RFI Response methods
        Task<IEnumerable<RFIResponseDTO>> GetRFIResponsesByRFIIdAsync(int rfiId);
        Task<RFIResponseDTO> GetRFIResponseByIdAsync(int id);
        Task<RFIResponseDTO> CreateRFIResponseAsync(RFIResponseCreateDTO rfiResponseDto);
        Task<RFIResponseDTO> UpdateRFIResponseAsync(int id, RFIResponseUpdateDTO rfiResponseDto);
        Task<bool> DeleteRFIResponseAsync(int id);
    }
}
