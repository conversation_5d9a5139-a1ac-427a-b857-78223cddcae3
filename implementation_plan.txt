# NOBLE API Implementation Plan

## Database Overview
The Noble database consists of 26 tables with various relationships between them. This implementation plan outlines the order in which we should implement the API endpoints for each entity, respecting the dependencies between them.

## Implementation Principles
1. Implement parent entities before child entities
2. Respect foreign key relationships
3. Implement core business entities first
4. Follow consistent patterns for all implementations:
   - Create DTOs (Data Transfer Objects)
   - Create Service Interface
   - Implement Service with proper validation
   - Create Controller with proper error handling
   - Register Service in DI container
   - Use UserContextService for current user information

## Already Implemented Entities
1. **BusinessUnits** ✓
   - Primary entity that many other entities depend on
   - No dependencies on other entities

2. **Projects** ✓
   - Depends on BusinessUnits (FK_Projects_BusinessUnits)
   - Many other entities depend on Projects

3. **Trades** ✓
   - Depends on Projects (FK_Trades_Projects)
   - Depends on JobTypes (FK_Trades_JobTypes)
   - Depends on Contracts (FK_Trades_Contracts)
   - Depends on People (FK_Trades_People, FK_Trades_People1)
   - Many other entities depend on Trades

4. **JobTypes** ✓
   - Independent entity
   - Used by Trades (already implemented)

5. **Budgets** ✓
   - Depends on Projects (FK_Budgets_Projects)
   - Used by BudgetsTrades

## Implementation Order for Remaining Entities

### Tier 1: Core Entities with Dependencies on Already Implemented Entities

6. **Claims** ✓
   - Depends on Projects (FK_Claims_Projects)

7. **ClientVariations** ✓
   - Depends on Projects (FK_ClientVariations_Projects)
   - Can have parent-child relationships (FK_ClientVariations_ClientVariations)
   - Used by ClientVariationsDetails and ClientVariationsTrades

8. **EOTs** (Extensions of Time) ✓
   - Depends on Projects (FK_EOTs_Projects)

9. **RFIs** (Requests for Information) ✓
   - Depends on Projects (FK_RFIs_Projects)
   - Used by RFIsResponse

10. **SubContractors** ✓
    - Depends on BusinessUnits (FK_SubContractors_BusinessUnits)
    - Used by TradeParticipations

11. **Contracts** ✓
    - Can have parent-child relationships (FK_Contracts_Contracts)
    - Related to Trades (FK_Contracts_Trades)

### Tier 2: Entities Dependent on Tier 1 Entities
12. **BudgetsTrades** ✓
    - Depends on Budgets (FK_BudgetsTrades_Budgets)
    - Depends on Trades (FK_BudgetsTrades_Trades)

13. **ClientVariationsDetails** ✓
    - Depends on ClientVariations (FK_ClientVariationsDetails_ClientVariations)

14. **ClientVariationsTrades** ✓
    - Depends on ClientVariations (FK_ClientVariationsTrades_ClientVariations)

15. **RFIsResponse** ✓
    - Depends on RFIs (FK_RFIsResponse_RFIs)
    - Used by RFIsResponseAttachment

16. **TradeItemCategories** ✓
    - Depends on Trades (FK_TradeItemCategories_Trades)
    - Used by TradeItems

17. **TradeParticipations** ✓
    - Depends on Trades (FK_TradeParticipations_Trades)
    - Depends on People (FK_TradeParticipations_People)
    - Can have parent-child relationships (FK_TradeParticipations_TradeParticipations)

### Tier 3: Entities Dependent on Tier 2 Entities
18. **ClientVariationsTradesTrades** ✓
    - Depends on ClientVariationsTrades and Trades

19. **RFIsResponseAttachment** ✓
    - Depends on RFIsResponse

20. **TradeItems** ✓
    - Depends on TradeItemCategories (FK_TradeItems_TradeItemCategories)

### Supporting Entities (Implement as Needed)
21. **People** ✓
    - Depends on BusinessUnits (FK_People_BusinessUnits)
    - Used by many entities

22. **Processes** ✓
    - Independent entity
    - Used by many entities

23. **ProcessSteps** ✓
    - Depends on Processes (FK_ProcessSteps_Processes)

24. **Holidays** ✓
    - Independent entity

25. **InvitationTemplates** ✓
    - Independent entity

26. **UserTypes** ✓
    - Independent entity

## Implementation Details for Each Entity

For each entity, we will:

1. Create DTOs:
   - EntityDTO (for GET responses)
   - EntityCreateDTO (for POST requests)
   - EntityUpdateDTO (for PUT requests)

2. Create Service Interface:
   - GetAllAsync()
   - GetByIdAsync(int id)
   - GetByRelatedEntityAsync(int relatedEntityId) (where applicable)
   - CreateAsync(EntityCreateDTO dto)
   - UpdateAsync(int id, EntityUpdateDTO dto)
   - DeleteAsync(int id)

3. Implement Service:
   - Validate foreign key relationships
   - Handle business logic
   - Use UserContextService for current user information
   - Proper error handling

4. Create Controller:
   - GET, POST, PUT, DELETE endpoints
   - Proper error handling and status codes

5. Register Service in DI container

## Next Steps

1. All API implementations are now complete! ✓
2. The NOBLE API is ready for use
3. All entities from the implementation plan have been successfully implemented

This plan ensures that we respect all dependencies between entities and implement the API in a logical, maintainable way.
