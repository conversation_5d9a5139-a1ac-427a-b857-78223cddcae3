using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class ClientVariationsTradesTradesController : ControllerBase
    {
        private readonly IClientVariationsTradesTradeService _clientVariationsTradesTradeService;
        
        public ClientVariationsTradesTradesController(IClientVariationsTradesTradeService clientVariationsTradesTradeService)
        {
            _clientVariationsTradesTradeService = clientVariationsTradesTradeService;
        }
        
        // GET: api/ClientVariationsTradesTrades
        [HttpGet]
        public async Task<ActionResult<IEnumerable<ClientVariationsTradesTradeDTO>>> GetClientVariationsTradesTrades()
        {
            var clientVariationsTradesTrades = await _clientVariationsTradesTradeService.GetAllClientVariationsTradesTradesAsync();
            return Ok(clientVariationsTradesTrades);
        }
        
        // GET: api/ClientVariationsTradesTrades/ClientVariationTrade/5/Trade/10
        [HttpGet("ClientVariationTrade/{clientVariationTradeId}/Trade/{tradeId}")]
        public async Task<ActionResult<ClientVariationsTradesTradeDTO>> GetClientVariationsTradesTrade(int clientVariationTradeId, int tradeId)
        {
            try
            {
                var clientVariationsTradesTrade = await _clientVariationsTradesTradeService.GetClientVariationsTradesTradeByIdsAsync(clientVariationTradeId, tradeId);
                return Ok(clientVariationsTradesTrade);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // GET: api/ClientVariationsTradesTrades/ClientVariationTrade/5
        [HttpGet("ClientVariationTrade/{clientVariationTradeId}")]
        public async Task<ActionResult<IEnumerable<ClientVariationsTradesTradeDTO>>> GetClientVariationsTradesTradesByClientVariationTrade(int clientVariationTradeId)
        {
            try
            {
                var clientVariationsTradesTrades = await _clientVariationsTradesTradeService.GetClientVariationsTradesTradesByClientVariationTradeIdAsync(clientVariationTradeId);
                return Ok(clientVariationsTradesTrades);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // GET: api/ClientVariationsTradesTrades/Trade/5
        [HttpGet("Trade/{tradeId}")]
        public async Task<ActionResult<IEnumerable<ClientVariationsTradesTradeDTO>>> GetClientVariationsTradesTradesByTrade(int tradeId)
        {
            try
            {
                var clientVariationsTradesTrades = await _clientVariationsTradesTradeService.GetClientVariationsTradesTradesByTradeIdAsync(tradeId);
                return Ok(clientVariationsTradesTrades);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // POST: api/ClientVariationsTradesTrades
        [HttpPost]
        public async Task<ActionResult<ClientVariationsTradesTradeDTO>> CreateClientVariationsTradesTrade(ClientVariationsTradesTradeCreateDTO clientVariationsTradesTradeDto)
        {
            try
            {
                var createdClientVariationsTradesTrade = await _clientVariationsTradesTradeService.CreateClientVariationsTradesTradeAsync(clientVariationsTradesTradeDto);
                return CreatedAtAction(
                    nameof(GetClientVariationsTradesTrade), 
                    new { clientVariationTradeId = createdClientVariationsTradesTrade.ClientVariationTradeId, tradeId = createdClientVariationsTradesTrade.TradeId }, 
                    createdClientVariationsTradesTrade);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // PUT: api/ClientVariationsTradesTrades/ClientVariationTrade/5/Trade/10
        [HttpPut("ClientVariationTrade/{clientVariationTradeId}/Trade/{tradeId}")]
        public async Task<IActionResult> UpdateClientVariationsTradesTrade(int clientVariationTradeId, int tradeId, ClientVariationsTradesTradeUpdateDTO clientVariationsTradesTradeDto)
        {
            try
            {
                var updatedClientVariationsTradesTrade = await _clientVariationsTradesTradeService.UpdateClientVariationsTradesTradeAsync(clientVariationTradeId, tradeId, clientVariationsTradesTradeDto);
                return Ok(updatedClientVariationsTradesTrade);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // DELETE: api/ClientVariationsTradesTrades/ClientVariationTrade/5/Trade/10
        [HttpDelete("ClientVariationTrade/{clientVariationTradeId}/Trade/{tradeId}")]
        public async Task<IActionResult> DeleteClientVariationsTradesTrade(int clientVariationTradeId, int tradeId)
        {
            try
            {
                var result = await _clientVariationsTradesTradeService.DeleteClientVariationsTradesTradeAsync(clientVariationTradeId, tradeId);
                
                if (!result)
                    return NotFound(new { message = $"ClientVariationsTradesTrade with ClientVariationTradeId {clientVariationTradeId} and TradeId {tradeId} not found" });
                    
                return NoContent();
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
    }
}
