﻿using System;
using System.Collections.Generic;

namespace NOBLE.API.Models.Generated;

public partial class Contract
{
    public int ContractId { get; set; }

    public int? ParentContractId { get; set; }

    public int? ProcessId { get; set; }

    public int? TradeId { get; set; }

    public string? Template { get; set; }

    public int? Status { get; set; }

    public string? Number { get; set; }

    public DateTime? WriteDate { get; set; }

    public DateTime? ApprovalDate { get; set; }

    public int? OrderApprovalDate { get; set; }

    public bool? CheckQuotes { get; set; }

    public bool? CheckWinningQuote { get; set; }

    public bool? CheckComparison { get; set; }

    public bool? CheckCheckList { get; set; }

    public int? CheckPrelettingMinutes { get; set; }

    public int? SubcontractNumber { get; set; }

    public string? SiteInstruction { get; set; }

    public DateTime? SiteInstructionDate { get; set; }

    public string? SubcontractorReference { get; set; }

    public DateTime? SubcontractorReferenceDate { get; set; }

    public decimal? GoodsServicesTax { get; set; }

    public string? Description { get; set; }

    public string? Comments { get; set; }

    public string? QuotesFile { get; set; }

    public int? CheckAmendments { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public int CreatedPeopleId { get; set; }

    public int ModifiedPeopleId { get; set; }

    public virtual ICollection<Contract> InverseParentContract { get; set; } = new List<Contract>();

    public virtual Contract? ParentContract { get; set; }

    public virtual Trade? Trade { get; set; }

    public virtual ICollection<Trade> Trades { get; set; } = new List<Trade>();
}
