using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class RFIsResponsesController : ControllerBase
    {
        private readonly IRFIsResponseService _rfisResponseService;
        
        public RFIsResponsesController(IRFIsResponseService rfisResponseService)
        {
            _rfisResponseService = rfisResponseService;
        }
        
        // GET: api/RFIsResponses
        [HttpGet]
        public async Task<ActionResult<IEnumerable<RFIsResponseDTO>>> GetRFIsResponses()
        {
            var rfisResponses = await _rfisResponseService.GetAllRFIsResponsesAsync();
            return Ok(rfisResponses);
        }
        
        // GET: api/RFIsResponses/5
        [HttpGet("{id}")]
        public async Task<ActionResult<RFIsResponseDTO>> GetRFIsResponse(int id)
        {
            try
            {
                var rfisResponse = await _rfisResponseService.GetRFIsResponseByIdAsync(id);
                return Ok(rfisResponse);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // GET: api/RFIsResponses/RFI/5
        [HttpGet("RFI/{rfiId}")]
        public async Task<ActionResult<IEnumerable<RFIsResponseDTO>>> GetRFIsResponsesByRFI(int rfiId)
        {
            try
            {
                var rfisResponses = await _rfisResponseService.GetRFIsResponsesByRFIIdAsync(rfiId);
                return Ok(rfisResponses);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // POST: api/RFIsResponses
        [HttpPost]
        public async Task<ActionResult<RFIsResponseDTO>> CreateRFIsResponse(RFIsResponseCreateDTO rfisResponseDto)
        {
            try
            {
                var createdRFIsResponse = await _rfisResponseService.CreateRFIsResponseAsync(rfisResponseDto);
                return CreatedAtAction(nameof(GetRFIsResponse), new { id = createdRFIsResponse.ResponseId }, createdRFIsResponse);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // PUT: api/RFIsResponses/5
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateRFIsResponse(int id, RFIsResponseUpdateDTO rfisResponseDto)
        {
            try
            {
                var updatedRFIsResponse = await _rfisResponseService.UpdateRFIsResponseAsync(id, rfisResponseDto);
                return Ok(updatedRFIsResponse);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // DELETE: api/RFIsResponses/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteRFIsResponse(int id)
        {
            try
            {
                var result = await _rfisResponseService.DeleteRFIsResponseAsync(id);
                
                if (!result)
                    return NotFound(new { message = $"RFIsResponse with ID {id} not found" });
                    
                return NoContent();
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
    }
}
