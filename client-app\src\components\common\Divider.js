import React from 'react';
import styled from 'styled-components';
import ColorPalette from '../../styles/ColorPalette';
import Typography from '../../styles/Typography';

const DividerContainer = styled.div`
  display: flex;
  align-items: center;
  margin: 20px 0;
  width: 100%;
`;

const Line = styled.div`
  flex: 1;
  height: 1px;
  background-color: #E5E7EB;
`;

const Text = styled.span`
  font-family: 'Poppins', sans-serif;
  font-size: 13px;
  color: #9CA3AF;
  padding: 0 16px;
`;

const Divider = ({ text }) => {
  return (
    <DividerContainer>
      <Line />
      {text && <Text>{text}</Text>}
      <Line />
    </DividerContainer>
  );
};

export default Divider;
