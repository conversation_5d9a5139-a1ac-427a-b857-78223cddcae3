using NOBLE.API.Models.DTOs;

namespace NOBLE.API.Services.Interfaces
{
    public interface IRFIsResponseService
    {
        Task<IEnumerable<RFIsResponseDTO>> GetAllRFIsResponsesAsync();
        Task<RFIsResponseDTO> GetRFIsResponseByIdAsync(int id);
        Task<IEnumerable<RFIsResponseDTO>> GetRFIsResponsesByRFIIdAsync(int rfiId);
        Task<RFIsResponseDTO> CreateRFIsResponseAsync(RFIsResponseCreateDTO rfisResponseDto);
        Task<RFIsResponseDTO> UpdateRFIsResponseAsync(int id, RFIsResponseUpdateDTO rfisResponseDto);
        Task<bool> DeleteRFIsResponseAsync(int id);
    }
}
