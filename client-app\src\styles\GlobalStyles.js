import { createGlobalStyle } from 'styled-components';
import ColorPalette from './ColorPalette';
import Typography from './Typography';

const GlobalStyles = createGlobalStyle`
  @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Poppins:wght@300;400;500;600;700&display=swap');

  * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }

  html, body {
    height: 100%;
    width: 100%;
  }

  body {
    font-family: ${Typography.fontFamily.primary};
    font-size: ${Typography.fontSize.md};
    line-height: ${Typography.lineHeight.normal};
    color: ${ColorPalette.text.primary};
    background-color: ${ColorPalette.background.default};
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  #root {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: ${Typography.fontFamily.secondary};
    margin: 0;
  }

  h1 {
    font-size: ${Typography.heading.h1.fontSize};
    font-weight: ${Typography.heading.h1.fontWeight};
    line-height: ${Typography.heading.h1.lineHeight};
    margin-bottom: ${Typography.heading.h1.marginBottom};
  }

  h2 {
    font-size: ${Typography.heading.h2.fontSize};
    font-weight: ${Typography.heading.h2.fontWeight};
    line-height: ${Typography.heading.h2.lineHeight};
    margin-bottom: ${Typography.heading.h2.marginBottom};
  }

  h3 {
    font-size: ${Typography.heading.h3.fontSize};
    font-weight: ${Typography.heading.h3.fontWeight};
    line-height: ${Typography.heading.h3.lineHeight};
    margin-bottom: ${Typography.heading.h3.marginBottom};
  }

  p {
    margin-bottom: 1rem;
  }

  a {
    color: ${ColorPalette.primary.main};
    text-decoration: none;
    transition: color 0.2s ease-in-out;

    &:hover {
      color: ${ColorPalette.primary.dark};
    }
  }

  button {
    font-family: ${Typography.fontFamily.primary};
    cursor: pointer;
  }

  input, textarea, select {
    font-family: ${Typography.fontFamily.primary};
  }

  img {
    max-width: 100%;
    height: auto;
  }

  ul, ol {
    list-style: none;
  }
`;

export default GlobalStyles;
