.login-page .page-container {
  display: flex;
  height: 100vh;
  max-height: 100vh;
  overflow: hidden;
  background-color: #F5F7FA;
}

.login-page .left-section {
  flex: 0 0 70% !important;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  padding: 2rem 2rem 0 2rem;
  background-image: var(--bg-image);
  background-size: cover;
  background-position: center;
  position: relative;
  height: 100vh;
  overflow: hidden;
}

.login-page .left-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 1;
}

@media (max-width: 768px) {
  .login-page .left-section {
    display: none;
  }
}

.login-page .right-section {
  flex: 0 0 30% !important;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  padding: 2.5rem;
  background-color: white;
  height: 100vh;
  overflow: hidden;
}

@media (max-width: 768px) {
  .right-section {
    flex: 1;
    padding: 1.5rem;
    min-height: 100vh;
  }

  .page-container {
    flex-direction: column;
  }
}

.login-page .logo {
  max-width: 120px !important;
  width: 120px !important;
  position: relative;
  z-index: 2;
  margin: 0 !important;
  align-self: flex-start;
}

.login-page .hero-title {
  font-family: 'Poppins', 'Helvetica', 'Arial', sans-serif;
  font-size: 28px;
  font-weight: 700;
  color: white;
  margin-bottom: 0.5rem;
  position: relative;
  z-index: 2;
  line-height: 1.2;
}

.login-page .hero-subtitle {
  font-size: 16px;
  color: white;
  margin-bottom: 2rem;
  max-width: 400px;
  position: relative;
  z-index: 2;
  line-height: 1.4;
}

.login-page .learn-more-button {
  font-family: 'Poppins', 'Helvetica', 'Arial', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: white;
  background-color: #232526;
  border: none;
  border-radius: 4px;
  padding: 0.6rem 1.2rem;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  text-decoration: none;
  position: relative;
  z-index: 2;
  display: inline-block;
}

.login-page .learn-more-button:hover {
  background-color: #FF6600;
}

.company-logo {
  max-width: 170px;
  margin-bottom: 1.5rem;
  align-self: flex-start;
}

.login-page .middle-content {
  position: absolute;
  z-index: 2;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  width: 80%;
}

.login-page .bottom-icon {
  max-width: 180px !important;
  width: 180px !important;
  position: relative;
  z-index: 2;
  align-self: flex-start;
  margin: 0 !important;
}

.login-page button[type="submit"]:hover:not(:disabled) {
  background-color: #FF6600 !important;
}

.login-page div[class*="SocialButtonsContainer"] {
  margin-top: 16px !important;
  margin-bottom: 16px !important;
}

.login-page div[class*="CheckboxRow"] {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin-bottom: 24px !important;
}

.login-page div[class*="CheckboxRow"] > div {
  margin-bottom: 0 !important;
  display: flex !important;
  align-items: center !important;
}

.login-page div[class*="CheckboxRow"] > a {
  margin: 0 !important;
  align-self: center !important;
  line-height: 1 !important;
}
