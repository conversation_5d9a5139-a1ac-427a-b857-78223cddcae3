import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import MainLayout from '../../components/layout/MainLayout';
import DataTable from '../../components/common/DataTable';
import projectService from './services/projectService';
import './css/ProjectPage.css';

const HomeIconSvg = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
  </svg>
);

const PlusIconSvg = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
  </svg>
);

const ProjectPage = () => {
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [businessUnitFilter, setBusinessUnitFilter] = useState('All');
  const [statusFilter, setStatusFilter] = useState('All');

  useEffect(() => {
    fetchProjects();
  }, []);

  const fetchProjects = async () => {
    try {
      setLoading(true);
      const data = await projectService.getAllProjects();
      setProjects(data);
      setError('');
    } catch (err) {
      setError('Failed to load projects. Please try again.');
      console.error('Error fetching projects:', err);

      // For testing purposes, add some sample data if API fails
      setProjects([
        {
          projectId: 1,
          name: 'AussieBuild Group',
          number: '21-223',
          businessUnitName: 'VIC',
          projectStatus: 'Active',
          commencementDate: '2022-05-10',
          completionDate: '2023-02-14',
          street: 'Lot 5 Horsburgh Drive',
          city: 'Altona',
          state: 'VIC',
          postalCode: '3018'
        },
        {
          projectId: 2,
          name: 'BlueRock Constructions',
          number: '20-133',
          businessUnitName: 'QLD',
          projectStatus: 'Active',
          commencementDate: '2021-10-21',
          completionDate: '2022-06-07',
          street: '40 Barracks Road',
          city: 'Wacol',
          state: 'QLD',
          postalCode: '4076'
        },
        {
          projectId: 3,
          name: 'CoastalCore Developments',
          number: '18-827',
          businessUnitName: 'VIC',
          projectStatus: 'Active',
          commencementDate: '2019-05-20',
          completionDate: '2022-12-25',
          street: '200 Whitehall Street',
          city: 'Footscray',
          state: 'VIC',
          postalCode: '3011'
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-AU', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const getStatusBadge = (status) => {
    const statusClass = status?.toLowerCase() === 'active' ? 'status-active' : 'status-inactive';
    return <span className={`status-badge ${statusClass}`}>{status || 'Unknown'}</span>;
  };

  const columns = [
    {
      key: 'name',
      title: 'Name',
      width: '200px',
      render: (value) => <span className="project-name">{value}</span>
    },
    {
      key: 'number',
      title: 'Number',
      width: '120px'
    },
    {
      key: 'businessUnitName',
      title: 'Business Unit',
      width: '120px',
      render: (_, row) => row.businessUnitName || 'N/A'
    },
    {
      key: 'projectStatus',
      title: 'Status',
      width: '100px',
      render: (value) => getStatusBadge(value)
    },
    {
      key: 'commencementDate',
      title: 'Commenced',
      width: '120px',
      render: (value) => formatDate(value)
    },
    {
      key: 'completionDate',
      title: 'Completion',
      width: '120px',
      render: (value) => formatDate(value)
    },
    {
      key: 'address',
      title: 'Address',
      width: '200px',
      render: (_, row) => {
        const address = [row.street, row.city].filter(Boolean).join(', ');
        return address || '';
      }
    },
    {
      key: 'city',
      title: 'Suburb',
      width: '120px'
    },
    {
      key: 'state',
      title: 'State',
      width: '80px'
    },
    {
      key: 'postalCode',
      title: 'Postal Code',
      width: '100px',
      align: 'center'
    }
  ];

  const handleRowClick = (project) => {
    console.log('Project clicked:', project);
  };

  if (error) {
    return (
      <MainLayout>
        <div className="project-container">
          <div className="error-message">
            {error}
            <button onClick={fetchProjects} className="retry-btn">Retry</button>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="project-container">
        <div className="page-header">
          <div className="header-left">
            <h1 className="page-title">Projects</h1>
          </div>
          <div className="breadcrumb-container">
            <Link to="/dashboard" className="breadcrumb-item">
              <HomeIconSvg />
              Main Dashboard
            </Link>
            <span className="breadcrumb-separator">/</span>
            <span className="breadcrumb-item active">Projects</span>
          </div>
        </div>

        <DataTable
          columns={columns}
          data={projects}
          loading={loading}
          onRowClick={handleRowClick}
          pageSize={25}
          className="projects-table"
          customActions={
            <div className="table-actions">
              <button className="add-btn">
                <PlusIconSvg />
              </button>
              <div className="filter-group">
                <select
                  value={businessUnitFilter}
                  onChange={(e) => setBusinessUnitFilter(e.target.value)}
                  className="filter-select"
                >
                  <option value="All">Business Unit</option>
                  <option value="VIC">VIC</option>
                  <option value="QLD">QLD</option>
                </select>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="filter-select status-filter"
                >
                  <option value="All">Status</option>
                  <option value="Active">Active</option>
                  <option value="Inactive">Inactive</option>
                </select>
              </div>
            </div>
          }
        />

        <div className="page-footer">
          <span className="footer-brand">© NOBLE</span>
        </div>
      </div>
    </MainLayout>
  );
};

export default ProjectPage;
