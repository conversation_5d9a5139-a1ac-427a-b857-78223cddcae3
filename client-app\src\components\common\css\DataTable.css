.data-table-container {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.table-search {
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-input {
  width: 300px;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: #ffffff;
  transition: border-color 0.2s ease;
}

.search-btn {
  padding: 8px 16px;
  background: #374151;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.search-btn:hover {
  background: #1f2937;
}

.custom-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-input::placeholder {
  color: #9ca3af;
}

.table-wrapper {
  overflow-x: auto;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  background: #ffffff;
}

.data-table thead {
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.data-table th {
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid #e2e8f0;
  position: relative;
  user-select: none;
}

.data-table th.sortable {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.data-table th.sortable:hover {
  background: #e2e8f0;
}

.th-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sort-icon {
  margin-left: 8px;
  color: #9ca3af;
  font-size: 12px;
  transition: color 0.2s ease;
}

.sort-icon.active {
  color: #3b82f6;
}

.data-table th.center,
.data-table td.center {
  text-align: center;
}

.data-table th.right,
.data-table td.right {
  text-align: right;
}

.data-table tbody tr {
  border-bottom: 1px solid #f1f5f9;
  transition: background-color 0.2s ease;
}

.data-table tbody tr:hover {
  background: #f8fafc;
}

.data-table tbody tr.clickable {
  cursor: pointer;
}

.data-table tbody tr.clickable:hover {
  background: #f1f5f9;
}

.data-table td {
  padding: 12px 16px;
  color: #374151;
  vertical-align: middle;
}

.data-table td:first-child {
  font-weight: 500;
}

.table-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
  font-size: 14px;
}

.pagination-info {
  color: #6b7280;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-info {
  color: #374151;
  font-weight: 500;
}

.pagination-btn {
  width: 32px;
  height: 32px;
  border: 1px solid #d1d5db;
  background: #ffffff;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 16px;
  color: #374151;
}

.pagination-btn:hover:not(:disabled) {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60px 20px;
  color: #6b7280;
  font-size: 16px;
}

@media (max-width: 768px) {
  .table-search {
    padding: 12px 16px;
  }

  .search-input {
    width: 100%;
    max-width: 300px;
  }

  .data-table th,
  .data-table td {
    padding: 8px 12px;
    font-size: 13px;
  }

  .table-pagination {
    flex-direction: column;
    gap: 12px;
    padding: 12px 16px;
  }

  .pagination-controls {
    width: 100%;
    justify-content: center;
  }
}
