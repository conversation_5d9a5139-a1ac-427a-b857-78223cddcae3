
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import ColorPalette from '../../styles/ColorPalette';
import Typography from '../../styles/Typography';
import { useAuth } from '../../context/AuthContext';

const NavbarContainer = styled.header`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  height: 64px;
  background-color: ${ColorPalette.neutral.white};
  border-bottom: 1px solid ${ColorPalette.border.light};
`;

const LeftSection = styled.div`
  display: flex;
  align-items: center;
`;

const SearchContainer = styled.div`
  position: relative;
  width: 300px;
`;

const SearchInput = styled.input`
  width: 100%;
  padding: 8px 40px 8px 16px;
  border: 1px solid ${ColorPalette.border.light};
  border-radius: 4px;
  font-family: ${Typography.fontFamily.primary};
  font-size: ${Typography.fontSize.sm};
  color: ${ColorPalette.text.primary};
  background-color: ${ColorPalette.background.default};

  &:focus {
    outline: none;
    border-color: ${ColorPalette.primary.main};
  }

  &::placeholder {
    color: ${ColorPalette.text.hint};
  }
`;

const SearchIcon = styled.div`
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: ${ColorPalette.primary.main};
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
`;

const RightSection = styled.div`
  display: flex;
  align-items: center;
`;

const IconButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  background-color: transparent;
  color: ${ColorPalette.text.secondary};
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  margin-left: 16px;

  &:hover {
    background-color: ${ColorPalette.neutral.offWhite};
    color: ${ColorPalette.primary.main};
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px ${ColorPalette.primary.light}20;
  }
`;

const NotificationBadge = styled.span`
  position: absolute;
  top: 4px;
  right: 4px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: ${ColorPalette.status.error};
`;

const UserProfile = styled.div`
  display: flex;
  align-items: center;
  margin-left: 16px;
  cursor: pointer;
  position: relative;
`;

const UserAvatar = styled.div`
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: ${ColorPalette.neutral.grey};
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${ColorPalette.neutral.white};
  font-weight: ${Typography.fontWeight.bold};
  font-size: ${Typography.fontSize.md};
  overflow: hidden;
`;

const UserAvatarImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
`;

const UserDropdownArrow = styled.span`
  margin-left: 4px;
  color: ${ColorPalette.text.secondary};
`;

const SearchIconSvg = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
  </svg>
);

const ThemeIconSvg = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
  </svg>
);

const FullscreenIconSvg = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5v-4m0 4h-4m4 0l-5-5" />
  </svg>
);

const NotificationIconSvg = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
  </svg>
);

const HomeIconSvg = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
  </svg>
);

const ChevronDownSvg = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
  </svg>
);

const Navbar = () => {
  const { currentUser } = useAuth();

  // Get user initials
  const getUserInitials = () => {
    if (!currentUser) return 'U';
    return `${currentUser.firstName.charAt(0)}${currentUser.lastName.charAt(0)}`;
  };

  return (
    <NavbarContainer>
      <LeftSection>
        <SearchContainer>
          <SearchInput placeholder="Search..." />
          <SearchIcon>
            <SearchIconSvg />
          </SearchIcon>
        </SearchContainer>
      </LeftSection>

      <RightSection>
        <IconButton>
          <ThemeIconSvg />
        </IconButton>

        <IconButton>
          <FullscreenIconSvg />
        </IconButton>

        <IconButton style={{ position: 'relative' }}>
          <NotificationIconSvg />
          <NotificationBadge />
        </IconButton>

        <UserProfile>
          <UserAvatar>
            {getUserInitials()}
          </UserAvatar>
          <UserDropdownArrow>
            <ChevronDownSvg />
          </UserDropdownArrow>
        </UserProfile>
      </RightSection>
    </NavbarContainer>
  );
};

export default Navbar;
