using NOBLE.API.Models.DTOs;

namespace NOBLE.API.Services.Interfaces
{
    public interface IBudgetsTradeService
    {
        Task<IEnumerable<BudgetsTradeDTO>> GetAllBudgetsTradesAsync();
        Task<BudgetsTradeDTO> GetBudgetsTradeByIdsAsync(int budgetId, int tradeId);
        Task<IEnumerable<BudgetsTradeDTO>> GetBudgetsTradesByBudgetIdAsync(int budgetId);
        Task<IEnumerable<BudgetsTradeDTO>> GetBudgetsTradesByTradeIdAsync(int tradeId);
        Task<BudgetsTradeDTO> CreateBudgetsTradeAsync(BudgetsTradeCreateDTO budgetsTradeDto);
        Task<BudgetsTradeDTO> UpdateBudgetsTradeAsync(int budgetId, int tradeId, BudgetsTradeUpdateDTO budgetsTradeDto);
        Task<bool> DeleteBudgetsTradeAsync(int budgetId, int tradeId);
    }
}
