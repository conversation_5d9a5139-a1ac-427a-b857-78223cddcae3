using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class ProcessStepsController : ControllerBase
    {
        private readonly IProcessStepService _processStepService;
        
        public ProcessStepsController(IProcessStepService processStepService)
        {
            _processStepService = processStepService;
        }
        
        // GET: api/ProcessSteps
        [HttpGet]
        public async Task<ActionResult<IEnumerable<ProcessStepDTO>>> GetProcessSteps()
        {
            var processSteps = await _processStepService.GetAllProcessStepsAsync();
            return Ok(processSteps);
        }
        
        // GET: api/ProcessSteps/5
        [HttpGet("{id}")]
        public async Task<ActionResult<ProcessStepDTO>> GetProcessStep(int id)
        {
            try
            {
                var processStep = await _processStepService.GetProcessStepByIdAsync(id);
                return Ok(processStep);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // GET: api/ProcessSteps/Process/5
        [HttpGet("Process/{processId}")]
        public async Task<ActionResult<IEnumerable<ProcessStepDTO>>> GetProcessStepsByProcess(int processId)
        {
            try
            {
                var processSteps = await _processStepService.GetProcessStepsByProcessIdAsync(processId);
                return Ok(processSteps);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // GET: api/ProcessSteps/AssignedPerson/5
        [HttpGet("AssignedPerson/{assignedPersonId}")]
        public async Task<ActionResult<IEnumerable<ProcessStepDTO>>> GetProcessStepsByAssignedPerson(int assignedPersonId)
        {
            try
            {
                var processSteps = await _processStepService.GetProcessStepsByAssignedPersonIdAsync(assignedPersonId);
                return Ok(processSteps);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // GET: api/ProcessSteps/Type/Approval
        [HttpGet("Type/{type}")]
        public async Task<ActionResult<IEnumerable<ProcessStepDTO>>> GetProcessStepsByType(string type)
        {
            var processSteps = await _processStepService.GetProcessStepsByTypeAsync(type);
            return Ok(processSteps);
        }
        
        // POST: api/ProcessSteps
        [HttpPost]
        public async Task<ActionResult<ProcessStepDTO>> CreateProcessStep(ProcessStepCreateDTO processStepDto)
        {
            try
            {
                var createdProcessStep = await _processStepService.CreateProcessStepAsync(processStepDto);
                return CreatedAtAction(nameof(GetProcessStep), new { id = createdProcessStep.ProcessStepId }, createdProcessStep);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // PUT: api/ProcessSteps/5
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateProcessStep(int id, ProcessStepUpdateDTO processStepDto)
        {
            try
            {
                var updatedProcessStep = await _processStepService.UpdateProcessStepAsync(id, processStepDto);
                return Ok(updatedProcessStep);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // DELETE: api/ProcessSteps/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteProcessStep(int id)
        {
            try
            {
                var result = await _processStepService.DeleteProcessStepAsync(id);
                
                if (!result)
                    return NotFound(new { message = $"ProcessStep with ID {id} not found" });
                    
                return NoContent();
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
    }
}
