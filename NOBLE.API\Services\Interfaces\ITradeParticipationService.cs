using NOBLE.API.Models.DTOs;

namespace NOBLE.API.Services.Interfaces
{
    public interface ITradeParticipationService
    {
        Task<IEnumerable<TradeParticipationDTO>> GetAllTradeParticipationsAsync();
        Task<TradeParticipationDTO> GetTradeParticipationByIdAsync(int id);
        Task<IEnumerable<TradeParticipationDTO>> GetTradeParticipationsByTradeIdAsync(int tradeId);
        Task<IEnumerable<TradeParticipationDTO>> GetTradeParticipationsBySubContractorIdAsync(int subContractorId);
        Task<IEnumerable<TradeParticipationDTO>> GetChildTradeParticipationsAsync(int parentId);
        Task<TradeParticipationDTO> CreateTradeParticipationAsync(TradeParticipationCreateDTO tradeParticipationDto);
        Task<TradeParticipationDTO> UpdateTradeParticipationAsync(int id, TradeParticipationUpdateDTO tradeParticipationDto);
        Task<bool> DeleteTradeParticipationAsync(int id);
    }
}
