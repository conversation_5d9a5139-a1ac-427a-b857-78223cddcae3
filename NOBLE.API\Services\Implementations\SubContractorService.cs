using Microsoft.EntityFrameworkCore;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Models.Generated;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Services.Implementations
{
    public class SubContractorService : ISubContractorService
    {
        private readonly NobleDbContext _context;
        private readonly IUserContextService _userContextService;

        public SubContractorService(NobleDbContext context, IUserContextService userContextService)
        {
            _context = context;
            _userContextService = userContextService;
        }

        public async Task<IEnumerable<SubContractorDTO>> GetAllSubContractorsAsync()
        {
            return await _context.SubContractors
                .Include(sc => sc.BusinessUnit)
                .Select(sc => new SubContractorDTO
                {
                    SubContractorId = sc.SubContractorId,
                    BusinessUnitId = sc.BusinessUnitId,
                    Name = sc.Name,
                    ShortName = sc.ShortName,
                    Street = sc.Street,
                    Locality = sc.Locality,
                    State = sc.State,
                    PostalCode = sc.PostalCode,
                    Comments = sc.Comments,
                    Account = sc.Account,
                    ABN = sc.Abn,
                    Phone = sc.Phone,
                    Fax = sc.Fax,
                    Website = sc.Website,
                    ACN = sc.Acn,
                    LicenceNumber = sc.LicenceNumber,
                    PrequalifiedForm = sc.PrequalifiedForm,
                    PublicLiabilityInsurance = sc.PublicLiabilityInsurance,
                    WorkCoverInsurance = sc.WorkCoverInsurance,
                    ProfessionalIndemnityInsurance = sc.ProfessionalIndemnityInsurance,
                    DCContractor = sc.Dccontractor,
                    BusinessUnitName = sc.BusinessUnit != null ? sc.BusinessUnit.Name : null
                })
                .ToListAsync();
        }

        public async Task<SubContractorDTO> GetSubContractorByIdAsync(int id)
        {
            var subContractor = await _context.SubContractors
                .Include(sc => sc.BusinessUnit)
                .FirstOrDefaultAsync(sc => sc.SubContractorId == id);
            
            if (subContractor == null)
                throw new KeyNotFoundException($"SubContractor with ID {id} not found");
                
            return new SubContractorDTO
            {
                SubContractorId = subContractor.SubContractorId,
                BusinessUnitId = subContractor.BusinessUnitId,
                Name = subContractor.Name,
                ShortName = subContractor.ShortName,
                Street = subContractor.Street,
                Locality = subContractor.Locality,
                State = subContractor.State,
                PostalCode = subContractor.PostalCode,
                Comments = subContractor.Comments,
                Account = subContractor.Account,
                ABN = subContractor.Abn,
                Phone = subContractor.Phone,
                Fax = subContractor.Fax,
                Website = subContractor.Website,
                ACN = subContractor.Acn,
                LicenceNumber = subContractor.LicenceNumber,
                PrequalifiedForm = subContractor.PrequalifiedForm,
                PublicLiabilityInsurance = subContractor.PublicLiabilityInsurance,
                WorkCoverInsurance = subContractor.WorkCoverInsurance,
                ProfessionalIndemnityInsurance = subContractor.ProfessionalIndemnityInsurance,
                DCContractor = subContractor.Dccontractor,
                BusinessUnitName = subContractor.BusinessUnit != null ? subContractor.BusinessUnit.Name : null
            };
        }

        public async Task<IEnumerable<SubContractorDTO>> GetSubContractorsByBusinessUnitIdAsync(int businessUnitId)
        {
            // Check if the business unit exists
            var businessUnit = await _context.BusinessUnits.FindAsync(businessUnitId);
            if (businessUnit == null)
                throw new KeyNotFoundException($"BusinessUnit with ID {businessUnitId} not found");
                
            return await _context.SubContractors
                .Include(sc => sc.BusinessUnit)
                .Where(sc => sc.BusinessUnitId == businessUnitId)
                .Select(sc => new SubContractorDTO
                {
                    SubContractorId = sc.SubContractorId,
                    BusinessUnitId = sc.BusinessUnitId,
                    Name = sc.Name,
                    ShortName = sc.ShortName,
                    Street = sc.Street,
                    Locality = sc.Locality,
                    State = sc.State,
                    PostalCode = sc.PostalCode,
                    Comments = sc.Comments,
                    Account = sc.Account,
                    ABN = sc.Abn,
                    Phone = sc.Phone,
                    Fax = sc.Fax,
                    Website = sc.Website,
                    ACN = sc.Acn,
                    LicenceNumber = sc.LicenceNumber,
                    PrequalifiedForm = sc.PrequalifiedForm,
                    PublicLiabilityInsurance = sc.PublicLiabilityInsurance,
                    WorkCoverInsurance = sc.WorkCoverInsurance,
                    ProfessionalIndemnityInsurance = sc.ProfessionalIndemnityInsurance,
                    DCContractor = sc.Dccontractor,
                    BusinessUnitName = sc.BusinessUnit != null ? sc.BusinessUnit.Name : null
                })
                .ToListAsync();
        }

        public async Task<SubContractorDTO> CreateSubContractorAsync(SubContractorCreateDTO subContractorDto)
        {
            // Check if the business unit exists
            var businessUnit = await _context.BusinessUnits.FindAsync(subContractorDto.BusinessUnitId);
            if (businessUnit == null)
                throw new KeyNotFoundException($"BusinessUnit with ID {subContractorDto.BusinessUnitId} not found");
                
            var subContractor = new SubContractor
            {
                BusinessUnitId = subContractorDto.BusinessUnitId,
                Name = subContractorDto.Name,
                ShortName = subContractorDto.ShortName,
                Street = subContractorDto.Street,
                Locality = subContractorDto.Locality,
                State = subContractorDto.State,
                PostalCode = subContractorDto.PostalCode,
                Comments = subContractorDto.Comments,
                Account = subContractorDto.Account,
                Abn = subContractorDto.ABN,
                Phone = subContractorDto.Phone,
                Fax = subContractorDto.Fax,
                Website = subContractorDto.Website,
                Acn = subContractorDto.ACN,
                LicenceNumber = subContractorDto.LicenceNumber,
                PrequalifiedForm = subContractorDto.PrequalifiedForm,
                PublicLiabilityInsurance = subContractorDto.PublicLiabilityInsurance,
                WorkCoverInsurance = subContractorDto.WorkCoverInsurance,
                ProfessionalIndemnityInsurance = subContractorDto.ProfessionalIndemnityInsurance,
                Dccontractor = subContractorDto.DCContractor,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow,
                CreatedPeopleId = _userContextService.GetCurrentUserId(),
                ModifiedPeopleId = _userContextService.GetCurrentUserId()
            };
            
            _context.SubContractors.Add(subContractor);
            await _context.SaveChangesAsync();
            
            return await GetSubContractorByIdAsync(subContractor.SubContractorId);
        }

        public async Task<SubContractorDTO> UpdateSubContractorAsync(int id, SubContractorUpdateDTO subContractorDto)
        {
            var subContractor = await _context.SubContractors.FindAsync(id);
            
            if (subContractor == null)
                throw new KeyNotFoundException($"SubContractor with ID {id} not found");
                
            // Check if the business unit exists
            var businessUnit = await _context.BusinessUnits.FindAsync(subContractorDto.BusinessUnitId);
            if (businessUnit == null)
                throw new KeyNotFoundException($"BusinessUnit with ID {subContractorDto.BusinessUnitId} not found");
                
            subContractor.BusinessUnitId = subContractorDto.BusinessUnitId;
            subContractor.Name = subContractorDto.Name;
            subContractor.ShortName = subContractorDto.ShortName;
            subContractor.Street = subContractorDto.Street;
            subContractor.Locality = subContractorDto.Locality;
            subContractor.State = subContractorDto.State;
            subContractor.PostalCode = subContractorDto.PostalCode;
            subContractor.Comments = subContractorDto.Comments;
            subContractor.Account = subContractorDto.Account;
            subContractor.Abn = subContractorDto.ABN;
            subContractor.Phone = subContractorDto.Phone;
            subContractor.Fax = subContractorDto.Fax;
            subContractor.Website = subContractorDto.Website;
            subContractor.Acn = subContractorDto.ACN;
            subContractor.LicenceNumber = subContractorDto.LicenceNumber;
            subContractor.PrequalifiedForm = subContractorDto.PrequalifiedForm;
            subContractor.PublicLiabilityInsurance = subContractorDto.PublicLiabilityInsurance;
            subContractor.WorkCoverInsurance = subContractorDto.WorkCoverInsurance;
            subContractor.ProfessionalIndemnityInsurance = subContractorDto.ProfessionalIndemnityInsurance;
            subContractor.Dccontractor = subContractorDto.DCContractor;
            subContractor.ModifiedDate = DateTime.UtcNow;
            subContractor.ModifiedPeopleId = _userContextService.GetCurrentUserId();
            
            await _context.SaveChangesAsync();
            
            return await GetSubContractorByIdAsync(id);
        }

        public async Task<bool> DeleteSubContractorAsync(int id)
        {
            var subContractor = await _context.SubContractors.FindAsync(id);
            
            if (subContractor == null)
                return false;
                
            // Check if the subcontractor is used in any trade participations
            var isUsedInTradeParticipations = await _context.TradeParticipations
                .AnyAsync(tp => tp.SubContractorId == id);
                
            if (isUsedInTradeParticipations)
                throw new InvalidOperationException("Cannot delete subcontractor that is used in trade participations");
                
            _context.SubContractors.Remove(subContractor);
            await _context.SaveChangesAsync();
            
            return true;
        }
    }
}
