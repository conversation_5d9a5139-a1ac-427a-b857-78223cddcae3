using Microsoft.EntityFrameworkCore;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Models.Generated;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Services.Implementations
{
    public class BusinessUnitService : IBusinessUnitService
    {
        private readonly NobleDbContext _context;
        private readonly IUserContextService _userContextService;

        public BusinessUnitService(NobleDbContext context, IUserContextService userContextService)
        {
            _context = context;
            _userContextService = userContextService;
        }

        public async Task<IEnumerable<BusinessUnitDTO>> GetAllBusinessUnitsAsync()
        {
            return await _context.BusinessUnits
                .Select(bu => new BusinessUnitDTO
                {
                    BusinessUnitId = bu.BusinessUnitId,
                    Name = bu.Name,
                    ProjectNumberFormat = bu.ProjectNumberFormat,
                    UmpeopleId = bu.UmpeopleId,
                    EdpeopleId = bu.EdpeopleId,
                    TradeOverbudgetApproval = bu.TradeOverbudgetApproval,
                    TradeAmountApproval = bu.TradeAmountApproval,
                    TradeComAmountApproval = bu.TradeComAmountApproval,
                    TradeComOverBudget = bu.TradeComOverBudget,
                    TradeDaamountApproval = bu.TradeDaamountApproval,
                    TradeUmoverbudgetApproval = bu.TradeUmoverbudgetApproval,
                    VariationUmdaoverAmtApproval = bu.VariationUmdaoverAmtApproval,
                    ClaimSpecialNote = bu.ClaimSpecialNote,
                    VariationSepAccUmapproval = bu.VariationSepAccUmapproval,
                    VariationUmboqvcvdvapproval = bu.VariationUmboqvcvdvapproval
                })
                .ToListAsync();
        }

        public async Task<BusinessUnitDTO> GetBusinessUnitByIdAsync(int id)
        {
            var businessUnit = await _context.BusinessUnits.FindAsync(id);

            if (businessUnit == null)
                throw new KeyNotFoundException($"Business Unit with ID {id} not found");

            return new BusinessUnitDTO
            {
                BusinessUnitId = businessUnit.BusinessUnitId,
                Name = businessUnit.Name,
                ProjectNumberFormat = businessUnit.ProjectNumberFormat,
                UmpeopleId = businessUnit.UmpeopleId,
                EdpeopleId = businessUnit.EdpeopleId,
                TradeOverbudgetApproval = businessUnit.TradeOverbudgetApproval,
                TradeAmountApproval = businessUnit.TradeAmountApproval,
                TradeComAmountApproval = businessUnit.TradeComAmountApproval,
                TradeComOverBudget = businessUnit.TradeComOverBudget,
                TradeDaamountApproval = businessUnit.TradeDaamountApproval,
                TradeUmoverbudgetApproval = businessUnit.TradeUmoverbudgetApproval,
                VariationUmdaoverAmtApproval = businessUnit.VariationUmdaoverAmtApproval,
                ClaimSpecialNote = businessUnit.ClaimSpecialNote,
                VariationSepAccUmapproval = businessUnit.VariationSepAccUmapproval,
                VariationUmboqvcvdvapproval = businessUnit.VariationUmboqvcvdvapproval
            };
        }

        public async Task<BusinessUnitDTO> CreateBusinessUnitAsync(BusinessUnitCreateDTO businessUnitDto)
        {
            var businessUnit = new BusinessUnit
            {
                Name = businessUnitDto.Name,
                ProjectNumberFormat = businessUnitDto.ProjectNumberFormat,
                UmpeopleId = businessUnitDto.UmpeopleId,
                EdpeopleId = businessUnitDto.EdpeopleId,
                TradeOverbudgetApproval = businessUnitDto.TradeOverbudgetApproval,
                TradeAmountApproval = businessUnitDto.TradeAmountApproval,
                TradeComAmountApproval = businessUnitDto.TradeComAmountApproval,
                TradeComOverBudget = businessUnitDto.TradeComOverBudget,
                TradeDaamountApproval = businessUnitDto.TradeDaamountApproval,
                TradeUmoverbudgetApproval = businessUnitDto.TradeUmoverbudgetApproval,
                VariationUmdaoverAmtApproval = businessUnitDto.VariationUmdaoverAmtApproval,
                ClaimSpecialNote = businessUnitDto.ClaimSpecialNote,
                VariationSepAccUmapproval = businessUnitDto.VariationSepAccUmapproval,
                VariationUmboqvcvdvapproval = businessUnitDto.VariationUmboqvcvdvapproval,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow,
                CreatedPeopleId = _userContextService.GetCurrentUserId(),
                ModifiedPeopleId = _userContextService.GetCurrentUserId()
            };

            _context.BusinessUnits.Add(businessUnit);
            await _context.SaveChangesAsync();

            return await GetBusinessUnitByIdAsync(businessUnit.BusinessUnitId);
        }

        public async Task<BusinessUnitDTO> UpdateBusinessUnitAsync(int id, BusinessUnitUpdateDTO businessUnitDto)
        {
            var businessUnit = await _context.BusinessUnits.FindAsync(id);

            if (businessUnit == null)
                throw new KeyNotFoundException($"Business Unit with ID {id} not found");

            businessUnit.Name = businessUnitDto.Name;
            businessUnit.ProjectNumberFormat = businessUnitDto.ProjectNumberFormat;
            businessUnit.UmpeopleId = businessUnitDto.UmpeopleId;
            businessUnit.EdpeopleId = businessUnitDto.EdpeopleId;
            businessUnit.TradeOverbudgetApproval = businessUnitDto.TradeOverbudgetApproval;
            businessUnit.TradeAmountApproval = businessUnitDto.TradeAmountApproval;
            businessUnit.TradeComAmountApproval = businessUnitDto.TradeComAmountApproval;
            businessUnit.TradeComOverBudget = businessUnitDto.TradeComOverBudget;
            businessUnit.TradeDaamountApproval = businessUnitDto.TradeDaamountApproval;
            businessUnit.TradeUmoverbudgetApproval = businessUnitDto.TradeUmoverbudgetApproval;
            businessUnit.VariationUmdaoverAmtApproval = businessUnitDto.VariationUmdaoverAmtApproval;
            businessUnit.ClaimSpecialNote = businessUnitDto.ClaimSpecialNote;
            businessUnit.VariationSepAccUmapproval = businessUnitDto.VariationSepAccUmapproval;
            businessUnit.VariationUmboqvcvdvapproval = businessUnitDto.VariationUmboqvcvdvapproval;
            businessUnit.ModifiedDate = DateTime.UtcNow;
            businessUnit.ModifiedPeopleId = _userContextService.GetCurrentUserId();

            await _context.SaveChangesAsync();

            return await GetBusinessUnitByIdAsync(id);
        }

        public async Task<bool> DeleteBusinessUnitAsync(int id)
        {
            var businessUnit = await _context.BusinessUnits.FindAsync(id);

            if (businessUnit == null)
                return false;
             
            var hasProjects = await _context.Projects.AnyAsync(p => p.BusinessUnitId == id);

            if (hasProjects)
            { 
                throw new InvalidOperationException("Cannot delete a business unit that has associated projects");
            }

            _context.BusinessUnits.Remove(businessUnit);
            await _context.SaveChangesAsync();

            return true;
        }


    }
}
