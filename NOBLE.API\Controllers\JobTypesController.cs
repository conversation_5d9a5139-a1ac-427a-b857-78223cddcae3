using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class JobTypesController : ControllerBase
    {
        private readonly IJobTypeService _jobTypeService;
        
        public JobTypesController(IJobTypeService jobTypeService)
        {
            _jobTypeService = jobTypeService;
        }
       
        [HttpGet]
        public async Task<ActionResult<IEnumerable<JobTypeDTO>>> GetJobTypes()
        {
            var jobTypes = await _jobTypeService.GetAllJobTypesAsync();
            return Ok(jobTypes);
        }
         
        [HttpGet("{id}")]
        public async Task<ActionResult<JobTypeDTO>> GetJobType(int id)
        {
            try
            {
                var jobType = await _jobTypeService.GetJobTypeByIdAsync(id);
                return Ok(jobType);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
         
        [HttpPost]
        public async Task<ActionResult<JobTypeDTO>> CreateJobType(JobTypeCreateDTO jobTypeDto)
        {
            try
            {
                var createdJobType = await _jobTypeService.CreateJobTypeAsync(jobTypeDto);
                return CreatedAtAction(nameof(GetJobType), new { id = createdJobType.JobTypeId }, createdJobType);
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
         
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateJobType(int id, JobTypeUpdateDTO jobTypeDto)
        {
            try
            {
                var updatedJobType = await _jobTypeService.UpdateJobTypeAsync(id, jobTypeDto);
                return Ok(updatedJobType);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
         
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteJobType(int id)
        {
            try
            {
                var result = await _jobTypeService.DeleteJobTypeAsync(id);
                
                if (!result)
                    return NotFound(new { message = $"JobType with ID {id} not found" });
                    
                return NoContent();
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
    }
}
