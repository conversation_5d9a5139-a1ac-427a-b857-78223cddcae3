﻿using System;
using System.Collections.Generic;

namespace NOBLE.API.Models.Generated;

public partial class Budget
{
    public int BudgetId { get; set; }

    public int ProjectId { get; set; }

    public string Code { get; set; } = null!;

    public decimal? Amount { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public int CreatedPeopleId { get; set; }

    public int ModifiedPeopleId { get; set; }

    public virtual ICollection<BudgetsTrade> BudgetsTrades { get; set; } = new List<BudgetsTrade>();

    public virtual Project Project { get; set; } = null!;
}
