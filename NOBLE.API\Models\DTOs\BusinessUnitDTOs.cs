using System.ComponentModel.DataAnnotations;

namespace NOBLE.API.Models.DTOs
{
    public class BusinessUnitDTO
    {
        public int BusinessUnitId { get; set; }
        public string? Name { get; set; }
        public string? ProjectNumberFormat { get; set; }
        public int? UmpeopleId { get; set; }
        public int? EdpeopleId { get; set; }
        public decimal? TradeOverbudgetApproval { get; set; }
        public decimal? TradeAmountApproval { get; set; }
        public decimal? TradeComAmountApproval { get; set; }
        public int? TradeComOverBudget { get; set; }
        public decimal? TradeDaamountApproval { get; set; }
        public decimal? TradeUmoverbudgetApproval { get; set; }
        public decimal? VariationUmdaoverAmtApproval { get; set; }
        public string? ClaimSpecialNote { get; set; }
        public decimal? VariationSepAccUmapproval { get; set; }
        public decimal? VariationUmboqvcvdvapproval { get; set; }

    }

    public class BusinessUnitCreateDTO
    {
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [StringLength(10)]
        public string? ProjectNumberFormat { get; set; }

        public int? UmpeopleId { get; set; }
        public int? EdpeopleId { get; set; }
        public decimal? TradeOverbudgetApproval { get; set; }
        public decimal? TradeAmountApproval { get; set; }
        public decimal? TradeComAmountApproval { get; set; }
        public int? TradeComOverBudget { get; set; }
        public decimal? TradeDaamountApproval { get; set; }
        public decimal? TradeUmoverbudgetApproval { get; set; }
        public decimal? VariationUmdaoverAmtApproval { get; set; }

        [StringLength(1000)]
        public string? ClaimSpecialNote { get; set; }

        public decimal? VariationSepAccUmapproval { get; set; }
        public decimal? VariationUmboqvcvdvapproval { get; set; }
    }

    public class BusinessUnitUpdateDTO
    {
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [StringLength(10)]
        public string? ProjectNumberFormat { get; set; }

        public int? UmpeopleId { get; set; }
        public int? EdpeopleId { get; set; }
        public decimal? TradeOverbudgetApproval { get; set; }
        public decimal? TradeAmountApproval { get; set; }
        public decimal? TradeComAmountApproval { get; set; }
        public int? TradeComOverBudget { get; set; }
        public decimal? TradeDaamountApproval { get; set; }
        public decimal? TradeUmoverbudgetApproval { get; set; }
        public decimal? VariationUmdaoverAmtApproval { get; set; }

        [StringLength(1000)]
        public string? ClaimSpecialNote { get; set; }

        public decimal? VariationSepAccUmapproval { get; set; }
        public decimal? VariationUmboqvcvdvapproval { get; set; }


    }
}
