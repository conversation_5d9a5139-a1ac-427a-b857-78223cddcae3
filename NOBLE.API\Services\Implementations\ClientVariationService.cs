using Microsoft.EntityFrameworkCore;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Models.Generated;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Services.Implementations
{
    public class ClientVariationService : IClientVariationService
    {
        private readonly NobleDbContext _context;
        private readonly IUserContextService _userContextService;

        public ClientVariationService(NobleDbContext context, IUserContextService userContextService)
        {
            _context = context;
            _userContextService = userContextService;
        }

        public async Task<IEnumerable<ClientVariationDTO>> GetAllClientVariationsAsync()
        {
            return await _context.ClientVariations
                .Include(cv => cv.Project)
                .Include(cv => cv.ParentClientVariation)
                .Select(cv => new ClientVariationDTO
                {
                    ClientVariationId = cv.ClientVariationId,
                    ParentClientVariationId = cv.ParentClientVariationId,
                    ProjectId = cv.ProjectId,
                    ProcessId = cv.ProcessId,
                    VariationId = cv.VariationId,
                    Type = cv.Type,
                    Name = cv.Name,
                    Number = cv.Number,
                    GoodsServicesTax = cv.GoodsServicesTax,
                    WriteDate = cv.WriteDate,
                    VerbalApprovalDate = cv.VerbalApprovalDate,
                    InternalApprovalDate = cv.InternalApprovalDate,
                    ApprovalDate = cv.ApprovalDate,
                    CancelDate = cv.CancelDate,
                    QuotesFile = cv.QuotesFile,
                    HideCostDetails = cv.HideCostDetails,
                    BackupFile = cv.BackupFile,
                    ClientApprovalFile = cv.ClientApprovalFile,
                    Comments = cv.Comments,
                    InvoiceNumber = cv.InvoiceNumber,
                    InvoiceDate = cv.InvoiceDate,
                    InvoiceSentDate = cv.InvoiceSentDate,
                    InvoiceDueDate = cv.InvoiceDueDate,
                    InvoicePaidDate = cv.InvoicePaidDate,
                    WorksCompletedDate = cv.WorksCompletedDate,
                    UseSecondPrincipal = cv.UseSecondPrincipal,
                    ProjectName = cv.Project != null ? cv.Project.Name : null,
                    ParentClientVariationName = cv.ParentClientVariation != null ? cv.ParentClientVariation.Name : null
                })
                .ToListAsync();
        }

        public async Task<ClientVariationDTO> GetClientVariationByIdAsync(int id)
        {
            var clientVariation = await _context.ClientVariations
                .Include(cv => cv.Project)
                .Include(cv => cv.ParentClientVariation)
                .FirstOrDefaultAsync(cv => cv.ClientVariationId == id);
            
            if (clientVariation == null)
                throw new KeyNotFoundException($"ClientVariation with ID {id} not found");
                
            return new ClientVariationDTO
            {
                ClientVariationId = clientVariation.ClientVariationId,
                ParentClientVariationId = clientVariation.ParentClientVariationId,
                ProjectId = clientVariation.ProjectId,
                ProcessId = clientVariation.ProcessId,
                VariationId = clientVariation.VariationId,
                Type = clientVariation.Type,
                Name = clientVariation.Name,
                Number = clientVariation.Number,
                GoodsServicesTax = clientVariation.GoodsServicesTax,
                WriteDate = clientVariation.WriteDate,
                VerbalApprovalDate = clientVariation.VerbalApprovalDate,
                InternalApprovalDate = clientVariation.InternalApprovalDate,
                ApprovalDate = clientVariation.ApprovalDate,
                CancelDate = clientVariation.CancelDate,
                QuotesFile = clientVariation.QuotesFile,
                HideCostDetails = clientVariation.HideCostDetails,
                BackupFile = clientVariation.BackupFile,
                ClientApprovalFile = clientVariation.ClientApprovalFile,
                Comments = clientVariation.Comments,
                InvoiceNumber = clientVariation.InvoiceNumber,
                InvoiceDate = clientVariation.InvoiceDate,
                InvoiceSentDate = clientVariation.InvoiceSentDate,
                InvoiceDueDate = clientVariation.InvoiceDueDate,
                InvoicePaidDate = clientVariation.InvoicePaidDate,
                WorksCompletedDate = clientVariation.WorksCompletedDate,
                UseSecondPrincipal = clientVariation.UseSecondPrincipal,
                ProjectName = clientVariation.Project != null ? clientVariation.Project.Name : null,
                ParentClientVariationName = clientVariation.ParentClientVariation != null ? clientVariation.ParentClientVariation.Name : null
            };
        }

        public async Task<IEnumerable<ClientVariationDTO>> GetClientVariationsByProjectIdAsync(int projectId)
        {
            // Check if the project exists
            var project = await _context.Projects.FindAsync(projectId);
            if (project == null)
                throw new KeyNotFoundException($"Project with ID {projectId} not found");
                
            return await _context.ClientVariations
                .Include(cv => cv.Project)
                .Include(cv => cv.ParentClientVariation)
                .Where(cv => cv.ProjectId == projectId)
                .Select(cv => new ClientVariationDTO
                {
                    ClientVariationId = cv.ClientVariationId,
                    ParentClientVariationId = cv.ParentClientVariationId,
                    ProjectId = cv.ProjectId,
                    ProcessId = cv.ProcessId,
                    VariationId = cv.VariationId,
                    Type = cv.Type,
                    Name = cv.Name,
                    Number = cv.Number,
                    GoodsServicesTax = cv.GoodsServicesTax,
                    WriteDate = cv.WriteDate,
                    VerbalApprovalDate = cv.VerbalApprovalDate,
                    InternalApprovalDate = cv.InternalApprovalDate,
                    ApprovalDate = cv.ApprovalDate,
                    CancelDate = cv.CancelDate,
                    QuotesFile = cv.QuotesFile,
                    HideCostDetails = cv.HideCostDetails,
                    BackupFile = cv.BackupFile,
                    ClientApprovalFile = cv.ClientApprovalFile,
                    Comments = cv.Comments,
                    InvoiceNumber = cv.InvoiceNumber,
                    InvoiceDate = cv.InvoiceDate,
                    InvoiceSentDate = cv.InvoiceSentDate,
                    InvoiceDueDate = cv.InvoiceDueDate,
                    InvoicePaidDate = cv.InvoicePaidDate,
                    WorksCompletedDate = cv.WorksCompletedDate,
                    UseSecondPrincipal = cv.UseSecondPrincipal,
                    ProjectName = cv.Project != null ? cv.Project.Name : null,
                    ParentClientVariationName = cv.ParentClientVariation != null ? cv.ParentClientVariation.Name : null
                })
                .ToListAsync();
        }

        public async Task<IEnumerable<ClientVariationDTO>> GetChildClientVariationsAsync(int parentId)
        {
            // Check if the parent client variation exists
            var parentClientVariation = await _context.ClientVariations.FindAsync(parentId);
            if (parentClientVariation == null)
                throw new KeyNotFoundException($"Parent ClientVariation with ID {parentId} not found");
                
            return await _context.ClientVariations
                .Include(cv => cv.Project)
                .Include(cv => cv.ParentClientVariation)
                .Where(cv => cv.ParentClientVariationId == parentId)
                .Select(cv => new ClientVariationDTO
                {
                    ClientVariationId = cv.ClientVariationId,
                    ParentClientVariationId = cv.ParentClientVariationId,
                    ProjectId = cv.ProjectId,
                    ProcessId = cv.ProcessId,
                    VariationId = cv.VariationId,
                    Type = cv.Type,
                    Name = cv.Name,
                    Number = cv.Number,
                    GoodsServicesTax = cv.GoodsServicesTax,
                    WriteDate = cv.WriteDate,
                    VerbalApprovalDate = cv.VerbalApprovalDate,
                    InternalApprovalDate = cv.InternalApprovalDate,
                    ApprovalDate = cv.ApprovalDate,
                    CancelDate = cv.CancelDate,
                    QuotesFile = cv.QuotesFile,
                    HideCostDetails = cv.HideCostDetails,
                    BackupFile = cv.BackupFile,
                    ClientApprovalFile = cv.ClientApprovalFile,
                    Comments = cv.Comments,
                    InvoiceNumber = cv.InvoiceNumber,
                    InvoiceDate = cv.InvoiceDate,
                    InvoiceSentDate = cv.InvoiceSentDate,
                    InvoiceDueDate = cv.InvoiceDueDate,
                    InvoicePaidDate = cv.InvoicePaidDate,
                    WorksCompletedDate = cv.WorksCompletedDate,
                    UseSecondPrincipal = cv.UseSecondPrincipal,
                    ProjectName = cv.Project != null ? cv.Project.Name : null,
                    ParentClientVariationName = cv.ParentClientVariation != null ? cv.ParentClientVariation.Name : null
                })
                .ToListAsync();
        }

        public async Task<ClientVariationDTO> CreateClientVariationAsync(ClientVariationCreateDTO clientVariationDto)
        {
            // Check if the project exists
            var project = await _context.Projects.FindAsync(clientVariationDto.ProjectId);
            if (project == null)
                throw new KeyNotFoundException($"Project with ID {clientVariationDto.ProjectId} not found");
                
            // Check if the parent client variation exists (if specified)
            if (clientVariationDto.ParentClientVariationId.HasValue)
            {
                var parentClientVariation = await _context.ClientVariations.FindAsync(clientVariationDto.ParentClientVariationId.Value);
                if (parentClientVariation == null)
                    throw new KeyNotFoundException($"Parent ClientVariation with ID {clientVariationDto.ParentClientVariationId.Value} not found");
            }
                
            var clientVariation = new ClientVariation
            {
                ParentClientVariationId = clientVariationDto.ParentClientVariationId,
                ProjectId = clientVariationDto.ProjectId,
                ProcessId = clientVariationDto.ProcessId,
                VariationId = clientVariationDto.VariationId,
                Type = clientVariationDto.Type,
                Name = clientVariationDto.Name,
                Number = clientVariationDto.Number,
                GoodsServicesTax = clientVariationDto.GoodsServicesTax,
                WriteDate = clientVariationDto.WriteDate,
                VerbalApprovalDate = clientVariationDto.VerbalApprovalDate,
                InternalApprovalDate = clientVariationDto.InternalApprovalDate,
                ApprovalDate = clientVariationDto.ApprovalDate,
                CancelDate = clientVariationDto.CancelDate,
                QuotesFile = clientVariationDto.QuotesFile,
                HideCostDetails = clientVariationDto.HideCostDetails,
                BackupFile = clientVariationDto.BackupFile,
                ClientApprovalFile = clientVariationDto.ClientApprovalFile,
                Comments = clientVariationDto.Comments,
                InvoiceNumber = clientVariationDto.InvoiceNumber,
                InvoiceDate = clientVariationDto.InvoiceDate,
                InvoiceSentDate = clientVariationDto.InvoiceSentDate,
                InvoiceDueDate = clientVariationDto.InvoiceDueDate,
                InvoicePaidDate = clientVariationDto.InvoicePaidDate,
                WorksCompletedDate = clientVariationDto.WorksCompletedDate,
                UseSecondPrincipal = clientVariationDto.UseSecondPrincipal,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow,
                CreatedPeopleId = _userContextService.GetCurrentUserId(),
                ModifiedPeopleId = _userContextService.GetCurrentUserId()
            };
            
            _context.ClientVariations.Add(clientVariation);
            await _context.SaveChangesAsync();
            
            return await GetClientVariationByIdAsync(clientVariation.ClientVariationId);
        }

        public async Task<ClientVariationDTO> UpdateClientVariationAsync(int id, ClientVariationUpdateDTO clientVariationDto)
        {
            var clientVariation = await _context.ClientVariations.FindAsync(id);
            
            if (clientVariation == null)
                throw new KeyNotFoundException($"ClientVariation with ID {id} not found");
                
            // Check if the project exists
            var project = await _context.Projects.FindAsync(clientVariationDto.ProjectId);
            if (project == null)
                throw new KeyNotFoundException($"Project with ID {clientVariationDto.ProjectId} not found");
                
            // Check if the parent client variation exists (if specified)
            if (clientVariationDto.ParentClientVariationId.HasValue)
            {
                // Prevent circular reference (a client variation cannot be its own parent)
                if (clientVariationDto.ParentClientVariationId.Value == id)
                    throw new InvalidOperationException("A client variation cannot be its own parent");
                    
                var parentClientVariation = await _context.ClientVariations.FindAsync(clientVariationDto.ParentClientVariationId.Value);
                if (parentClientVariation == null)
                    throw new KeyNotFoundException($"Parent ClientVariation with ID {clientVariationDto.ParentClientVariationId.Value} not found");
            }
                
            clientVariation.ParentClientVariationId = clientVariationDto.ParentClientVariationId;
            clientVariation.ProjectId = clientVariationDto.ProjectId;
            clientVariation.ProcessId = clientVariationDto.ProcessId;
            clientVariation.VariationId = clientVariationDto.VariationId;
            clientVariation.Type = clientVariationDto.Type;
            clientVariation.Name = clientVariationDto.Name;
            clientVariation.Number = clientVariationDto.Number;
            clientVariation.GoodsServicesTax = clientVariationDto.GoodsServicesTax;
            clientVariation.WriteDate = clientVariationDto.WriteDate;
            clientVariation.VerbalApprovalDate = clientVariationDto.VerbalApprovalDate;
            clientVariation.InternalApprovalDate = clientVariationDto.InternalApprovalDate;
            clientVariation.ApprovalDate = clientVariationDto.ApprovalDate;
            clientVariation.CancelDate = clientVariationDto.CancelDate;
            clientVariation.QuotesFile = clientVariationDto.QuotesFile;
            clientVariation.HideCostDetails = clientVariationDto.HideCostDetails;
            clientVariation.BackupFile = clientVariationDto.BackupFile;
            clientVariation.ClientApprovalFile = clientVariationDto.ClientApprovalFile;
            clientVariation.Comments = clientVariationDto.Comments;
            clientVariation.InvoiceNumber = clientVariationDto.InvoiceNumber;
            clientVariation.InvoiceDate = clientVariationDto.InvoiceDate;
            clientVariation.InvoiceSentDate = clientVariationDto.InvoiceSentDate;
            clientVariation.InvoiceDueDate = clientVariationDto.InvoiceDueDate;
            clientVariation.InvoicePaidDate = clientVariationDto.InvoicePaidDate;
            clientVariation.WorksCompletedDate = clientVariationDto.WorksCompletedDate;
            clientVariation.UseSecondPrincipal = clientVariationDto.UseSecondPrincipal;
            clientVariation.ModifiedDate = DateTime.UtcNow;
            clientVariation.ModifiedPeopleId = _userContextService.GetCurrentUserId();
            
            await _context.SaveChangesAsync();
            
            return await GetClientVariationByIdAsync(id);
        }

        public async Task<bool> DeleteClientVariationAsync(int id)
        {
            var clientVariation = await _context.ClientVariations
                .Include(cv => cv.ClientVariationsDetails)
                .Include(cv => cv.ClientVariationsTrades)
                .Include(cv => cv.InverseParentClientVariation)
                .FirstOrDefaultAsync(cv => cv.ClientVariationId == id);
            
            if (clientVariation == null)
                return false;
                
            // Check if there are any child client variations
            if (clientVariation.InverseParentClientVariation.Any())
            {
                throw new InvalidOperationException("Cannot delete client variation that has child client variations");
            }
            
            // Check if there are any client variation details
            if (clientVariation.ClientVariationsDetails.Any())
            {
                throw new InvalidOperationException("Cannot delete client variation that has client variation details");
            }
            
            // Check if there are any client variation trades
            if (clientVariation.ClientVariationsTrades.Any())
            {
                throw new InvalidOperationException("Cannot delete client variation that has client variation trades");
            }
            
            _context.ClientVariations.Remove(clientVariation);
            await _context.SaveChangesAsync();
            
            return true;
        }
    }
}
