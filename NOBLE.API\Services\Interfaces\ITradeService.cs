using NOBLE.API.Models.DTOs;

namespace NOBLE.API.Services.Interfaces
{
    public interface ITradeService
    {
        Task<IEnumerable<TradeDTO>> GetAllTradesAsync();
        Task<TradeDTO> GetTradeByIdAsync(int id);
        Task<IEnumerable<TradeDTO>> GetTradesByProjectIdAsync(int projectId);
        Task<TradeDTO> CreateTradeAsync(TradeCreateDTO tradeDto);
        Task<TradeDTO> UpdateTradeAsync(int id, TradeUpdateDTO tradeDto);
        Task<bool> DeleteTradeAsync(int id);
    }
}
