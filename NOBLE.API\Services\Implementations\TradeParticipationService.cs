using Microsoft.EntityFrameworkCore;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Models.Generated;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Services.Implementations
{
    public class TradeParticipationService : ITradeParticipationService
    {
        private readonly NobleDbContext _context;
        private readonly IUserContextService _userContextService;

        public TradeParticipationService(NobleDbContext context, IUserContextService userContextService)
        {
            _context = context;
            _userContextService = userContextService;
        }

        private string? GetSubContractorName(int subContractorId)
        {
            var subContractor = _context.SubContractors.FirstOrDefault(sc => sc.SubContractorId == subContractorId);
            return subContractor != null ? subContractor.Name : null;
        }

        public async Task<IEnumerable<TradeParticipationDTO>> GetAllTradeParticipationsAsync()
        {
            return await _context.TradeParticipations
                .Include(tp => tp.Trade)
                .Include(tp => tp.Trade.Project)
                .Include(tp => tp.ContactPeople)
                .Include(tp => tp.InverseComparisonTradeParticipation)
                .Select(tp => new TradeParticipationDTO
                {
                    TradeParticipationId = tp.TradeParticipationId,
                    TradeId = tp.TradeId,
                    SubContractorId = tp.SubContractorId,
                    ContactPeopleId = tp.ContactPeopleId,
                    InvitationDate = tp.InvitationDate,
                    QuoteDate = tp.QuoteDate,
                    QuoteDueDate = tp.QuoteDueDate,
                    Amount = tp.Amount,
                    Status = tp.Status,
                    Rank = tp.Rank,
                    Comments = tp.Comments,
                    InternalComments = tp.InternalComments,
                    PulledOut = tp.PulledOut,
                    OpenDate = tp.OpenDate,
                    ReminderDate = tp.ReminderDate,
                    TradeParticipationType = tp.TradeParticipationType,
                    ComparisonTradeParticipationId = tp.ComparisonTradeParticipationId,
                    QuoteFile = tp.QuoteFile,
                    TransmittalId = tp.TransmittalId,
                    SafetyRating = tp.SafetyRating,
                    PaymentTerms = tp.PaymentTerms,
                    TradeName = tp.Trade != null ? tp.Trade.Name : null,
                    ProjectName = tp.Trade != null && tp.Trade.Project != null ? tp.Trade.Project.Name : null,
                    SubContractorName = tp.SubContractorId.HasValue ?
                        GetSubContractorName(tp.SubContractorId.Value) : null,
                    ContactPersonName = tp.ContactPeople != null ?
                        $"{tp.ContactPeople.FirstName} {tp.ContactPeople.LastName}" : null,
                    ChildParticipationsCount = tp.InverseComparisonTradeParticipation.Count
                })
                .ToListAsync();
        }

        public async Task<TradeParticipationDTO> GetTradeParticipationByIdAsync(int id)
        {
            var tradeParticipation = await _context.TradeParticipations
                .Include(tp => tp.Trade)
                .Include(tp => tp.Trade.Project)
                .Include(tp => tp.ContactPeople)
                .Include(tp => tp.InverseComparisonTradeParticipation)
                .FirstOrDefaultAsync(tp => tp.TradeParticipationId == id);

            if (tradeParticipation == null)
                throw new KeyNotFoundException($"TradeParticipation with ID {id} not found");

            string? subContractorName = null;
            if (tradeParticipation.SubContractorId.HasValue)
            {
                var subContractor = await _context.SubContractors.FindAsync(tradeParticipation.SubContractorId.Value);
                subContractorName = subContractor?.Name;
            }

            return new TradeParticipationDTO
            {
                TradeParticipationId = tradeParticipation.TradeParticipationId,
                TradeId = tradeParticipation.TradeId,
                SubContractorId = tradeParticipation.SubContractorId,
                ContactPeopleId = tradeParticipation.ContactPeopleId,
                InvitationDate = tradeParticipation.InvitationDate,
                QuoteDate = tradeParticipation.QuoteDate,
                QuoteDueDate = tradeParticipation.QuoteDueDate,
                Amount = tradeParticipation.Amount,
                Status = tradeParticipation.Status,
                Rank = tradeParticipation.Rank,
                Comments = tradeParticipation.Comments,
                InternalComments = tradeParticipation.InternalComments,
                PulledOut = tradeParticipation.PulledOut,
                OpenDate = tradeParticipation.OpenDate,
                ReminderDate = tradeParticipation.ReminderDate,
                TradeParticipationType = tradeParticipation.TradeParticipationType,
                ComparisonTradeParticipationId = tradeParticipation.ComparisonTradeParticipationId,
                QuoteFile = tradeParticipation.QuoteFile,
                TransmittalId = tradeParticipation.TransmittalId,
                SafetyRating = tradeParticipation.SafetyRating,
                PaymentTerms = tradeParticipation.PaymentTerms,
                TradeName = tradeParticipation.Trade?.Name,
                ProjectName = tradeParticipation.Trade?.Project?.Name,
                SubContractorName = subContractorName,
                ContactPersonName = tradeParticipation.ContactPeople != null ?
                    $"{tradeParticipation.ContactPeople.FirstName} {tradeParticipation.ContactPeople.LastName}" : null,
                ChildParticipationsCount = tradeParticipation.InverseComparisonTradeParticipation.Count
            };
        }

        public async Task<IEnumerable<TradeParticipationDTO>> GetTradeParticipationsByTradeIdAsync(int tradeId)
        {
            // Check if the trade exists
            var trade = await _context.Trades.FindAsync(tradeId);
            if (trade == null)
                throw new KeyNotFoundException($"Trade with ID {tradeId} not found");

            return await _context.TradeParticipations
                .Include(tp => tp.Trade)
                .Include(tp => tp.Trade.Project)
                .Include(tp => tp.ContactPeople)
                .Include(tp => tp.InverseComparisonTradeParticipation)
                .Where(tp => tp.TradeId == tradeId)
                .Select(tp => new TradeParticipationDTO
                {
                    TradeParticipationId = tp.TradeParticipationId,
                    TradeId = tp.TradeId,
                    SubContractorId = tp.SubContractorId,
                    ContactPeopleId = tp.ContactPeopleId,
                    InvitationDate = tp.InvitationDate,
                    QuoteDate = tp.QuoteDate,
                    QuoteDueDate = tp.QuoteDueDate,
                    Amount = tp.Amount,
                    Status = tp.Status,
                    Rank = tp.Rank,
                    Comments = tp.Comments,
                    InternalComments = tp.InternalComments,
                    PulledOut = tp.PulledOut,
                    OpenDate = tp.OpenDate,
                    ReminderDate = tp.ReminderDate,
                    TradeParticipationType = tp.TradeParticipationType,
                    ComparisonTradeParticipationId = tp.ComparisonTradeParticipationId,
                    QuoteFile = tp.QuoteFile,
                    TransmittalId = tp.TransmittalId,
                    SafetyRating = tp.SafetyRating,
                    PaymentTerms = tp.PaymentTerms,
                    TradeName = tp.Trade.Name,
                    ProjectName = tp.Trade.Project.Name,
                    SubContractorName = tp.SubContractorId.HasValue ?
                        GetSubContractorName(tp.SubContractorId.Value) : null,
                    ContactPersonName = tp.ContactPeople != null ?
                        $"{tp.ContactPeople.FirstName} {tp.ContactPeople.LastName}" : null,
                    ChildParticipationsCount = tp.InverseComparisonTradeParticipation.Count
                })
                .ToListAsync();
        }

        public async Task<IEnumerable<TradeParticipationDTO>> GetTradeParticipationsBySubContractorIdAsync(int subContractorId)
        {
            // Check if the subcontractor exists
            var subContractor = await _context.SubContractors.FindAsync(subContractorId);
            if (subContractor == null)
                throw new KeyNotFoundException($"SubContractor with ID {subContractorId} not found");

            return await _context.TradeParticipations
                .Include(tp => tp.Trade)
                .Include(tp => tp.Trade.Project)
                .Include(tp => tp.ContactPeople)
                .Include(tp => tp.InverseComparisonTradeParticipation)
                .Where(tp => tp.SubContractorId == subContractorId)
                .Select(tp => new TradeParticipationDTO
                {
                    TradeParticipationId = tp.TradeParticipationId,
                    TradeId = tp.TradeId,
                    SubContractorId = tp.SubContractorId,
                    ContactPeopleId = tp.ContactPeopleId,
                    InvitationDate = tp.InvitationDate,
                    QuoteDate = tp.QuoteDate,
                    QuoteDueDate = tp.QuoteDueDate,
                    Amount = tp.Amount,
                    Status = tp.Status,
                    Rank = tp.Rank,
                    Comments = tp.Comments,
                    InternalComments = tp.InternalComments,
                    PulledOut = tp.PulledOut,
                    OpenDate = tp.OpenDate,
                    ReminderDate = tp.ReminderDate,
                    TradeParticipationType = tp.TradeParticipationType,
                    ComparisonTradeParticipationId = tp.ComparisonTradeParticipationId,
                    QuoteFile = tp.QuoteFile,
                    TransmittalId = tp.TransmittalId,
                    SafetyRating = tp.SafetyRating,
                    PaymentTerms = tp.PaymentTerms,
                    TradeName = tp.Trade != null ? tp.Trade.Name : null,
                    ProjectName = tp.Trade != null && tp.Trade.Project != null ? tp.Trade.Project.Name : null,
                    SubContractorName = subContractor.Name,
                    ContactPersonName = tp.ContactPeople != null ?
                        $"{tp.ContactPeople.FirstName} {tp.ContactPeople.LastName}" : null,
                    ChildParticipationsCount = tp.InverseComparisonTradeParticipation.Count
                })
                .ToListAsync();
        }

        public async Task<IEnumerable<TradeParticipationDTO>> GetChildTradeParticipationsAsync(int parentId)
        {
            // Check if the parent trade participation exists
            var parentTradeParticipation = await _context.TradeParticipations.FindAsync(parentId);
            if (parentTradeParticipation == null)
                throw new KeyNotFoundException($"Parent TradeParticipation with ID {parentId} not found");

            return await _context.TradeParticipations
                .Include(tp => tp.Trade)
                .Include(tp => tp.Trade.Project)
                .Include(tp => tp.ContactPeople)
                .Include(tp => tp.InverseComparisonTradeParticipation)
                .Where(tp => tp.ComparisonTradeParticipationId == parentId)
                .Select(tp => new TradeParticipationDTO
                {
                    TradeParticipationId = tp.TradeParticipationId,
                    TradeId = tp.TradeId,
                    SubContractorId = tp.SubContractorId,
                    ContactPeopleId = tp.ContactPeopleId,
                    InvitationDate = tp.InvitationDate,
                    QuoteDate = tp.QuoteDate,
                    QuoteDueDate = tp.QuoteDueDate,
                    Amount = tp.Amount,
                    Status = tp.Status,
                    Rank = tp.Rank,
                    Comments = tp.Comments,
                    InternalComments = tp.InternalComments,
                    PulledOut = tp.PulledOut,
                    OpenDate = tp.OpenDate,
                    ReminderDate = tp.ReminderDate,
                    TradeParticipationType = tp.TradeParticipationType,
                    ComparisonTradeParticipationId = tp.ComparisonTradeParticipationId,
                    QuoteFile = tp.QuoteFile,
                    TransmittalId = tp.TransmittalId,
                    SafetyRating = tp.SafetyRating,
                    PaymentTerms = tp.PaymentTerms,
                    TradeName = tp.Trade != null ? tp.Trade.Name : null,
                    ProjectName = tp.Trade != null && tp.Trade.Project != null ? tp.Trade.Project.Name : null,
                    SubContractorName = tp.SubContractorId.HasValue ?
                        GetSubContractorName(tp.SubContractorId.Value) : null,
                    ContactPersonName = tp.ContactPeople != null ?
                        $"{tp.ContactPeople.FirstName} {tp.ContactPeople.LastName}" : null,
                    ChildParticipationsCount = tp.InverseComparisonTradeParticipation.Count
                })
                .ToListAsync();
        }

        public async Task<TradeParticipationDTO> CreateTradeParticipationAsync(TradeParticipationCreateDTO tradeParticipationDto)
        {
            // Check if the trade exists
            var trade = await _context.Trades.FindAsync(tradeParticipationDto.TradeId);
            if (trade == null)
                throw new KeyNotFoundException($"Trade with ID {tradeParticipationDto.TradeId} not found");

            // Check if the subcontractor exists (if provided)
            if (tradeParticipationDto.SubContractorId.HasValue)
            {
                var subContractor = await _context.SubContractors.FindAsync(tradeParticipationDto.SubContractorId.Value);
                if (subContractor == null)
                    throw new KeyNotFoundException($"SubContractor with ID {tradeParticipationDto.SubContractorId.Value} not found");
            }

            // Check if the contact person exists (if provided)
            if (tradeParticipationDto.ContactPeopleId.HasValue)
            {
                var contactPerson = await _context.People.FindAsync(tradeParticipationDto.ContactPeopleId.Value);
                if (contactPerson == null)
                    throw new KeyNotFoundException($"Contact Person with ID {tradeParticipationDto.ContactPeopleId.Value} not found");
            }

            // Check if the comparison trade participation exists (if provided)
            if (tradeParticipationDto.ComparisonTradeParticipationId.HasValue)
            {
                var comparisonTradeParticipation = await _context.TradeParticipations.FindAsync(tradeParticipationDto.ComparisonTradeParticipationId.Value);
                if (comparisonTradeParticipation == null)
                    throw new KeyNotFoundException($"Comparison TradeParticipation with ID {tradeParticipationDto.ComparisonTradeParticipationId.Value} not found");
            }

            var tradeParticipation = new TradeParticipation
            {
                TradeId = tradeParticipationDto.TradeId,
                SubContractorId = tradeParticipationDto.SubContractorId,
                ContactPeopleId = tradeParticipationDto.ContactPeopleId,
                InvitationDate = tradeParticipationDto.InvitationDate,
                QuoteDate = tradeParticipationDto.QuoteDate,
                QuoteDueDate = tradeParticipationDto.QuoteDueDate,
                Amount = tradeParticipationDto.Amount,
                Status = tradeParticipationDto.Status,
                Rank = tradeParticipationDto.Rank,
                Comments = tradeParticipationDto.Comments,
                InternalComments = tradeParticipationDto.InternalComments,
                PulledOut = tradeParticipationDto.PulledOut,
                OpenDate = tradeParticipationDto.OpenDate,
                ReminderDate = tradeParticipationDto.ReminderDate,
                TradeParticipationType = tradeParticipationDto.TradeParticipationType,
                ComparisonTradeParticipationId = tradeParticipationDto.ComparisonTradeParticipationId,
                QuoteFile = tradeParticipationDto.QuoteFile,
                TransmittalId = tradeParticipationDto.TransmittalId,
                SafetyRating = tradeParticipationDto.SafetyRating,
                PaymentTerms = tradeParticipationDto.PaymentTerms,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow,
                CreatedPeopleId = _userContextService.GetCurrentUserId(),
                ModifiedPeopleId = _userContextService.GetCurrentUserId()
            };

            _context.TradeParticipations.Add(tradeParticipation);
            await _context.SaveChangesAsync();

            return await GetTradeParticipationByIdAsync(tradeParticipation.TradeParticipationId);
        }

        public async Task<TradeParticipationDTO> UpdateTradeParticipationAsync(int id, TradeParticipationUpdateDTO tradeParticipationDto)
        {
            var tradeParticipation = await _context.TradeParticipations.FindAsync(id);

            if (tradeParticipation == null)
                throw new KeyNotFoundException($"TradeParticipation with ID {id} not found");

            // Check if the subcontractor exists (if provided)
            if (tradeParticipationDto.SubContractorId.HasValue)
            {
                var subContractor = await _context.SubContractors.FindAsync(tradeParticipationDto.SubContractorId.Value);
                if (subContractor == null)
                    throw new KeyNotFoundException($"SubContractor with ID {tradeParticipationDto.SubContractorId.Value} not found");

                tradeParticipation.SubContractorId = tradeParticipationDto.SubContractorId;
            }

            // Check if the contact person exists (if provided)
            if (tradeParticipationDto.ContactPeopleId.HasValue)
            {
                var contactPerson = await _context.People.FindAsync(tradeParticipationDto.ContactPeopleId.Value);
                if (contactPerson == null)
                    throw new KeyNotFoundException($"Contact Person with ID {tradeParticipationDto.ContactPeopleId.Value} not found");

                tradeParticipation.ContactPeopleId = tradeParticipationDto.ContactPeopleId;
            }

            // Check if the comparison trade participation exists (if provided)
            if (tradeParticipationDto.ComparisonTradeParticipationId.HasValue)
            {
                // Prevent circular reference (a trade participation cannot be its own parent)
                if (tradeParticipationDto.ComparisonTradeParticipationId.Value == id)
                    throw new InvalidOperationException("A trade participation cannot be its own parent");

                var comparisonTradeParticipation = await _context.TradeParticipations.FindAsync(tradeParticipationDto.ComparisonTradeParticipationId.Value);
                if (comparisonTradeParticipation == null)
                    throw new KeyNotFoundException($"Comparison TradeParticipation with ID {tradeParticipationDto.ComparisonTradeParticipationId.Value} not found");

                tradeParticipation.ComparisonTradeParticipationId = tradeParticipationDto.ComparisonTradeParticipationId;
            }

            if (tradeParticipationDto.InvitationDate.HasValue)
                tradeParticipation.InvitationDate = tradeParticipationDto.InvitationDate;

            if (tradeParticipationDto.QuoteDate.HasValue)
                tradeParticipation.QuoteDate = tradeParticipationDto.QuoteDate;

            if (tradeParticipationDto.QuoteDueDate.HasValue)
                tradeParticipation.QuoteDueDate = tradeParticipationDto.QuoteDueDate;

            if (tradeParticipationDto.Amount.HasValue)
                tradeParticipation.Amount = tradeParticipationDto.Amount;

            if (tradeParticipationDto.Status != null)
                tradeParticipation.Status = tradeParticipationDto.Status;

            if (tradeParticipationDto.Rank.HasValue)
                tradeParticipation.Rank = tradeParticipationDto.Rank;

            if (tradeParticipationDto.Comments != null)
                tradeParticipation.Comments = tradeParticipationDto.Comments;

            if (tradeParticipationDto.InternalComments != null)
                tradeParticipation.InternalComments = tradeParticipationDto.InternalComments;

            if (tradeParticipationDto.PulledOut.HasValue)
                tradeParticipation.PulledOut = tradeParticipationDto.PulledOut;

            if (tradeParticipationDto.OpenDate.HasValue)
                tradeParticipation.OpenDate = tradeParticipationDto.OpenDate;

            if (tradeParticipationDto.ReminderDate.HasValue)
                tradeParticipation.ReminderDate = tradeParticipationDto.ReminderDate;

            if (tradeParticipationDto.TradeParticipationType != null)
                tradeParticipation.TradeParticipationType = tradeParticipationDto.TradeParticipationType;

            if (tradeParticipationDto.QuoteFile != null)
                tradeParticipation.QuoteFile = tradeParticipationDto.QuoteFile;

            if (tradeParticipationDto.TransmittalId.HasValue)
                tradeParticipation.TransmittalId = tradeParticipationDto.TransmittalId;

            if (tradeParticipationDto.SafetyRating != null)
                tradeParticipation.SafetyRating = tradeParticipationDto.SafetyRating;

            if (tradeParticipationDto.PaymentTerms != null)
                tradeParticipation.PaymentTerms = tradeParticipationDto.PaymentTerms;

            tradeParticipation.ModifiedDate = DateTime.UtcNow;
            tradeParticipation.ModifiedPeopleId = _userContextService.GetCurrentUserId();

            await _context.SaveChangesAsync();

            return await GetTradeParticipationByIdAsync(id);
        }

        public async Task<bool> DeleteTradeParticipationAsync(int id)
        {
            var tradeParticipation = await _context.TradeParticipations
                .Include(tp => tp.InverseComparisonTradeParticipation)
                .FirstOrDefaultAsync(tp => tp.TradeParticipationId == id);

            if (tradeParticipation == null)
                return false;

            // Check if there are any child trade participations
            if (tradeParticipation.InverseComparisonTradeParticipation.Any())
                throw new InvalidOperationException("Cannot delete trade participation that has child trade participations. Delete the child trade participations first.");

            _context.TradeParticipations.Remove(tradeParticipation);
            await _context.SaveChangesAsync();

            return true;
        }
    }
}
