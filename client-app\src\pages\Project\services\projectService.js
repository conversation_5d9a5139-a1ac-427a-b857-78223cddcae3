import axios from 'axios';

const API_URL = 'https://localhost:7035/api/Projects';

const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});

api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = localStorage.getItem('refreshToken');
        if (!refreshToken) {
          throw new Error('No refresh token available');
        }

        const response = await axios.post('https://localhost:7035/api/auth/refresh-token', {
          refreshToken: refreshToken
        });

        const { token: newToken, refreshToken: newRefreshToken } = response.data;
        localStorage.setItem('token', newToken);
        localStorage.setItem('refreshToken', newRefreshToken);

        originalRequest.headers['Authorization'] = `Bearer ${newToken}`;
        return api(originalRequest);
      } catch (refreshError) {
        localStorage.removeItem('token');
        localStorage.removeItem('refreshToken');
        localStorage.removeItem('user');
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

const projectService = {
  getAllProjects: async () => {
    const response = await api.get('/');
    return response.data;
  },

  getProjectById: async (id) => {
    const response = await api.get(`/${id}`);
    return response.data;
  },

  getProjectsByBusinessUnit: async (businessUnitId) => {
    const response = await api.get(`/BusinessUnit/${businessUnitId}`);
    return response.data;
  },

  createProject: async (projectData) => {
    const response = await api.post('/', projectData);
    return response.data;
  },

  updateProject: async (id, projectData) => {
    const response = await api.put(`/${id}`, projectData);
    return response.data;
  },

  deleteProject: async (id) => {
    const response = await api.delete(`/${id}`);
    return response.data;
  }
};

export default projectService;
