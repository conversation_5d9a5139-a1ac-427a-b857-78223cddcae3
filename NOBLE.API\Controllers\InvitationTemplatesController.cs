using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class InvitationTemplatesController : ControllerBase
    {
        private readonly IInvitationTemplateService _invitationTemplateService;
        
        public InvitationTemplatesController(IInvitationTemplateService invitationTemplateService)
        {
            _invitationTemplateService = invitationTemplateService;
        }
        
        // GET: api/InvitationTemplates
        [HttpGet]
        public async Task<ActionResult<IEnumerable<InvitationTemplateDTO>>> GetInvitationTemplates()
        {
            var invitationTemplates = await _invitationTemplateService.GetAllInvitationTemplatesAsync();
            return Ok(invitationTemplates);
        }
        
        // GET: api/InvitationTemplates/5
        [HttpGet("{id}")]
        public async Task<ActionResult<InvitationTemplateDTO>> GetInvitationTemplate(int id)
        {
            try
            {
                var invitationTemplate = await _invitationTemplateService.GetInvitationTemplateByIdAsync(id);
                return Ok(invitationTemplate);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // POST: api/InvitationTemplates
        [HttpPost]
        public async Task<ActionResult<InvitationTemplateDTO>> CreateInvitationTemplate(InvitationTemplateCreateDTO invitationTemplateDto)
        {
            try
            {
                var createdInvitationTemplate = await _invitationTemplateService.CreateInvitationTemplateAsync(invitationTemplateDto);
                return CreatedAtAction(nameof(GetInvitationTemplate), new { id = createdInvitationTemplate.InvitationTemplateId }, createdInvitationTemplate);
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // PUT: api/InvitationTemplates/5
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateInvitationTemplate(int id, InvitationTemplateUpdateDTO invitationTemplateDto)
        {
            try
            {
                var updatedInvitationTemplate = await _invitationTemplateService.UpdateInvitationTemplateAsync(id, invitationTemplateDto);
                return Ok(updatedInvitationTemplate);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // DELETE: api/InvitationTemplates/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteInvitationTemplate(int id)
        {
            try
            {
                var result = await _invitationTemplateService.DeleteInvitationTemplateAsync(id);
                
                if (!result)
                    return NotFound(new { message = $"InvitationTemplate with ID {id} not found" });
                    
                return NoContent();
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
    }
}
