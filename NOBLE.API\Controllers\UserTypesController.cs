using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class UserTypesController : ControllerBase
    {
        private readonly IUserTypeService _userTypeService;
        
        public UserTypesController(IUserTypeService userTypeService)
        {
            _userTypeService = userTypeService;
        }
        
        // GET: api/UserTypes
        [HttpGet]
        public async Task<ActionResult<IEnumerable<UserTypeDTO>>> GetUserTypes()
        {
            var userTypes = await _userTypeService.GetAllUserTypesAsync();
            return Ok(userTypes);
        }
        
        // GET: api/UserTypes/5
        [HttpGet("{id}")]
        public async Task<ActionResult<UserTypeDTO>> GetUserType(int id)
        {
            try
            {
                var userType = await _userTypeService.GetUserTypeByIdAsync(id);
                return Ok(userType);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // POST: api/UserTypes
        [HttpPost]
        public async Task<ActionResult<UserTypeDTO>> CreateUserType(UserTypeCreateDTO userTypeDto)
        {
            try
            {
                var createdUserType = await _userTypeService.CreateUserTypeAsync(userTypeDto);
                return CreatedAtAction(nameof(GetUserType), new { id = createdUserType.UserTypeId }, createdUserType);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // PUT: api/UserTypes/5
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateUserType(int id, UserTypeUpdateDTO userTypeDto)
        {
            try
            {
                var updatedUserType = await _userTypeService.UpdateUserTypeAsync(id, userTypeDto);
                return Ok(updatedUserType);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // DELETE: api/UserTypes/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteUserType(int id)
        {
            try
            {
                var result = await _userTypeService.DeleteUserTypeAsync(id);
                
                if (!result)
                    return NotFound(new { message = $"UserType with ID {id} not found" });
                    
                return NoContent();
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
    }
}
