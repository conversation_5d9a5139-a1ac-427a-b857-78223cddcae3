using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class TradesController : ControllerBase
    {
        private readonly ITradeService _tradeService;

        public TradesController(ITradeService tradeService)
        {
            _tradeService = tradeService;
        }
         
        [HttpGet]
        public async Task<ActionResult<IEnumerable<TradeDTO>>> GetTrades()
        {
            var trades = await _tradeService.GetAllTradesAsync();
            return Ok(trades);
        }
         
        [HttpGet("{id}")]
        public async Task<ActionResult<TradeDTO>> GetTrade(int id)
        {
            try
            {
                var trade = await _tradeService.GetTradeByIdAsync(id);
                return Ok(trade);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
         
        [HttpGet("Project/{projectId}")]
        public async Task<ActionResult<IEnumerable<TradeDTO>>> GetTradesByProject(int projectId)
        {
            try
            {
                var trades = await _tradeService.GetTradesByProjectIdAsync(projectId);
                return Ok(trades);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
         
        [HttpPost]
        public async Task<ActionResult<TradeDTO>> CreateTrade(TradeCreateDTO tradeDto)
        {
            try
            {
                var createdTrade = await _tradeService.CreateTradeAsync(tradeDto);
                return CreatedAtAction(nameof(GetTrade), new { id = createdTrade.TradeId }, createdTrade);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
         
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateTrade(int id, TradeUpdateDTO tradeDto)
        {
            try
            {
                var updatedTrade = await _tradeService.UpdateTradeAsync(id, tradeDto);
                return Ok(updatedTrade);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
         
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteTrade(int id)
        {
            try
            {
                var result = await _tradeService.DeleteTradeAsync(id);

                if (!result)
                    return NotFound(new { message = $"Trade with ID {id} not found" });

                return NoContent();
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
    }
}
