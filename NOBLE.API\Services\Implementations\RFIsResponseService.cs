using Microsoft.EntityFrameworkCore;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Models.Generated;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Services.Implementations
{
    public class RFIsResponseService : IRFIsResponseService
    {
        private readonly NobleDbContext _context;
        private readonly IUserContextService _userContextService;

        public RFIsResponseService(NobleDbContext context, IUserContextService userContextService)
        {
            _context = context;
            _userContextService = userContextService;
        }

        public async Task<IEnumerable<RFIsResponseDTO>> GetAllRFIsResponsesAsync()
        {
            return await _context.RfisResponses
                .Include(r => r.Rfi)
                .Include(r => r.Rfi.Project)
                .Select(r => new RFIsResponseDTO
                {
                    ResponseId = r.ResponseId,
                    RFIId = r.Rfiid,
                    RFINumber = r.Rfinumber,
                    ResponseNumber = r.ResponseNumber,
                    ResponseFrom = r.ResponseFrom,
                    ResponseMessage = r.ResponseMessage,
                    ResponseDate = r.ResponseDate,
                    ResponseFolderPath = r.ResponseFolderPath,
                    CreatedDate = r.CreatedDate,
                    CreatedBy = r.CreatedBy,
                    ModifiedDate = r.ModifiedDate,
                    ModifiedBy = r.ModifiedBy,
                    RFISubject = r.Rfi.Subject,
                    ProjectName = r.Rfi.Project.Name,
                    AttachmentCount = _context.RfisResponseAttachments.Count(a => a.ResponseId == r.ResponseId)
                })
                .ToListAsync();
        }

        public async Task<RFIsResponseDTO> GetRFIsResponseByIdAsync(int id)
        {
            var rfisResponse = await _context.RfisResponses
                .Include(r => r.Rfi)
                .Include(r => r.Rfi.Project)
                .FirstOrDefaultAsync(r => r.ResponseId == id);
            
            if (rfisResponse == null)
                throw new KeyNotFoundException($"RFIsResponse with ID {id} not found");
                
            return new RFIsResponseDTO
            {
                ResponseId = rfisResponse.ResponseId,
                RFIId = rfisResponse.Rfiid,
                RFINumber = rfisResponse.Rfinumber,
                ResponseNumber = rfisResponse.ResponseNumber,
                ResponseFrom = rfisResponse.ResponseFrom,
                ResponseMessage = rfisResponse.ResponseMessage,
                ResponseDate = rfisResponse.ResponseDate,
                ResponseFolderPath = rfisResponse.ResponseFolderPath,
                CreatedDate = rfisResponse.CreatedDate,
                CreatedBy = rfisResponse.CreatedBy,
                ModifiedDate = rfisResponse.ModifiedDate,
                ModifiedBy = rfisResponse.ModifiedBy,
                RFISubject = rfisResponse.Rfi.Subject,
                ProjectName = rfisResponse.Rfi.Project.Name,
                AttachmentCount = await _context.RfisResponseAttachments.CountAsync(a => a.ResponseId == rfisResponse.ResponseId)
            };
        }

        public async Task<IEnumerable<RFIsResponseDTO>> GetRFIsResponsesByRFIIdAsync(int rfiId)
        {
            // Check if the RFI exists
            var rfi = await _context.Rfis.FindAsync(rfiId);
            if (rfi == null)
                throw new KeyNotFoundException($"RFI with ID {rfiId} not found");
                
            return await _context.RfisResponses
                .Include(r => r.Rfi)
                .Include(r => r.Rfi.Project)
                .Where(r => r.Rfiid == rfiId)
                .Select(r => new RFIsResponseDTO
                {
                    ResponseId = r.ResponseId,
                    RFIId = r.Rfiid,
                    RFINumber = r.Rfinumber,
                    ResponseNumber = r.ResponseNumber,
                    ResponseFrom = r.ResponseFrom,
                    ResponseMessage = r.ResponseMessage,
                    ResponseDate = r.ResponseDate,
                    ResponseFolderPath = r.ResponseFolderPath,
                    CreatedDate = r.CreatedDate,
                    CreatedBy = r.CreatedBy,
                    ModifiedDate = r.ModifiedDate,
                    ModifiedBy = r.ModifiedBy,
                    RFISubject = r.Rfi.Subject,
                    ProjectName = r.Rfi.Project.Name,
                    AttachmentCount = _context.RfisResponseAttachments.Count(a => a.ResponseId == r.ResponseId)
                })
                .ToListAsync();
        }

        public async Task<RFIsResponseDTO> CreateRFIsResponseAsync(RFIsResponseCreateDTO rfisResponseDto)
        {
            // Check if the RFI exists
            var rfi = await _context.Rfis.FindAsync(rfisResponseDto.RFIId);
            if (rfi == null)
                throw new KeyNotFoundException($"RFI with ID {rfisResponseDto.RFIId} not found");
                
            var currentUser = _userContextService.GetCurrentUserName();
            
            var rfisResponse = new RfisResponse
            {
                Rfiid = rfisResponseDto.RFIId,
                Rfinumber = rfisResponseDto.RFINumber,
                ResponseNumber = rfisResponseDto.ResponseNumber,
                ResponseFrom = rfisResponseDto.ResponseFrom,
                ResponseMessage = rfisResponseDto.ResponseMessage,
                ResponseDate = rfisResponseDto.ResponseDate,
                ResponseFolderPath = rfisResponseDto.ResponseFolderPath,
                CreatedDate = DateTime.UtcNow,
                CreatedBy = currentUser,
                ModifiedDate = DateTime.UtcNow,
                ModifiedBy = currentUser
            };
            
            _context.RfisResponses.Add(rfisResponse);
            await _context.SaveChangesAsync();
            
            return await GetRFIsResponseByIdAsync(rfisResponse.ResponseId);
        }

        public async Task<RFIsResponseDTO> UpdateRFIsResponseAsync(int id, RFIsResponseUpdateDTO rfisResponseDto)
        {
            var rfisResponse = await _context.RfisResponses.FindAsync(id);
            
            if (rfisResponse == null)
                throw new KeyNotFoundException($"RFIsResponse with ID {id} not found");
                
            var currentUser = _userContextService.GetCurrentUserName();
                
            rfisResponse.Rfinumber = rfisResponseDto.RFINumber;
            rfisResponse.ResponseNumber = rfisResponseDto.ResponseNumber;
            rfisResponse.ResponseFrom = rfisResponseDto.ResponseFrom;
            rfisResponse.ResponseMessage = rfisResponseDto.ResponseMessage;
            rfisResponse.ResponseDate = rfisResponseDto.ResponseDate;
            rfisResponse.ResponseFolderPath = rfisResponseDto.ResponseFolderPath;
            rfisResponse.ModifiedDate = DateTime.UtcNow;
            rfisResponse.ModifiedBy = currentUser;
            
            await _context.SaveChangesAsync();
            
            return await GetRFIsResponseByIdAsync(id);
        }

        public async Task<bool> DeleteRFIsResponseAsync(int id)
        {
            var rfisResponse = await _context.RfisResponses.FindAsync(id);
            
            if (rfisResponse == null)
                return false;
                
            // Check if there are any attachments
            var hasAttachments = await _context.RfisResponseAttachments.AnyAsync(a => a.ResponseId == id);
            
            if (hasAttachments)
                throw new InvalidOperationException("Cannot delete RFI response that has attachments. Delete the attachments first.");
                
            _context.RfisResponses.Remove(rfisResponse);
            await _context.SaveChangesAsync();
            
            return true;
        }
    }
}
