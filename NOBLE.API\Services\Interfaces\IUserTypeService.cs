using NOBLE.API.Models.DTOs;

namespace NOBLE.API.Services.Interfaces
{
    public interface IUserTypeService
    {
        Task<IEnumerable<UserTypeDTO>> GetAllUserTypesAsync();
        Task<UserTypeDTO> GetUserTypeByIdAsync(int id);
        Task<UserTypeDTO> CreateUserTypeAsync(UserTypeCreateDTO userTypeDto);
        Task<UserTypeDTO> UpdateUserTypeAsync(int id, UserTypeUpdateDTO userTypeDto);
        Task<bool> DeleteUserTypeAsync(int id);
    }
}
