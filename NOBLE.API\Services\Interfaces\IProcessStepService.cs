using NOBLE.API.Models.DTOs;

namespace NOBLE.API.Services.Interfaces
{
    public interface IProcessStepService
    {
        Task<IEnumerable<ProcessStepDTO>> GetAllProcessStepsAsync();
        Task<ProcessStepDTO> GetProcessStepByIdAsync(int id);
        Task<IEnumerable<ProcessStepDTO>> GetProcessStepsByProcessIdAsync(int processId);
        Task<IEnumerable<ProcessStepDTO>> GetProcessStepsByAssignedPersonIdAsync(int assignedPersonId);
        Task<IEnumerable<ProcessStepDTO>> GetProcessStepsByTypeAsync(string type);
        Task<ProcessStepDTO> CreateProcessStepAsync(ProcessStepCreateDTO processStepDto);
        Task<ProcessStepDTO> UpdateProcessStepAsync(int id, ProcessStepUpdateDTO processStepDto);
        Task<bool> DeleteProcessStepAsync(int id);
    }
}
