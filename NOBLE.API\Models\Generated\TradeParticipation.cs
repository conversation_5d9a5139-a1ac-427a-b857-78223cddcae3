﻿using System;
using System.Collections.Generic;

namespace NOBLE.API.Models.Generated;

public partial class TradeParticipation
{
    public int TradeParticipationId { get; set; }

    public int? TradeId { get; set; }

    public int? SubContractorId { get; set; }

    public int? ContactPeopleId { get; set; }

    public DateTime? InvitationDate { get; set; }

    public DateTime? QuoteDate { get; set; }

    public DateTime? QuoteDueDate { get; set; }

    public decimal? Amount { get; set; }

    public string? Status { get; set; }

    public int? Rank { get; set; }

    public string? Comments { get; set; }

    public string? InternalComments { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public int CreatedPeopleId { get; set; }

    public int ModifiedPeopleId { get; set; }

    public bool? PulledOut { get; set; }

    public DateTime? OpenDate { get; set; }

    public DateTime? ReminderDate { get; set; }

    public string TradeParticipationType { get; set; } = null!;

    public int? ComparisonTradeParticipationId { get; set; }

    public string? QuoteFile { get; set; }

    public int? TransmittalId { get; set; }

    public string? SafetyRating { get; set; }

    public string? PaymentTerms { get; set; }

    public virtual TradeParticipation? ComparisonTradeParticipation { get; set; }

    public virtual Person? ContactPeople { get; set; }

    public virtual ICollection<TradeParticipation> InverseComparisonTradeParticipation { get; set; } = new List<TradeParticipation>();

    public virtual Trade? Trade { get; set; }
}
