using NOBLE.API.Models.DTOs;

namespace NOBLE.API.Services.Interfaces
{
    public interface IEOTService
    {
        Task<IEnumerable<EOTDTO>> GetAllEOTsAsync();
        Task<EOTDTO> GetEOTByIdAsync(int id);
        Task<IEnumerable<EOTDTO>> GetEOTsByProjectIdAsync(int projectId);
        Task<EOTDTO> CreateEOTAsync(EOTCreateDTO eotDto);
        Task<EOTDTO> UpdateEOTAsync(int id, EOTUpdateDTO eotDto);
        Task<bool> DeleteEOTAsync(int id);
    }
}
