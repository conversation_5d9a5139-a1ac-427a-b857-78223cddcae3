using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class ClientVariationsDetailsController : ControllerBase
    {
        private readonly IClientVariationsDetailService _clientVariationsDetailService;
        
        public ClientVariationsDetailsController(IClientVariationsDetailService clientVariationsDetailService)
        {
            _clientVariationsDetailService = clientVariationsDetailService;
        }
        
        // GET: api/ClientVariationsDetails
        [HttpGet]
        public async Task<ActionResult<IEnumerable<ClientVariationsDetailDTO>>> GetClientVariationsDetails()
        {
            var clientVariationsDetails = await _clientVariationsDetailService.GetAllClientVariationsDetailsAsync();
            return Ok(clientVariationsDetails);
        }
        
        // GET: api/ClientVariationsDetails/5
        [HttpGet("{id}")]
        public async Task<ActionResult<ClientVariationsDetailDTO>> GetClientVariationsDetail(int id)
        {
            try
            {
                var clientVariationsDetail = await _clientVariationsDetailService.GetClientVariationsDetailByIdAsync(id);
                return Ok(clientVariationsDetail);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // GET: api/ClientVariationsDetails/ClientVariation/5
        [HttpGet("ClientVariation/{clientVariationId}")]
        public async Task<ActionResult<IEnumerable<ClientVariationsDetailDTO>>> GetClientVariationsDetailsByClientVariation(int clientVariationId)
        {
            try
            {
                var clientVariationsDetails = await _clientVariationsDetailService.GetClientVariationsDetailsByClientVariationIdAsync(clientVariationId);
                return Ok(clientVariationsDetails);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // POST: api/ClientVariationsDetails
        [HttpPost]
        public async Task<ActionResult<ClientVariationsDetailDTO>> CreateClientVariationsDetail(ClientVariationsDetailCreateDTO clientVariationsDetailDto)
        {
            try
            {
                var createdClientVariationsDetail = await _clientVariationsDetailService.CreateClientVariationsDetailAsync(clientVariationsDetailDto);
                return CreatedAtAction(nameof(GetClientVariationsDetail), new { id = createdClientVariationsDetail.ClientVariationDetailId }, createdClientVariationsDetail);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // PUT: api/ClientVariationsDetails/5
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateClientVariationsDetail(int id, ClientVariationsDetailUpdateDTO clientVariationsDetailDto)
        {
            try
            {
                var updatedClientVariationsDetail = await _clientVariationsDetailService.UpdateClientVariationsDetailAsync(id, clientVariationsDetailDto);
                return Ok(updatedClientVariationsDetail);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // DELETE: api/ClientVariationsDetails/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteClientVariationsDetail(int id)
        {
            try
            {
                var result = await _clientVariationsDetailService.DeleteClientVariationsDetailAsync(id);
                
                if (!result)
                    return NotFound(new { message = $"ClientVariationsDetail with ID {id} not found" });
                    
                return NoContent();
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
    }
}
