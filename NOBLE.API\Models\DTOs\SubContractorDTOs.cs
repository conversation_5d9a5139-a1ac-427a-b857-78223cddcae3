using System.ComponentModel.DataAnnotations;

namespace NOBLE.API.Models.DTOs
{
    public class SubContractorDTO
    {
        public int SubContractorId { get; set; }
        public int? BusinessUnitId { get; set; }
        public string? Name { get; set; }
        public string? ShortName { get; set; }
        public string? Street { get; set; }
        public string? Locality { get; set; }
        public string? State { get; set; }
        public string? PostalCode { get; set; }
        public string? Comments { get; set; }
        public string? Account { get; set; }
        public string? ABN { get; set; }
        public string? Phone { get; set; }
        public string? Fax { get; set; }
        public string? Website { get; set; }
        public string? ACN { get; set; }
        public string? LicenceNumber { get; set; }
        public string? PrequalifiedForm { get; set; }
        public string? PublicLiabilityInsurance { get; set; }
        public string? WorkCoverInsurance { get; set; }
        public string? ProfessionalIndemnityInsurance { get; set; }
        public bool? DCContractor { get; set; }
        
        // Navigation properties for display purposes
        public string? BusinessUnitName { get; set; }
    }

    public class SubContractorCreateDTO
    {
        [Required]
        public int BusinessUnitId { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(50)]
        public string? ShortName { get; set; }
        
        [StringLength(100)]
        public string? Street { get; set; }
        
        [StringLength(100)]
        public string? Locality { get; set; }
        
        [StringLength(50)]
        public string? State { get; set; }
        
        [StringLength(20)]
        public string? PostalCode { get; set; }
        
        public string? Comments { get; set; }
        
        [StringLength(50)]
        public string? Account { get; set; }
        
        [StringLength(20)]
        public string? ABN { get; set; }
        
        [StringLength(20)]
        public string? Phone { get; set; }
        
        [StringLength(20)]
        public string? Fax { get; set; }
        
        [StringLength(100)]
        public string? Website { get; set; }
        
        [StringLength(20)]
        public string? ACN { get; set; }
        
        [StringLength(50)]
        public string? LicenceNumber { get; set; }
        
        [StringLength(255)]
        public string? PrequalifiedForm { get; set; }
        
        [StringLength(255)]
        public string? PublicLiabilityInsurance { get; set; }
        
        [StringLength(255)]
        public string? WorkCoverInsurance { get; set; }
        
        [StringLength(255)]
        public string? ProfessionalIndemnityInsurance { get; set; }
        
        public bool? DCContractor { get; set; }
    }

    public class SubContractorUpdateDTO
    {
        [Required]
        public int BusinessUnitId { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(50)]
        public string? ShortName { get; set; }
        
        [StringLength(100)]
        public string? Street { get; set; }
        
        [StringLength(100)]
        public string? Locality { get; set; }
        
        [StringLength(50)]
        public string? State { get; set; }
        
        [StringLength(20)]
        public string? PostalCode { get; set; }
        
        public string? Comments { get; set; }
        
        [StringLength(50)]
        public string? Account { get; set; }
        
        [StringLength(20)]
        public string? ABN { get; set; }
        
        [StringLength(20)]
        public string? Phone { get; set; }
        
        [StringLength(20)]
        public string? Fax { get; set; }
        
        [StringLength(100)]
        public string? Website { get; set; }
        
        [StringLength(20)]
        public string? ACN { get; set; }
        
        [StringLength(50)]
        public string? LicenceNumber { get; set; }
        
        [StringLength(255)]
        public string? PrequalifiedForm { get; set; }
        
        [StringLength(255)]
        public string? PublicLiabilityInsurance { get; set; }
        
        [StringLength(255)]
        public string? WorkCoverInsurance { get; set; }
        
        [StringLength(255)]
        public string? ProfessionalIndemnityInsurance { get; set; }
        
        public bool? DCContractor { get; set; }
    }
}
