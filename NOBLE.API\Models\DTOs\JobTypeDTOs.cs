using System.ComponentModel.DataAnnotations;

namespace NOBLE.API.Models.DTOs
{
    public class JobTypeDTO
    {
        public int JobTypeId { get; set; }
        public string? Name { get; set; }
    }

    public class JobTypeCreateDTO
    {
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
    }

    public class JobTypeUpdateDTO
    {
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
    }
}
