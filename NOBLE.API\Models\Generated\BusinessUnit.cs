﻿using System;
using System.Collections.Generic;

namespace NOBLE.API.Models.Generated;

public partial class BusinessUnit
{
    public int BusinessUnitId { get; set; }

    public string? Name { get; set; }

    public string? ProjectNumberFormat { get; set; }

    public int? UmpeopleId { get; set; }

    public int? EdpeopleId { get; set; }

    public decimal? TradeOverbudgetApproval { get; set; }

    public decimal? TradeAmountApproval { get; set; }

    public decimal? TradeComAmountApproval { get; set; }

    public int? TradeComOverBudget { get; set; }

    public decimal? TradeDaamountApproval { get; set; }

    public decimal? TradeUmoverbudgetApproval { get; set; }

    public decimal? VariationUmdaoverAmtApproval { get; set; }

    public string? ClaimSpecialNote { get; set; }

    public decimal? VariationSepAccUmapproval { get; set; }

    public decimal? VariationUmboqvcvdvapproval { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public int CreatedPeopleId { get; set; }

    public int ModifiedPeopleId { get; set; }

    public virtual ICollection<Person> People { get; set; } = new List<Person>();

    public virtual ICollection<Project> Projects { get; set; } = new List<Project>();

    public virtual ICollection<SubContractor> SubContractors { get; set; } = new List<SubContractor>();
}
