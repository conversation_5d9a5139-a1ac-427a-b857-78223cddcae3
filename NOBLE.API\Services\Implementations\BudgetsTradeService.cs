using Microsoft.EntityFrameworkCore;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Models.Generated;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Services.Implementations
{
    public class BudgetsTradeService : IBudgetsTradeService
    {
        private readonly NobleDbContext _context;
        private readonly IUserContextService _userContextService;

        public BudgetsTradeService(NobleDbContext context, IUserContextService userContextService)
        {
            _context = context;
            _userContextService = userContextService;
        }

        public async Task<IEnumerable<BudgetsTradeDTO>> GetAllBudgetsTradesAsync()
        {
            return await _context.BudgetsTrades
                .Include(bt => bt.Budget)
                .Include(bt => bt.Trade)
                .Include(bt => bt.Trade.Project)
                .Select(bt => new BudgetsTradeDTO
                {
                    BudgetId = bt.BudgetId,
                    TradeId = bt.TradeId,
                    BudgetAmount = bt.BudgetAmount,
                    BudgetAmountInitial = bt.BudgetAmountInitial,
                    BudgetAmountAllowance = bt.BudgetAmountAllowance,
                    BudgetAmountTradeInitial = bt.BudgetAmountTradeInitial,
                    BudgetDate = bt.BudgetDate,
                    BudgetCode = bt.Budget.Code,
                    TradeName = bt.Trade.Name,
                    ProjectName = bt.Trade.Project.Name
                })
                .ToListAsync();
        }

        public async Task<BudgetsTradeDTO> GetBudgetsTradeByIdsAsync(int budgetId, int tradeId)
        {
            var budgetsTrade = await _context.BudgetsTrades
                .Include(bt => bt.Budget)
                .Include(bt => bt.Trade)
                .Include(bt => bt.Trade.Project)
                .FirstOrDefaultAsync(bt => bt.BudgetId == budgetId && bt.TradeId == tradeId);
            
            if (budgetsTrade == null)
                throw new KeyNotFoundException($"BudgetsTrade with BudgetId {budgetId} and TradeId {tradeId} not found");
                
            return new BudgetsTradeDTO
            {
                BudgetId = budgetsTrade.BudgetId,
                TradeId = budgetsTrade.TradeId,
                BudgetAmount = budgetsTrade.BudgetAmount,
                BudgetAmountInitial = budgetsTrade.BudgetAmountInitial,
                BudgetAmountAllowance = budgetsTrade.BudgetAmountAllowance,
                BudgetAmountTradeInitial = budgetsTrade.BudgetAmountTradeInitial,
                BudgetDate = budgetsTrade.BudgetDate,
                BudgetCode = budgetsTrade.Budget.Code,
                TradeName = budgetsTrade.Trade.Name,
                ProjectName = budgetsTrade.Trade.Project.Name
            };
        }

        public async Task<IEnumerable<BudgetsTradeDTO>> GetBudgetsTradesByBudgetIdAsync(int budgetId)
        {
            // Check if the budget exists
            var budget = await _context.Budgets.FindAsync(budgetId);
            if (budget == null)
                throw new KeyNotFoundException($"Budget with ID {budgetId} not found");
                
            return await _context.BudgetsTrades
                .Include(bt => bt.Budget)
                .Include(bt => bt.Trade)
                .Include(bt => bt.Trade.Project)
                .Where(bt => bt.BudgetId == budgetId)
                .Select(bt => new BudgetsTradeDTO
                {
                    BudgetId = bt.BudgetId,
                    TradeId = bt.TradeId,
                    BudgetAmount = bt.BudgetAmount,
                    BudgetAmountInitial = bt.BudgetAmountInitial,
                    BudgetAmountAllowance = bt.BudgetAmountAllowance,
                    BudgetAmountTradeInitial = bt.BudgetAmountTradeInitial,
                    BudgetDate = bt.BudgetDate,
                    BudgetCode = bt.Budget.Code,
                    TradeName = bt.Trade.Name,
                    ProjectName = bt.Trade.Project.Name
                })
                .ToListAsync();
        }

        public async Task<IEnumerable<BudgetsTradeDTO>> GetBudgetsTradesByTradeIdAsync(int tradeId)
        {
            // Check if the trade exists
            var trade = await _context.Trades.FindAsync(tradeId);
            if (trade == null)
                throw new KeyNotFoundException($"Trade with ID {tradeId} not found");
                
            return await _context.BudgetsTrades
                .Include(bt => bt.Budget)
                .Include(bt => bt.Trade)
                .Include(bt => bt.Trade.Project)
                .Where(bt => bt.TradeId == tradeId)
                .Select(bt => new BudgetsTradeDTO
                {
                    BudgetId = bt.BudgetId,
                    TradeId = bt.TradeId,
                    BudgetAmount = bt.BudgetAmount,
                    BudgetAmountInitial = bt.BudgetAmountInitial,
                    BudgetAmountAllowance = bt.BudgetAmountAllowance,
                    BudgetAmountTradeInitial = bt.BudgetAmountTradeInitial,
                    BudgetDate = bt.BudgetDate,
                    BudgetCode = bt.Budget.Code,
                    TradeName = bt.Trade.Name,
                    ProjectName = bt.Trade.Project.Name
                })
                .ToListAsync();
        }

        public async Task<BudgetsTradeDTO> CreateBudgetsTradeAsync(BudgetsTradeCreateDTO budgetsTradeDto)
        {
            // Check if the budget exists
            var budget = await _context.Budgets.FindAsync(budgetsTradeDto.BudgetId);
            if (budget == null)
                throw new KeyNotFoundException($"Budget with ID {budgetsTradeDto.BudgetId} not found");
                
            // Check if the trade exists
            var trade = await _context.Trades.FindAsync(budgetsTradeDto.TradeId);
            if (trade == null)
                throw new KeyNotFoundException($"Trade with ID {budgetsTradeDto.TradeId} not found");
                
            // Check if the budget and trade are already linked
            var existingBudgetsTrade = await _context.BudgetsTrades
                .FirstOrDefaultAsync(bt => bt.BudgetId == budgetsTradeDto.BudgetId && bt.TradeId == budgetsTradeDto.TradeId);
                
            if (existingBudgetsTrade != null)
                throw new InvalidOperationException($"Budget with ID {budgetsTradeDto.BudgetId} is already linked to Trade with ID {budgetsTradeDto.TradeId}");
                
            var budgetsTrade = new BudgetsTrade
            {
                BudgetId = budgetsTradeDto.BudgetId,
                TradeId = budgetsTradeDto.TradeId,
                BudgetAmount = budgetsTradeDto.BudgetAmount,
                BudgetAmountInitial = budgetsTradeDto.BudgetAmountInitial,
                BudgetAmountAllowance = budgetsTradeDto.BudgetAmountAllowance,
                BudgetAmountTradeInitial = budgetsTradeDto.BudgetAmountTradeInitial,
                BudgetDate = budgetsTradeDto.BudgetDate,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow,
                CreatedPeopleId = _userContextService.GetCurrentUserId(),
                ModifiedPeopleId = _userContextService.GetCurrentUserId()
            };
            
            _context.BudgetsTrades.Add(budgetsTrade);
            await _context.SaveChangesAsync();
            
            return await GetBudgetsTradeByIdsAsync(budgetsTrade.BudgetId, budgetsTrade.TradeId);
        }

        public async Task<BudgetsTradeDTO> UpdateBudgetsTradeAsync(int budgetId, int tradeId, BudgetsTradeUpdateDTO budgetsTradeDto)
        {
            var budgetsTrade = await _context.BudgetsTrades
                .FirstOrDefaultAsync(bt => bt.BudgetId == budgetId && bt.TradeId == tradeId);
            
            if (budgetsTrade == null)
                throw new KeyNotFoundException($"BudgetsTrade with BudgetId {budgetId} and TradeId {tradeId} not found");
                
            budgetsTrade.BudgetAmount = budgetsTradeDto.BudgetAmount;
            budgetsTrade.BudgetAmountInitial = budgetsTradeDto.BudgetAmountInitial;
            budgetsTrade.BudgetAmountAllowance = budgetsTradeDto.BudgetAmountAllowance;
            budgetsTrade.BudgetAmountTradeInitial = budgetsTradeDto.BudgetAmountTradeInitial;
            budgetsTrade.BudgetDate = budgetsTradeDto.BudgetDate;
            budgetsTrade.ModifiedDate = DateTime.UtcNow;
            budgetsTrade.ModifiedPeopleId = _userContextService.GetCurrentUserId();
            
            await _context.SaveChangesAsync();
            
            return await GetBudgetsTradeByIdsAsync(budgetId, tradeId);
        }

        public async Task<bool> DeleteBudgetsTradeAsync(int budgetId, int tradeId)
        {
            var budgetsTrade = await _context.BudgetsTrades
                .FirstOrDefaultAsync(bt => bt.BudgetId == budgetId && bt.TradeId == tradeId);
            
            if (budgetsTrade == null)
                return false;
                
            _context.BudgetsTrades.Remove(budgetsTrade);
            await _context.SaveChangesAsync();
            
            return true;
        }
    }
}
