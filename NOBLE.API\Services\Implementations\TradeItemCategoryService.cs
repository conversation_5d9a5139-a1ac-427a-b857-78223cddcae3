using Microsoft.EntityFrameworkCore;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Models.Generated;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Services.Implementations
{
    public class TradeItemCategoryService : ITradeItemCategoryService
    {
        private readonly NobleDbContext _context;
        private readonly IUserContextService _userContextService;

        public TradeItemCategoryService(NobleDbContext context, IUserContextService userContextService)
        {
            _context = context;
            _userContextService = userContextService;
        }

        public async Task<IEnumerable<TradeItemCategoryDTO>> GetAllTradeItemCategoriesAsync()
        {
            return await _context.TradeItemCategories
                .Include(c => c.Trade)
                .Include(c => c.Trade.Project)
                .Include(c => c.TradeItems)
                .Select(c => new TradeItemCategoryDTO
                {
                    TradeItemCategoryId = c.TradeItemCategoryId,
                    TradeId = c.TradeId,
                    Name = c.Name,
                    ShortDescription = c.ShortDescription,
                    LongDescription = c.LongDescription,
                    DisplayOrder = c.DisplayOrder,
                    TradeName = c.Trade.Name,
                    ProjectName = c.Trade.Project.Name,
                    TradeItemsCount = c.TradeItems.Count
                })
                .ToListAsync();
        }

        public async Task<TradeItemCategoryDTO> GetTradeItemCategoryByIdAsync(int id)
        {
            var tradeItemCategory = await _context.TradeItemCategories
                .Include(c => c.Trade)
                .Include(c => c.Trade.Project)
                .Include(c => c.TradeItems)
                .FirstOrDefaultAsync(c => c.TradeItemCategoryId == id);
            
            if (tradeItemCategory == null)
                throw new KeyNotFoundException($"TradeItemCategory with ID {id} not found");
                
            return new TradeItemCategoryDTO
            {
                TradeItemCategoryId = tradeItemCategory.TradeItemCategoryId,
                TradeId = tradeItemCategory.TradeId,
                Name = tradeItemCategory.Name,
                ShortDescription = tradeItemCategory.ShortDescription,
                LongDescription = tradeItemCategory.LongDescription,
                DisplayOrder = tradeItemCategory.DisplayOrder,
                TradeName = tradeItemCategory.Trade.Name,
                ProjectName = tradeItemCategory.Trade.Project.Name,
                TradeItemsCount = tradeItemCategory.TradeItems.Count
            };
        }

        public async Task<IEnumerable<TradeItemCategoryDTO>> GetTradeItemCategoriesByTradeIdAsync(int tradeId)
        {
            // Check if the trade exists
            var trade = await _context.Trades.FindAsync(tradeId);
            if (trade == null)
                throw new KeyNotFoundException($"Trade with ID {tradeId} not found");
                
            return await _context.TradeItemCategories
                .Include(c => c.Trade)
                .Include(c => c.Trade.Project)
                .Include(c => c.TradeItems)
                .Where(c => c.TradeId == tradeId)
                .Select(c => new TradeItemCategoryDTO
                {
                    TradeItemCategoryId = c.TradeItemCategoryId,
                    TradeId = c.TradeId,
                    Name = c.Name,
                    ShortDescription = c.ShortDescription,
                    LongDescription = c.LongDescription,
                    DisplayOrder = c.DisplayOrder,
                    TradeName = c.Trade.Name,
                    ProjectName = c.Trade.Project.Name,
                    TradeItemsCount = c.TradeItems.Count
                })
                .ToListAsync();
        }

        public async Task<TradeItemCategoryDTO> CreateTradeItemCategoryAsync(TradeItemCategoryCreateDTO tradeItemCategoryDto)
        {
            // Check if the trade exists
            var trade = await _context.Trades.FindAsync(tradeItemCategoryDto.TradeId);
            if (trade == null)
                throw new KeyNotFoundException($"Trade with ID {tradeItemCategoryDto.TradeId} not found");
                
            var tradeItemCategory = new TradeItemCategory
            {
                TradeId = tradeItemCategoryDto.TradeId,
                Name = tradeItemCategoryDto.Name,
                ShortDescription = tradeItemCategoryDto.ShortDescription,
                LongDescription = tradeItemCategoryDto.LongDescription,
                DisplayOrder = tradeItemCategoryDto.DisplayOrder,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow,
                CreatedPeopleId = _userContextService.GetCurrentUserId(),
                ModifiedPeopleId = _userContextService.GetCurrentUserId()
            };
            
            _context.TradeItemCategories.Add(tradeItemCategory);
            await _context.SaveChangesAsync();
            
            return await GetTradeItemCategoryByIdAsync(tradeItemCategory.TradeItemCategoryId);
        }

        public async Task<TradeItemCategoryDTO> UpdateTradeItemCategoryAsync(int id, TradeItemCategoryUpdateDTO tradeItemCategoryDto)
        {
            var tradeItemCategory = await _context.TradeItemCategories.FindAsync(id);
            
            if (tradeItemCategory == null)
                throw new KeyNotFoundException($"TradeItemCategory with ID {id} not found");
                
            if (tradeItemCategoryDto.Name != null)
                tradeItemCategory.Name = tradeItemCategoryDto.Name;
                
            if (tradeItemCategoryDto.ShortDescription != null)
                tradeItemCategory.ShortDescription = tradeItemCategoryDto.ShortDescription;
                
            if (tradeItemCategoryDto.LongDescription != null)
                tradeItemCategory.LongDescription = tradeItemCategoryDto.LongDescription;
                
            if (tradeItemCategoryDto.DisplayOrder != null)
                tradeItemCategory.DisplayOrder = tradeItemCategoryDto.DisplayOrder;
                
            tradeItemCategory.ModifiedDate = DateTime.UtcNow;
            tradeItemCategory.ModifiedPeopleId = _userContextService.GetCurrentUserId();
            
            await _context.SaveChangesAsync();
            
            return await GetTradeItemCategoryByIdAsync(id);
        }

        public async Task<bool> DeleteTradeItemCategoryAsync(int id)
        {
            var tradeItemCategory = await _context.TradeItemCategories
                .Include(c => c.TradeItems)
                .FirstOrDefaultAsync(c => c.TradeItemCategoryId == id);
            
            if (tradeItemCategory == null)
                return false;
                
            // Check if there are any trade items
            if (tradeItemCategory.TradeItems.Any())
                throw new InvalidOperationException("Cannot delete trade item category that has trade items. Delete the trade items first.");
                
            _context.TradeItemCategories.Remove(tradeItemCategory);
            await _context.SaveChangesAsync();
            
            return true;
        }
    }
}
