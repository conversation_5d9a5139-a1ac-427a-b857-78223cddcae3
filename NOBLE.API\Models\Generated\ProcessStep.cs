﻿using System;
using System.Collections.Generic;

namespace NOBLE.API.Models.Generated;

public partial class ProcessStep
{
    public int ProcessStepId { get; set; }

    public int? ProcessId { get; set; }

    public int? AssignedPeopleId { get; set; }

    public int? ApprovedPeopleId { get; set; }

    public string? Type { get; set; }

    public string? Name { get; set; }

    public string? UserType { get; set; }

    public int? NumDays { get; set; }

    public DateTime? TargetDate { get; set; }

    public DateTime? ActualDate { get; set; }

    public string? Comments { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public int CreatedPeopleId { get; set; }

    public int ModifiedPeopleId { get; set; }

    public bool? Skip { get; set; }

    public virtual Process? Process { get; set; }
}
