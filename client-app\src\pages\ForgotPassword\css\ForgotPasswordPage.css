.page-container {
  display: flex;
  min-height: 100vh;
  background-color: #F5F7FA;
}

.left-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  background-color: #FFFFFF;
}

@media (max-width: 768px) {
  .left-section {
    display: none;
  }
}

.right-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 2rem;
}

@media (max-width: 768px) {
  .right-section {
    flex: 1;
  }
}

.hero-title {
  font-family: 'Poppins', 'Helvetica', 'Arial', sans-serif;
  font-size: 2.25rem;
  font-weight: 700;
  color: #1F2933;
  margin-bottom: 1rem;
  text-align: center;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: #3E4C59;
  margin-bottom: 2rem;
  text-align: center;
  max-width: 500px;
}

.logo {
  max-width: 200px;
  margin-bottom: 2rem;
}

.company-logo {
  max-width: 150px;
  margin-bottom: 2rem;
}

.learn-more-button {
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  font-size: 1rem;
  font-weight: 500;
  color: #0056B3;
  background-color: transparent;
  border: 1px solid #0056B3;
  border-radius: 4px;
  padding: 0.75rem 1.5rem;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  text-decoration: none;
}

.learn-more-button:hover {
  background-color: #0056B310;
}
