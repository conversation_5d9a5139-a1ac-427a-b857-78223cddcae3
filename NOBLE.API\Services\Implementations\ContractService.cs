using Microsoft.EntityFrameworkCore;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Models.Generated;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Services.Implementations
{
    public class ContractService : IContractService
    {
        private readonly NobleDbContext _context;
        private readonly IUserContextService _userContextService;

        public ContractService(NobleDbContext context, IUserContextService userContextService)
        {
            _context = context;
            _userContextService = userContextService;
        }

        public async Task<IEnumerable<ContractDTO>> GetAllContractsAsync()
        {
            return await _context.Contracts
                .Include(c => c.ParentContract)
                .Include(c => c.Trade)
                .Include(c => c.InverseParentContract)
                .Include(c => c.Trades)
                .Select(c => new ContractDTO
                {
                    ContractId = c.ContractId,
                    ParentContractId = c.ParentContractId,
                    ProcessId = c.ProcessId,
                    TradeId = c.TradeId,
                    Template = c.Template,
                    Status = c.Status,
                    Number = c.Number,
                    WriteDate = c.WriteDate,
                    ApprovalDate = c.ApprovalDate,
                    OrderApprovalDate = c.OrderApprovalDate,
                    CheckQuotes = c.CheckQuotes,
                    CheckWinningQuote = c.CheckWinningQuote,
                    CheckComparison = c.CheckComparison,
                    CheckCheckList = c.CheckCheckList,
                    CheckPrelettingMinutes = c.CheckPrelettingMinutes,
                    SubcontractNumber = c.SubcontractNumber,
                    SiteInstruction = c.SiteInstruction,
                    SiteInstructionDate = c.SiteInstructionDate,
                    SubcontractorReference = c.SubcontractorReference,
                    SubcontractorReferenceDate = c.SubcontractorReferenceDate,
                    GoodsServicesTax = c.GoodsServicesTax,
                    Description = c.Description,
                    Comments = c.Comments,
                    QuotesFile = c.QuotesFile,
                    CheckAmendments = c.CheckAmendments,
                    ParentContractNumber = c.ParentContract != null ? c.ParentContract.Number : null,
                    TradeName = c.Trade != null ? c.Trade.Name : null,
                    ChildContractsCount = c.InverseParentContract.Count,
                    RelatedTradesCount = c.Trades.Count
                })
                .ToListAsync();
        }

        public async Task<ContractDTO> GetContractByIdAsync(int id)
        {
            var contract = await _context.Contracts
                .Include(c => c.ParentContract)
                .Include(c => c.Trade)
                .Include(c => c.InverseParentContract)
                .Include(c => c.Trades)
                .FirstOrDefaultAsync(c => c.ContractId == id);
            
            if (contract == null)
                throw new KeyNotFoundException($"Contract with ID {id} not found");
                
            return new ContractDTO
            {
                ContractId = contract.ContractId,
                ParentContractId = contract.ParentContractId,
                ProcessId = contract.ProcessId,
                TradeId = contract.TradeId,
                Template = contract.Template,
                Status = contract.Status,
                Number = contract.Number,
                WriteDate = contract.WriteDate,
                ApprovalDate = contract.ApprovalDate,
                OrderApprovalDate = contract.OrderApprovalDate,
                CheckQuotes = contract.CheckQuotes,
                CheckWinningQuote = contract.CheckWinningQuote,
                CheckComparison = contract.CheckComparison,
                CheckCheckList = contract.CheckCheckList,
                CheckPrelettingMinutes = contract.CheckPrelettingMinutes,
                SubcontractNumber = contract.SubcontractNumber,
                SiteInstruction = contract.SiteInstruction,
                SiteInstructionDate = contract.SiteInstructionDate,
                SubcontractorReference = contract.SubcontractorReference,
                SubcontractorReferenceDate = contract.SubcontractorReferenceDate,
                GoodsServicesTax = contract.GoodsServicesTax,
                Description = contract.Description,
                Comments = contract.Comments,
                QuotesFile = contract.QuotesFile,
                CheckAmendments = contract.CheckAmendments,
                ParentContractNumber = contract.ParentContract != null ? contract.ParentContract.Number : null,
                TradeName = contract.Trade != null ? contract.Trade.Name : null,
                ChildContractsCount = contract.InverseParentContract.Count,
                RelatedTradesCount = contract.Trades.Count
            };
        }

        public async Task<IEnumerable<ContractDTO>> GetContractsByTradeIdAsync(int tradeId)
        {
            // Check if the trade exists
            var trade = await _context.Trades.FindAsync(tradeId);
            if (trade == null)
                throw new KeyNotFoundException($"Trade with ID {tradeId} not found");
                
            return await _context.Contracts
                .Include(c => c.ParentContract)
                .Include(c => c.Trade)
                .Include(c => c.InverseParentContract)
                .Include(c => c.Trades)
                .Where(c => c.TradeId == tradeId)
                .Select(c => new ContractDTO
                {
                    ContractId = c.ContractId,
                    ParentContractId = c.ParentContractId,
                    ProcessId = c.ProcessId,
                    TradeId = c.TradeId,
                    Template = c.Template,
                    Status = c.Status,
                    Number = c.Number,
                    WriteDate = c.WriteDate,
                    ApprovalDate = c.ApprovalDate,
                    OrderApprovalDate = c.OrderApprovalDate,
                    CheckQuotes = c.CheckQuotes,
                    CheckWinningQuote = c.CheckWinningQuote,
                    CheckComparison = c.CheckComparison,
                    CheckCheckList = c.CheckCheckList,
                    CheckPrelettingMinutes = c.CheckPrelettingMinutes,
                    SubcontractNumber = c.SubcontractNumber,
                    SiteInstruction = c.SiteInstruction,
                    SiteInstructionDate = c.SiteInstructionDate,
                    SubcontractorReference = c.SubcontractorReference,
                    SubcontractorReferenceDate = c.SubcontractorReferenceDate,
                    GoodsServicesTax = c.GoodsServicesTax,
                    Description = c.Description,
                    Comments = c.Comments,
                    QuotesFile = c.QuotesFile,
                    CheckAmendments = c.CheckAmendments,
                    ParentContractNumber = c.ParentContract != null ? c.ParentContract.Number : null,
                    TradeName = c.Trade != null ? c.Trade.Name : null,
                    ChildContractsCount = c.InverseParentContract.Count,
                    RelatedTradesCount = c.Trades.Count
                })
                .ToListAsync();
        }

        public async Task<IEnumerable<ContractDTO>> GetChildContractsAsync(int parentId)
        {
            // Check if the parent contract exists
            var parentContract = await _context.Contracts.FindAsync(parentId);
            if (parentContract == null)
                throw new KeyNotFoundException($"Parent Contract with ID {parentId} not found");
                
            return await _context.Contracts
                .Include(c => c.ParentContract)
                .Include(c => c.Trade)
                .Include(c => c.InverseParentContract)
                .Include(c => c.Trades)
                .Where(c => c.ParentContractId == parentId)
                .Select(c => new ContractDTO
                {
                    ContractId = c.ContractId,
                    ParentContractId = c.ParentContractId,
                    ProcessId = c.ProcessId,
                    TradeId = c.TradeId,
                    Template = c.Template,
                    Status = c.Status,
                    Number = c.Number,
                    WriteDate = c.WriteDate,
                    ApprovalDate = c.ApprovalDate,
                    OrderApprovalDate = c.OrderApprovalDate,
                    CheckQuotes = c.CheckQuotes,
                    CheckWinningQuote = c.CheckWinningQuote,
                    CheckComparison = c.CheckComparison,
                    CheckCheckList = c.CheckCheckList,
                    CheckPrelettingMinutes = c.CheckPrelettingMinutes,
                    SubcontractNumber = c.SubcontractNumber,
                    SiteInstruction = c.SiteInstruction,
                    SiteInstructionDate = c.SiteInstructionDate,
                    SubcontractorReference = c.SubcontractorReference,
                    SubcontractorReferenceDate = c.SubcontractorReferenceDate,
                    GoodsServicesTax = c.GoodsServicesTax,
                    Description = c.Description,
                    Comments = c.Comments,
                    QuotesFile = c.QuotesFile,
                    CheckAmendments = c.CheckAmendments,
                    ParentContractNumber = c.ParentContract != null ? c.ParentContract.Number : null,
                    TradeName = c.Trade != null ? c.Trade.Name : null,
                    ChildContractsCount = c.InverseParentContract.Count,
                    RelatedTradesCount = c.Trades.Count
                })
                .ToListAsync();
        }

        public async Task<ContractDTO> CreateContractAsync(ContractCreateDTO contractDto)
        {
            // Check if the parent contract exists (if specified)
            if (contractDto.ParentContractId.HasValue)
            {
                var parentContract = await _context.Contracts.FindAsync(contractDto.ParentContractId.Value);
                if (parentContract == null)
                    throw new KeyNotFoundException($"Parent Contract with ID {contractDto.ParentContractId.Value} not found");
            }
            
            // Check if the trade exists (if specified)
            if (contractDto.TradeId.HasValue)
            {
                var trade = await _context.Trades.FindAsync(contractDto.TradeId.Value);
                if (trade == null)
                    throw new KeyNotFoundException($"Trade with ID {contractDto.TradeId.Value} not found");
            }
                
            var contract = new Contract
            {
                ParentContractId = contractDto.ParentContractId,
                ProcessId = contractDto.ProcessId,
                TradeId = contractDto.TradeId,
                Template = contractDto.Template,
                Status = contractDto.Status,
                Number = contractDto.Number,
                WriteDate = contractDto.WriteDate,
                ApprovalDate = contractDto.ApprovalDate,
                OrderApprovalDate = contractDto.OrderApprovalDate,
                CheckQuotes = contractDto.CheckQuotes,
                CheckWinningQuote = contractDto.CheckWinningQuote,
                CheckComparison = contractDto.CheckComparison,
                CheckCheckList = contractDto.CheckCheckList,
                CheckPrelettingMinutes = contractDto.CheckPrelettingMinutes,
                SubcontractNumber = contractDto.SubcontractNumber,
                SiteInstruction = contractDto.SiteInstruction,
                SiteInstructionDate = contractDto.SiteInstructionDate,
                SubcontractorReference = contractDto.SubcontractorReference,
                SubcontractorReferenceDate = contractDto.SubcontractorReferenceDate,
                GoodsServicesTax = contractDto.GoodsServicesTax,
                Description = contractDto.Description,
                Comments = contractDto.Comments,
                QuotesFile = contractDto.QuotesFile,
                CheckAmendments = contractDto.CheckAmendments,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow,
                CreatedPeopleId = _userContextService.GetCurrentUserId(),
                ModifiedPeopleId = _userContextService.GetCurrentUserId()
            };
            
            _context.Contracts.Add(contract);
            await _context.SaveChangesAsync();
            
            return await GetContractByIdAsync(contract.ContractId);
        }

        public async Task<ContractDTO> UpdateContractAsync(int id, ContractUpdateDTO contractDto)
        {
            var contract = await _context.Contracts.FindAsync(id);
            
            if (contract == null)
                throw new KeyNotFoundException($"Contract with ID {id} not found");
                
            // Check if the parent contract exists (if specified)
            if (contractDto.ParentContractId.HasValue)
            {
                // Prevent circular reference (a contract cannot be its own parent)
                if (contractDto.ParentContractId.Value == id)
                    throw new InvalidOperationException("A contract cannot be its own parent");
                    
                var parentContract = await _context.Contracts.FindAsync(contractDto.ParentContractId.Value);
                if (parentContract == null)
                    throw new KeyNotFoundException($"Parent Contract with ID {contractDto.ParentContractId.Value} not found");
            }
            
            // Check if the trade exists (if specified)
            if (contractDto.TradeId.HasValue)
            {
                var trade = await _context.Trades.FindAsync(contractDto.TradeId.Value);
                if (trade == null)
                    throw new KeyNotFoundException($"Trade with ID {contractDto.TradeId.Value} not found");
            }
                
            contract.ParentContractId = contractDto.ParentContractId;
            contract.ProcessId = contractDto.ProcessId;
            contract.TradeId = contractDto.TradeId;
            contract.Template = contractDto.Template;
            contract.Status = contractDto.Status;
            contract.Number = contractDto.Number;
            contract.WriteDate = contractDto.WriteDate;
            contract.ApprovalDate = contractDto.ApprovalDate;
            contract.OrderApprovalDate = contractDto.OrderApprovalDate;
            contract.CheckQuotes = contractDto.CheckQuotes;
            contract.CheckWinningQuote = contractDto.CheckWinningQuote;
            contract.CheckComparison = contractDto.CheckComparison;
            contract.CheckCheckList = contractDto.CheckCheckList;
            contract.CheckPrelettingMinutes = contractDto.CheckPrelettingMinutes;
            contract.SubcontractNumber = contractDto.SubcontractNumber;
            contract.SiteInstruction = contractDto.SiteInstruction;
            contract.SiteInstructionDate = contractDto.SiteInstructionDate;
            contract.SubcontractorReference = contractDto.SubcontractorReference;
            contract.SubcontractorReferenceDate = contractDto.SubcontractorReferenceDate;
            contract.GoodsServicesTax = contractDto.GoodsServicesTax;
            contract.Description = contractDto.Description;
            contract.Comments = contractDto.Comments;
            contract.QuotesFile = contractDto.QuotesFile;
            contract.CheckAmendments = contractDto.CheckAmendments;
            contract.ModifiedDate = DateTime.UtcNow;
            contract.ModifiedPeopleId = _userContextService.GetCurrentUserId();
            
            await _context.SaveChangesAsync();
            
            return await GetContractByIdAsync(id);
        }

        public async Task<bool> DeleteContractAsync(int id)
        {
            var contract = await _context.Contracts
                .Include(c => c.InverseParentContract)
                .Include(c => c.Trades)
                .FirstOrDefaultAsync(c => c.ContractId == id);
            
            if (contract == null)
                return false;
                
            // Check if there are any child contracts
            if (contract.InverseParentContract.Any())
            {
                throw new InvalidOperationException("Cannot delete contract that has child contracts");
            }
            
            // Check if there are any related trades
            if (contract.Trades.Any())
            {
                throw new InvalidOperationException("Cannot delete contract that has related trades");
            }
            
            _context.Contracts.Remove(contract);
            await _context.SaveChangesAsync();
            
            return true;
        }
    }
}
