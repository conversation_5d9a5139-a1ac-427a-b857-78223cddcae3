import React from 'react';
import styled from 'styled-components';
import ColorPalette from '../../styles/ColorPalette';
import Typography from '../../styles/Typography';

const InputContainer = styled.div`
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
  width: 100%;
`;

const InputLabel = styled.label`
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
`;

const StyledInput = styled.input`
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  font-weight: 400;
  padding: 12px 16px;
  border: 1px solid #E5E7EB;
  border-radius: 8px;
  background-color: #FFFFFF;
  color: #374151;
  transition: all 0.2s ease-in-out;
  width: 100%;

  &:focus {
    outline: none;
    border-color: #10B981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
  }

  &:disabled {
    background-color: #F9FAFB;
    cursor: not-allowed;
  }

  &::placeholder {
    color: #9CA3AF;
    font-size: 14px;
  }
`;

const ErrorMessage = styled.span`
  font-size: ${Typography.fontSize.sm};
  color: ${ColorPalette.status.error};
  margin-top: 0.25rem;
`;

const Input = ({
  label,
  id,
  error,
  ...props
}) => {
  return (
    <InputContainer>
      {label && <InputLabel htmlFor={id}>{label}</InputLabel>}
      <StyledInput
        id={id}
        error={error}
        {...props}
      />
      {error && <ErrorMessage>{error}</ErrorMessage>}
    </InputContainer>
  );
};

export default Input;
