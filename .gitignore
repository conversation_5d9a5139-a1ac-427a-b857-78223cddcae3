# Visual Studio / .NET specific
.vs/
bin/
obj/
*.user
*.userosscache
*.suo
*.userprefs
*.dbmdl
*.jfm
*.pfx
*.publishsettings
orleans.codegen.cs
*.cache
*.dll
*.pdb
*.exe
*.log
*.vspscc
*.vssscc
.builds
*.pidb
*.svclog
*.scc
*.dtbcache.v2
*.json.user
*.cache.json
*.endpoints.json
*.upToDateCheck.txt
*.removed.txt
*.staticwebassets.*
**/EndpointInfo/
**/CopilotIndices/

# React / Node.js specific
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log

.env.local
.env.development.local
.env.test.local
.env.production.local
coverage/
build/
dist/
.DS_Store

# IDE specific files
.idea/
*.swp
*.swo
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Generated by Windows
Thumbs.db

# Large media files
*.mp4
*.tiff
*.avi
*.flv
*.mov
*.wmv

# Exceptions - keep these files
!**/wwwroot/lib/**
!**/wwwroot/css/**
!**/wwwroot/js/**
!**/wwwroot/images/**
!**/wwwroot/fonts/**
!**/wwwroot/favicon.ico
