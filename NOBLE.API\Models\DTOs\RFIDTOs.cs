using System.ComponentModel.DataAnnotations;

namespace NOBLE.API.Models.DTOs
{
    public class RFIDTO
    {
        public int RFIId { get; set; }
        public int ProjectId { get; set; }
        public int? Number { get; set; }
        public string? Subject { get; set; }
        public string? Description { get; set; }
        public string? Status { get; set; }
        public DateTime? RaiseDate { get; set; }
        public DateTime? TargetAnswerDate { get; set; }
        public DateTime? ActualAnswerDate { get; set; }
        public string? ReferenceFile { get; set; }
        public string? ClientResponseFile { get; set; }
        public string? ClientResponseSummary { get; set; }
        
        // Navigation properties for display purposes
        public string? ProjectName { get; set; }
        public int ResponseCount { get; set; }
    }

    public class RFICreateDTO
    {
        [Required]
        public int ProjectId { get; set; }
        
        public int? Number { get; set; }
        
        [StringLength(100)]
        public string? Subject { get; set; }
        
        public string? Description { get; set; }
        
        [StringLength(50)]
        public string? Status { get; set; }
        
        public DateTime? RaiseDate { get; set; }
        public DateTime? TargetAnswerDate { get; set; }
        public DateTime? ActualAnswerDate { get; set; }
        
        [StringLength(255)]
        public string? ReferenceFile { get; set; }
        
        [StringLength(255)]
        public string? ClientResponseFile { get; set; }
        
        public string? ClientResponseSummary { get; set; }
    }

    public class RFIUpdateDTO
    {
        [Required]
        public int ProjectId { get; set; }
        
        public int? Number { get; set; }
        
        [StringLength(100)]
        public string? Subject { get; set; }
        
        public string? Description { get; set; }
        
        [StringLength(50)]
        public string? Status { get; set; }
        
        public DateTime? RaiseDate { get; set; }
        public DateTime? TargetAnswerDate { get; set; }
        public DateTime? ActualAnswerDate { get; set; }
        
        [StringLength(255)]
        public string? ReferenceFile { get; set; }
        
        [StringLength(255)]
        public string? ClientResponseFile { get; set; }
        
        public string? ClientResponseSummary { get; set; }
    }

    public class RFIResponseDTO
    {
        public int ResponseId { get; set; }
        public int RFIId { get; set; }
        public int? RFINumber { get; set; }
        public int? ResponseNumber { get; set; }
        public string? ResponseFrom { get; set; }
        public string? ResponseMessage { get; set; }
        public DateTime? ResponseDate { get; set; }
        public string? ResponseFolderPath { get; set; }
        public DateTime? CreatedDate { get; set; }
        public string? CreatedBy { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string? ModifiedBy { get; set; }
    }

    public class RFIResponseCreateDTO
    {
        [Required]
        public int RFIId { get; set; }
        
        public int? RFINumber { get; set; }
        public int? ResponseNumber { get; set; }
        
        [StringLength(100)]
        public string? ResponseFrom { get; set; }
        
        public string? ResponseMessage { get; set; }
        
        public DateTime? ResponseDate { get; set; }
        
        [StringLength(255)]
        public string? ResponseFolderPath { get; set; }
    }

    public class RFIResponseUpdateDTO
    {
        public int? RFINumber { get; set; }
        public int? ResponseNumber { get; set; }
        
        [StringLength(100)]
        public string? ResponseFrom { get; set; }
        
        public string? ResponseMessage { get; set; }
        
        public DateTime? ResponseDate { get; set; }
        
        [StringLength(255)]
        public string? ResponseFolderPath { get; set; }
    }
}
