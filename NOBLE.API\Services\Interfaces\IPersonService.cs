using NOBLE.API.Models.DTOs;

namespace NOBLE.API.Services.Interfaces
{
    public interface IPersonService
    {
        Task<IEnumerable<PersonDTO>> GetAllPeopleAsync();
        Task<PersonDTO> GetPersonByIdAsync(int id);
        Task<IEnumerable<PersonDTO>> GetPeopleByBusinessUnitIdAsync(int businessUnitId);
        Task<IEnumerable<PersonDTO>> GetPeopleBySubContractorIdAsync(int subContractorId);
        Task<IEnumerable<PersonDTO>> GetPeopleByTypeAsync(string type);
        Task<PersonDTO> CreatePersonAsync(PersonCreateDTO personDto);
        Task<PersonDTO> UpdatePersonAsync(int id, PersonUpdateDTO personDto);
        Task<bool> DeletePersonAsync(int id);
    }
}
