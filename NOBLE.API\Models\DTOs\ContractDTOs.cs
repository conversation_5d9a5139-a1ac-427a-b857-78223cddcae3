using System.ComponentModel.DataAnnotations;

namespace NOBLE.API.Models.DTOs
{
    public class ContractDTO
    {
        public int ContractId { get; set; }
        public int? ParentContractId { get; set; }
        public int? ProcessId { get; set; }
        public int? TradeId { get; set; }
        public string? Template { get; set; }
        public int? Status { get; set; }
        public string? Number { get; set; }
        public DateTime? WriteDate { get; set; }
        public DateTime? ApprovalDate { get; set; }
        public int? OrderApprovalDate { get; set; }
        public bool? CheckQuotes { get; set; }
        public bool? CheckWinningQuote { get; set; }
        public bool? CheckComparison { get; set; }
        public bool? CheckCheckList { get; set; }
        public int? CheckPrelettingMinutes { get; set; }
        public int? SubcontractNumber { get; set; }
        public string? SiteInstruction { get; set; }
        public DateTime? SiteInstructionDate { get; set; }
        public string? SubcontractorReference { get; set; }
        public DateTime? SubcontractorReferenceDate { get; set; }
        public decimal? GoodsServicesTax { get; set; }
        public string? Description { get; set; }
        public string? Comments { get; set; }
        public string? QuotesFile { get; set; }
        public int? CheckAmendments { get; set; }
        
        // Navigation properties for display purposes
        public string? ParentContractNumber { get; set; }
        public string? TradeName { get; set; }
        public int ChildContractsCount { get; set; }
        public int RelatedTradesCount { get; set; }
    }

    public class ContractCreateDTO
    {
        public int? ParentContractId { get; set; }
        public int? ProcessId { get; set; }
        public int? TradeId { get; set; }
        
        [StringLength(255)]
        public string? Template { get; set; }
        
        public int? Status { get; set; }
        
        [StringLength(50)]
        public string? Number { get; set; }
        
        public DateTime? WriteDate { get; set; }
        public DateTime? ApprovalDate { get; set; }
        public int? OrderApprovalDate { get; set; }
        public bool? CheckQuotes { get; set; }
        public bool? CheckWinningQuote { get; set; }
        public bool? CheckComparison { get; set; }
        public bool? CheckCheckList { get; set; }
        public int? CheckPrelettingMinutes { get; set; }
        public int? SubcontractNumber { get; set; }
        
        [StringLength(255)]
        public string? SiteInstruction { get; set; }
        
        public DateTime? SiteInstructionDate { get; set; }
        
        [StringLength(255)]
        public string? SubcontractorReference { get; set; }
        
        public DateTime? SubcontractorReferenceDate { get; set; }
        public decimal? GoodsServicesTax { get; set; }
        
        public string? Description { get; set; }
        public string? Comments { get; set; }
        
        [StringLength(255)]
        public string? QuotesFile { get; set; }
        
        public int? CheckAmendments { get; set; }
    }

    public class ContractUpdateDTO
    {
        public int? ParentContractId { get; set; }
        public int? ProcessId { get; set; }
        public int? TradeId { get; set; }
        
        [StringLength(255)]
        public string? Template { get; set; }
        
        public int? Status { get; set; }
        
        [StringLength(50)]
        public string? Number { get; set; }
        
        public DateTime? WriteDate { get; set; }
        public DateTime? ApprovalDate { get; set; }
        public int? OrderApprovalDate { get; set; }
        public bool? CheckQuotes { get; set; }
        public bool? CheckWinningQuote { get; set; }
        public bool? CheckComparison { get; set; }
        public bool? CheckCheckList { get; set; }
        public int? CheckPrelettingMinutes { get; set; }
        public int? SubcontractNumber { get; set; }
        
        [StringLength(255)]
        public string? SiteInstruction { get; set; }
        
        public DateTime? SiteInstructionDate { get; set; }
        
        [StringLength(255)]
        public string? SubcontractorReference { get; set; }
        
        public DateTime? SubcontractorReferenceDate { get; set; }
        public decimal? GoodsServicesTax { get; set; }
        
        public string? Description { get; set; }
        public string? Comments { get; set; }
        
        [StringLength(255)]
        public string? QuotesFile { get; set; }
        
        public int? CheckAmendments { get; set; }
    }
}
