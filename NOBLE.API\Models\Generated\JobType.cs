﻿using System;
using System.Collections.Generic;

namespace NOBLE.API.Models.Generated;

public partial class JobType
{
    public int JobTypeId { get; set; }

    public string? Name { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public int CreatedPeopleId { get; set; }

    public int ModifiedPeopleId { get; set; }

    public virtual ICollection<Trade> Trades { get; set; } = new List<Trade>();
}
