using Microsoft.EntityFrameworkCore;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Models.Generated;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Services.Implementations
{
    public class BudgetService : IBudgetService
    {
        private readonly NobleDbContext _context;
        private readonly IUserContextService _userContextService;

        public BudgetService(NobleDbContext context, IUserContextService userContextService)
        {
            _context = context;
            _userContextService = userContextService;
        }

        public async Task<IEnumerable<BudgetDTO>> GetAllBudgetsAsync()
        {
            return await _context.Budgets
                .Include(b => b.Project)
                .Select(b => new BudgetDTO
                {
                    BudgetId = b.BudgetId,
                    ProjectId = b.ProjectId,
                    Code = b.Code,
                    Amount = b.Amount,
                    ProjectName = b.Project.Name
                })
                .ToListAsync();
        }

        public async Task<BudgetDTO> GetBudgetByIdAsync(int id)
        {
            var budget = await _context.Budgets
                .Include(b => b.Project)
                .FirstOrDefaultAsync(b => b.BudgetId == id);
            
            if (budget == null)
                throw new KeyNotFoundException($"Budget with ID {id} not found");
                
            return new BudgetDTO
            {
                BudgetId = budget.BudgetId,
                ProjectId = budget.ProjectId,
                Code = budget.Code,
                Amount = budget.Amount,
                ProjectName = budget.Project.Name
            };
        }

        public async Task<IEnumerable<BudgetDTO>> GetBudgetsByProjectIdAsync(int projectId)
        {
            // Check if the project exists
            var project = await _context.Projects.FindAsync(projectId);
            if (project == null)
                throw new KeyNotFoundException($"Project with ID {projectId} not found");
                
            return await _context.Budgets
                .Include(b => b.Project)
                .Where(b => b.ProjectId == projectId)
                .Select(b => new BudgetDTO
                {
                    BudgetId = b.BudgetId,
                    ProjectId = b.ProjectId,
                    Code = b.Code,
                    Amount = b.Amount,
                    ProjectName = b.Project.Name
                })
                .ToListAsync();
        }

        public async Task<BudgetDTO> CreateBudgetAsync(BudgetCreateDTO budgetDto)
        {
            // Check if the project exists
            var project = await _context.Projects.FindAsync(budgetDto.ProjectId);
            if (project == null)
                throw new KeyNotFoundException($"Project with ID {budgetDto.ProjectId} not found");
                
            // Check if a budget with the same code already exists for this project
            var existingBudget = await _context.Budgets
                .FirstOrDefaultAsync(b => b.ProjectId == budgetDto.ProjectId && b.Code == budgetDto.Code);
                
            if (existingBudget != null)
                throw new InvalidOperationException($"A budget with code '{budgetDto.Code}' already exists for this project");
                
            var budget = new Budget
            {
                ProjectId = budgetDto.ProjectId,
                Code = budgetDto.Code,
                Amount = budgetDto.Amount,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow,
                CreatedPeopleId = _userContextService.GetCurrentUserId(),
                ModifiedPeopleId = _userContextService.GetCurrentUserId()
            };
            
            _context.Budgets.Add(budget);
            await _context.SaveChangesAsync();
            
            return await GetBudgetByIdAsync(budget.BudgetId);
        }

        public async Task<BudgetDTO> UpdateBudgetAsync(int id, BudgetUpdateDTO budgetDto)
        {
            var budget = await _context.Budgets.FindAsync(id);
            
            if (budget == null)
                throw new KeyNotFoundException($"Budget with ID {id} not found");
                
            
            var project = await _context.Projects.FindAsync(budgetDto.ProjectId);
            if (project == null)
                throw new KeyNotFoundException($"Project with ID {budgetDto.ProjectId} not found");
             
            var existingBudget = await _context.Budgets
                .FirstOrDefaultAsync(b => b.ProjectId == budgetDto.ProjectId && b.Code == budgetDto.Code && b.BudgetId != id);
                
            if (existingBudget != null)
                throw new InvalidOperationException($"A budget with code '{budgetDto.Code}' already exists for this project");
                
            budget.ProjectId = budgetDto.ProjectId;
            budget.Code = budgetDto.Code;
            budget.Amount = budgetDto.Amount;
            budget.ModifiedDate = DateTime.UtcNow;
            budget.ModifiedPeopleId = _userContextService.GetCurrentUserId();
            
            await _context.SaveChangesAsync();
            
            return await GetBudgetByIdAsync(id);
        }

        public async Task<bool> DeleteBudgetAsync(int id)
        {
            var budget = await _context.Budgets.FindAsync(id);
            
            if (budget == null)
                return false;
 
            var hasBudgetTrades = await _context.BudgetsTrades.AnyAsync(bt => bt.BudgetId == id);
            
            if (hasBudgetTrades)
            {
                throw new InvalidOperationException("Cannot delete budget that has associated budget trades");
            }
            
            _context.Budgets.Remove(budget);
            await _context.SaveChangesAsync();
            
            return true;
        }
    }
}
