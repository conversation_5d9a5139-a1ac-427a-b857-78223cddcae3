using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class ProcessesController : ControllerBase
    {
        private readonly IProcessService _processService;
        
        public ProcessesController(IProcessService processService)
        {
            _processService = processService;
        }
        
        // GET: api/Processes
        [HttpGet]
        public async Task<ActionResult<IEnumerable<ProcessDTO>>> GetProcesses()
        {
            var processes = await _processService.GetAllProcessesAsync();
            return Ok(processes);
        }
        
        // GET: api/Processes/5
        [HttpGet("{id}")]
        public async Task<ActionResult<ProcessDTO>> GetProcess(int id)
        {
            try
            {
                var process = await _processService.GetProcessByIdAsync(id);
                return Ok(process);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // GET: api/Processes/TemplateType/Contract
        [HttpGet("TemplateType/{templateType}")]
        public async Task<ActionResult<IEnumerable<ProcessDTO>>> GetProcessesByTemplateType(string templateType)
        {
            var processes = await _processService.GetProcessesByTemplateTypeAsync(templateType);
            return Ok(processes);
        }
        
        // GET: api/Processes/Visible
        [HttpGet("Visible")]
        public async Task<ActionResult<IEnumerable<ProcessDTO>>> GetVisibleProcesses()
        {
            var processes = await _processService.GetVisibleProcessesAsync();
            return Ok(processes);
        }
        
        // POST: api/Processes
        [HttpPost]
        public async Task<ActionResult<ProcessDTO>> CreateProcess(ProcessCreateDTO processDto)
        {
            try
            {
                var createdProcess = await _processService.CreateProcessAsync(processDto);
                return CreatedAtAction(nameof(GetProcess), new { id = createdProcess.ProcessId }, createdProcess);
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // PUT: api/Processes/5
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateProcess(int id, ProcessUpdateDTO processDto)
        {
            try
            {
                var updatedProcess = await _processService.UpdateProcessAsync(id, processDto);
                return Ok(updatedProcess);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // DELETE: api/Processes/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteProcess(int id)
        {
            try
            {
                var result = await _processService.DeleteProcessAsync(id);
                
                if (!result)
                    return NotFound(new { message = $"Process with ID {id} not found" });
                    
                return NoContent();
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
    }
}
