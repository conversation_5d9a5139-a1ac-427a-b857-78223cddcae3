using NOBLE.API.Models.DTOs;

namespace NOBLE.API.Services.Interfaces
{
    public interface IJobTypeService
    {
        Task<IEnumerable<JobTypeDTO>> GetAllJobTypesAsync();
        Task<JobTypeDTO> GetJobTypeByIdAsync(int id);
        Task<JobTypeDTO> CreateJobTypeAsync(JobTypeCreateDTO jobTypeDto);
        Task<JobTypeDTO> UpdateJobTypeAsync(int id, JobTypeUpdateDTO jobTypeDto);
        Task<bool> DeleteJobTypeAsync(int id);
    }
}
