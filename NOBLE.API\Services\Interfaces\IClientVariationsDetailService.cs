using NOBLE.API.Models.DTOs;

namespace NOBLE.API.Services.Interfaces
{
    public interface IClientVariationsDetailService
    {
        Task<IEnumerable<ClientVariationsDetailDTO>> GetAllClientVariationsDetailsAsync();
        Task<ClientVariationsDetailDTO> GetClientVariationsDetailByIdAsync(int id);
        Task<IEnumerable<ClientVariationsDetailDTO>> GetClientVariationsDetailsByClientVariationIdAsync(int clientVariationId);
        Task<ClientVariationsDetailDTO> CreateClientVariationsDetailAsync(ClientVariationsDetailCreateDTO clientVariationsDetailDto);
        Task<ClientVariationsDetailDTO> UpdateClientVariationsDetailAsync(int id, ClientVariationsDetailUpdateDTO clientVariationsDetailDto);
        Task<bool> DeleteClientVariationsDetailAsync(int id);
    }
}
