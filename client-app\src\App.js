import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './context/AuthContext';
import GlobalStyles from './styles/GlobalStyles';
import LoginPage from './pages/Login/LoginPage';
import ForgotPasswordPage from './pages/ForgotPassword/ForgotPasswordPage';
import ResetPasswordPage from './pages/ResetPasswordPage';
import DashboardPage from './pages/Dashboard/DashboardPage';
import ProjectPage from './pages/Project/ProjectPage';
import ProtectedRoute from './components/common/ProtectedRoute';

const UnauthorizedPage = () => <div>Unauthorized Access</div>;

function App() {
  return (
    <AuthProvider>
      <GlobalStyles />
      <Router>
        <Routes>

          <Route path="/login" element={<LoginPage />} />
          <Route path="/forgot-password" element={<ForgotPasswordPage />} />
          <Route path="/reset-password/:token" element={<ResetPasswordPage />} />
          <Route path="/unauthorized" element={<UnauthorizedPage />} />


          <Route element={<ProtectedRoute />}>
            <Route path="/dashboard" element={<DashboardPage />} />
            <Route path="/projects" element={<ProjectPage />} />
          </Route>

          <Route element={<ProtectedRoute requiredRoles={['Admin']} />}>
            <Route path="/admin" element={<div>Admin Dashboard</div>} />
          </Route>

          <Route path="*" element={<Navigate to="/login" replace />} />
        </Routes>
      </Router>
    </AuthProvider>
  );
}

export default App;
