using System.ComponentModel.DataAnnotations;

namespace NOBLE.API.Models.DTOs
{
    public class ProcessDTO
    {
        public int ProcessId { get; set; }
        public string? TemplateType { get; set; }
        public string? Name { get; set; }
        public string? StepComparisonApproval { get; set; }
        public string? StepContractApproval { get; set; }
        public int? DisplayOrder { get; set; }
        public bool? Hide { get; set; }
        
        // Navigation properties for display purposes
        public int ProcessStepsCount { get; set; }
    }

    public class ProcessCreateDTO
    {
        [StringLength(50)]
        public string? TemplateType { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(50)]
        public string? StepComparisonApproval { get; set; }
        
        [StringLength(50)]
        public string? StepContractApproval { get; set; }
        
        public int? DisplayOrder { get; set; }
        
        public bool? Hide { get; set; } = false;
    }

    public class ProcessUpdateDTO
    {
        [StringLength(50)]
        public string? TemplateType { get; set; }
        
        [StringLength(100)]
        public string? Name { get; set; }
        
        [StringLength(50)]
        public string? StepComparisonApproval { get; set; }
        
        [StringLength(50)]
        public string? StepContractApproval { get; set; }
        
        public int? DisplayOrder { get; set; }
        
        public bool? Hide { get; set; }
    }
}
