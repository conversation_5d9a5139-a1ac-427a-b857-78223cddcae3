﻿using System;
using System.Collections.Generic;

namespace NOBLE.API.Models.Generated;

public partial class Trade
{
    public int TradeId { get; set; }

    public int? JobTypeId { get; set; }

    public int? ProjectId { get; set; }

    public int? ProcessId { get; set; }

    public int? ContractId { get; set; }

    public string? Name { get; set; }

    public string? Code { get; set; }

    public bool? TenderRequired { get; set; }

    public string? Description { get; set; }

    public string? ScopeHeader { get; set; }

    public string? ScopeFooter { get; set; }

    public int? DisplayOrder { get; set; }

    public int? DaysFromPcd { get; set; }

    public DateTime? InvitationDate { get; set; }

    public DateTime? DueDate { get; set; }

    public DateTime? ComparisonDueDate { get; set; }

    public DateTime? ContractDueDate { get; set; }

    public DateTime? ComparisonApprovalDate { get; set; }

    public decimal? ComparisonApprovalAmount { get; set; }

    public DateTime? CommencementDate { get; set; }

    public DateTime? CompletionDate { get; set; }

    public string? WorkOrderNumber { get; set; }

    public string? QuotesFile { get; set; }

    public string? PrelettingFile { get; set; }

    public string? SignedContractFile { get; set; }

    public int? CapeopleId { get; set; }

    public int? PmpeopleId { get; set; }

    public int? Flag { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public int CreatedPeopleId { get; set; }

    public int ModifiedPeopleId { get; set; }

    public virtual ICollection<BudgetsTrade> BudgetsTrades { get; set; } = new List<BudgetsTrade>();

    public virtual Person? Capeople { get; set; }

    public virtual Contract? Contract { get; set; }

    public virtual ICollection<Contract> Contracts { get; set; } = new List<Contract>();

    public virtual JobType? JobType { get; set; }

    public virtual Person? Pmpeople { get; set; }

    public virtual Project? Project { get; set; }

    public virtual ICollection<TradeItemCategory> TradeItemCategories { get; set; } = new List<TradeItemCategory>();

    public virtual ICollection<TradeParticipation> TradeParticipations { get; set; } = new List<TradeParticipation>();
}
