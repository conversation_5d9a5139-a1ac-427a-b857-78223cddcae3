using Microsoft.EntityFrameworkCore;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Models.Generated;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Services.Implementations
{
    public class PersonService : IPersonService
    {
        private readonly NobleDbContext _context;
        private readonly IUserContextService _userContextService;

        public PersonService(NobleDbContext context, IUserContextService userContextService)
        {
            _context = context;
            _userContextService = userContextService;
        }

        public async Task<IEnumerable<PersonDTO>> GetAllPeopleAsync()
        {
            return await _context.People
                .Include(p => p.BusinessUnit)
                .Include(p => p.TradeCapeople)
                .Include(p => p.TradeParticipations)
                .Include(p => p.TradePmpeople)
                .Select(p => new PersonDTO
                {
                    PeopleId = p.PeopleId,
                    Type = p.Type,
                    SubContractorId = p.SubContractorId,
                    FirstName = p.FirstName,
                    LastName = p.LastName,
                    Street = p.Street,
                    City = p.City,
                    State = p.State,
                    PostCode = p.PostCode,
                    Phone = p.Phone,
                    Mobile = p.Mobile,
                    Email = p.Email,
                    EmployeePosition = p.EmployeePosition,
                    UserType = p.UserType,
                    UserLogin = p.UserLogin,
                    UserSignatureFile = p.UserSignatureFile,
                    BusinessUnitId = p.BusinessUnitId,
                    Dob = p.Dob,
                    EmergencyContactName = p.EmergencyContactName,
                    EmergencyContactNumber = p.EmergencyContactNumber,
                    UserLastLogin = p.UserLastLogin,
                    Inactive = p.Inactive,
                    BusinessUnitName = p.BusinessUnit != null ? p.BusinessUnit.Name : null,
                    SubContractorName = p.SubContractorId.HasValue ? 
                        _context.SubContractors
                            .Where(sc => sc.SubContractorId == p.SubContractorId.Value)
                            .Select(sc => sc.Name)
                            .FirstOrDefault() : null,
                    TradesCount = p.TradeCapeople.Count + p.TradePmpeople.Count,
                    TradeParticipationsCount = p.TradeParticipations.Count
                })
                .ToListAsync();
        }

        public async Task<PersonDTO> GetPersonByIdAsync(int id)
        {
            var person = await _context.People
                .Include(p => p.BusinessUnit)
                .Include(p => p.TradeCapeople)
                .Include(p => p.TradeParticipations)
                .Include(p => p.TradePmpeople)
                .FirstOrDefaultAsync(p => p.PeopleId == id);
            
            if (person == null)
                throw new KeyNotFoundException($"Person with ID {id} not found");
                
            string? subContractorName = null;
            if (person.SubContractorId.HasValue)
            {
                var subContractor = await _context.SubContractors.FindAsync(person.SubContractorId.Value);
                subContractorName = subContractor?.Name;
            }
                
            return new PersonDTO
            {
                PeopleId = person.PeopleId,
                Type = person.Type,
                SubContractorId = person.SubContractorId,
                FirstName = person.FirstName,
                LastName = person.LastName,
                Street = person.Street,
                City = person.City,
                State = person.State,
                PostCode = person.PostCode,
                Phone = person.Phone,
                Mobile = person.Mobile,
                Email = person.Email,
                EmployeePosition = person.EmployeePosition,
                UserType = person.UserType,
                UserLogin = person.UserLogin,
                UserSignatureFile = person.UserSignatureFile,
                BusinessUnitId = person.BusinessUnitId,
                Dob = person.Dob,
                EmergencyContactName = person.EmergencyContactName,
                EmergencyContactNumber = person.EmergencyContactNumber,
                UserLastLogin = person.UserLastLogin,
                Inactive = person.Inactive,
                BusinessUnitName = person.BusinessUnit?.Name,
                SubContractorName = subContractorName,
                TradesCount = person.TradeCapeople.Count + person.TradePmpeople.Count,
                TradeParticipationsCount = person.TradeParticipations.Count
            };
        }

        public async Task<IEnumerable<PersonDTO>> GetPeopleByBusinessUnitIdAsync(int businessUnitId)
        {
            // Check if the business unit exists
            var businessUnit = await _context.BusinessUnits.FindAsync(businessUnitId);
            if (businessUnit == null)
                throw new KeyNotFoundException($"BusinessUnit with ID {businessUnitId} not found");
                
            return await _context.People
                .Include(p => p.BusinessUnit)
                .Include(p => p.TradeCapeople)
                .Include(p => p.TradeParticipations)
                .Include(p => p.TradePmpeople)
                .Where(p => p.BusinessUnitId == businessUnitId)
                .Select(p => new PersonDTO
                {
                    PeopleId = p.PeopleId,
                    Type = p.Type,
                    SubContractorId = p.SubContractorId,
                    FirstName = p.FirstName,
                    LastName = p.LastName,
                    Street = p.Street,
                    City = p.City,
                    State = p.State,
                    PostCode = p.PostCode,
                    Phone = p.Phone,
                    Mobile = p.Mobile,
                    Email = p.Email,
                    EmployeePosition = p.EmployeePosition,
                    UserType = p.UserType,
                    UserLogin = p.UserLogin,
                    UserSignatureFile = p.UserSignatureFile,
                    BusinessUnitId = p.BusinessUnitId,
                    Dob = p.Dob,
                    EmergencyContactName = p.EmergencyContactName,
                    EmergencyContactNumber = p.EmergencyContactNumber,
                    UserLastLogin = p.UserLastLogin,
                    Inactive = p.Inactive,
                    BusinessUnitName = businessUnit.Name,
                    SubContractorName = p.SubContractorId.HasValue ? 
                        _context.SubContractors
                            .Where(sc => sc.SubContractorId == p.SubContractorId.Value)
                            .Select(sc => sc.Name)
                            .FirstOrDefault() : null,
                    TradesCount = p.TradeCapeople.Count + p.TradePmpeople.Count,
                    TradeParticipationsCount = p.TradeParticipations.Count
                })
                .ToListAsync();
        }

        public async Task<IEnumerable<PersonDTO>> GetPeopleBySubContractorIdAsync(int subContractorId)
        {
            // Check if the subcontractor exists
            var subContractor = await _context.SubContractors.FindAsync(subContractorId);
            if (subContractor == null)
                throw new KeyNotFoundException($"SubContractor with ID {subContractorId} not found");
                
            return await _context.People
                .Include(p => p.BusinessUnit)
                .Include(p => p.TradeCapeople)
                .Include(p => p.TradeParticipations)
                .Include(p => p.TradePmpeople)
                .Where(p => p.SubContractorId == subContractorId)
                .Select(p => new PersonDTO
                {
                    PeopleId = p.PeopleId,
                    Type = p.Type,
                    SubContractorId = p.SubContractorId,
                    FirstName = p.FirstName,
                    LastName = p.LastName,
                    Street = p.Street,
                    City = p.City,
                    State = p.State,
                    PostCode = p.PostCode,
                    Phone = p.Phone,
                    Mobile = p.Mobile,
                    Email = p.Email,
                    EmployeePosition = p.EmployeePosition,
                    UserType = p.UserType,
                    UserLogin = p.UserLogin,
                    UserSignatureFile = p.UserSignatureFile,
                    BusinessUnitId = p.BusinessUnitId,
                    Dob = p.Dob,
                    EmergencyContactName = p.EmergencyContactName,
                    EmergencyContactNumber = p.EmergencyContactNumber,
                    UserLastLogin = p.UserLastLogin,
                    Inactive = p.Inactive,
                    BusinessUnitName = p.BusinessUnit != null ? p.BusinessUnit.Name : null,
                    SubContractorName = subContractor.Name,
                    TradesCount = p.TradeCapeople.Count + p.TradePmpeople.Count,
                    TradeParticipationsCount = p.TradeParticipations.Count
                })
                .ToListAsync();
        }

        public async Task<IEnumerable<PersonDTO>> GetPeopleByTypeAsync(string type)
        {
            return await _context.People
                .Include(p => p.BusinessUnit)
                .Include(p => p.TradeCapeople)
                .Include(p => p.TradeParticipations)
                .Include(p => p.TradePmpeople)
                .Where(p => p.Type == type)
                .Select(p => new PersonDTO
                {
                    PeopleId = p.PeopleId,
                    Type = p.Type,
                    SubContractorId = p.SubContractorId,
                    FirstName = p.FirstName,
                    LastName = p.LastName,
                    Street = p.Street,
                    City = p.City,
                    State = p.State,
                    PostCode = p.PostCode,
                    Phone = p.Phone,
                    Mobile = p.Mobile,
                    Email = p.Email,
                    EmployeePosition = p.EmployeePosition,
                    UserType = p.UserType,
                    UserLogin = p.UserLogin,
                    UserSignatureFile = p.UserSignatureFile,
                    BusinessUnitId = p.BusinessUnitId,
                    Dob = p.Dob,
                    EmergencyContactName = p.EmergencyContactName,
                    EmergencyContactNumber = p.EmergencyContactNumber,
                    UserLastLogin = p.UserLastLogin,
                    Inactive = p.Inactive,
                    BusinessUnitName = p.BusinessUnit != null ? p.BusinessUnit.Name : null,
                    SubContractorName = p.SubContractorId.HasValue ? 
                        _context.SubContractors
                            .Where(sc => sc.SubContractorId == p.SubContractorId.Value)
                            .Select(sc => sc.Name)
                            .FirstOrDefault() : null,
                    TradesCount = p.TradeCapeople.Count + p.TradePmpeople.Count,
                    TradeParticipationsCount = p.TradeParticipations.Count
                })
                .ToListAsync();
        }

        public async Task<PersonDTO> CreatePersonAsync(PersonCreateDTO personDto)
        {
            // Check if the business unit exists (if provided)
            if (personDto.BusinessUnitId.HasValue)
            {
                var businessUnit = await _context.BusinessUnits.FindAsync(personDto.BusinessUnitId.Value);
                if (businessUnit == null)
                    throw new KeyNotFoundException($"BusinessUnit with ID {personDto.BusinessUnitId.Value} not found");
            }
            
            // Check if the subcontractor exists (if provided)
            if (personDto.SubContractorId.HasValue)
            {
                var subContractor = await _context.SubContractors.FindAsync(personDto.SubContractorId.Value);
                if (subContractor == null)
                    throw new KeyNotFoundException($"SubContractor with ID {personDto.SubContractorId.Value} not found");
            }
            
            // Check if the user login is unique (if provided)
            if (!string.IsNullOrEmpty(personDto.UserLogin))
            {
                var existingUser = await _context.People.FirstOrDefaultAsync(p => p.UserLogin == personDto.UserLogin);
                if (existingUser != null)
                    throw new InvalidOperationException($"User login '{personDto.UserLogin}' is already in use");
            }
                
            var person = new Person
            {
                Type = personDto.Type,
                SubContractorId = personDto.SubContractorId,
                FirstName = personDto.FirstName,
                LastName = personDto.LastName,
                Street = personDto.Street,
                City = personDto.City,
                State = personDto.State,
                PostCode = personDto.PostCode,
                Phone = personDto.Phone,
                Mobile = personDto.Mobile,
                Email = personDto.Email,
                EmployeePosition = personDto.EmployeePosition,
                UserType = personDto.UserType,
                UserLogin = personDto.UserLogin,
                UserPassword = null, // Password should be set separately through a secure process
                UserSignatureFile = personDto.UserSignatureFile,
                BusinessUnitId = personDto.BusinessUnitId,
                Dob = personDto.Dob,
                EmergencyContactName = personDto.EmergencyContactName,
                EmergencyContactNumber = personDto.EmergencyContactNumber,
                UserLastLogin = null,
                Inactive = personDto.Inactive,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow,
                CreatedPeopleId = _userContextService.GetCurrentUserId(),
                ModifiedPeopleId = _userContextService.GetCurrentUserId()
            };
            
            _context.People.Add(person);
            await _context.SaveChangesAsync();
            
            return await GetPersonByIdAsync(person.PeopleId);
        }

        public async Task<PersonDTO> UpdatePersonAsync(int id, PersonUpdateDTO personDto)
        {
            var person = await _context.People.FindAsync(id);
            
            if (person == null)
                throw new KeyNotFoundException($"Person with ID {id} not found");
                
            // Check if the business unit exists (if provided)
            if (personDto.BusinessUnitId.HasValue)
            {
                var businessUnit = await _context.BusinessUnits.FindAsync(personDto.BusinessUnitId.Value);
                if (businessUnit == null)
                    throw new KeyNotFoundException($"BusinessUnit with ID {personDto.BusinessUnitId.Value} not found");
                    
                person.BusinessUnitId = personDto.BusinessUnitId;
            }
            
            // Check if the subcontractor exists (if provided)
            if (personDto.SubContractorId.HasValue)
            {
                var subContractor = await _context.SubContractors.FindAsync(personDto.SubContractorId.Value);
                if (subContractor == null)
                    throw new KeyNotFoundException($"SubContractor with ID {personDto.SubContractorId.Value} not found");
                    
                person.SubContractorId = personDto.SubContractorId;
            }
            
            // Check if the user login is unique (if provided and changed)
            if (!string.IsNullOrEmpty(personDto.UserLogin) && personDto.UserLogin != person.UserLogin)
            {
                var existingUser = await _context.People.FirstOrDefaultAsync(p => p.UserLogin == personDto.UserLogin);
                if (existingUser != null)
                    throw new InvalidOperationException($"User login '{personDto.UserLogin}' is already in use");
                    
                person.UserLogin = personDto.UserLogin;
            }
                
            if (personDto.Type != null)
                person.Type = personDto.Type;
                
            if (personDto.FirstName != null)
                person.FirstName = personDto.FirstName;
                
            if (personDto.LastName != null)
                person.LastName = personDto.LastName;
                
            if (personDto.Street != null)
                person.Street = personDto.Street;
                
            if (personDto.City != null)
                person.City = personDto.City;
                
            if (personDto.State != null)
                person.State = personDto.State;
                
            if (personDto.PostCode != null)
                person.PostCode = personDto.PostCode;
                
            if (personDto.Phone != null)
                person.Phone = personDto.Phone;
                
            if (personDto.Mobile != null)
                person.Mobile = personDto.Mobile;
                
            if (personDto.Email != null)
                person.Email = personDto.Email;
                
            if (personDto.EmployeePosition != null)
                person.EmployeePosition = personDto.EmployeePosition;
                
            if (personDto.UserType != null)
                person.UserType = personDto.UserType;
                
            if (personDto.UserSignatureFile != null)
                person.UserSignatureFile = personDto.UserSignatureFile;
                
            if (personDto.Dob.HasValue)
                person.Dob = personDto.Dob;
                
            if (personDto.EmergencyContactName != null)
                person.EmergencyContactName = personDto.EmergencyContactName;
                
            if (personDto.EmergencyContactNumber != null)
                person.EmergencyContactNumber = personDto.EmergencyContactNumber;
                
            if (personDto.Inactive.HasValue)
                person.Inactive = personDto.Inactive.Value;
                
            person.ModifiedDate = DateTime.UtcNow;
            person.ModifiedPeopleId = _userContextService.GetCurrentUserId();
            
            await _context.SaveChangesAsync();
            
            return await GetPersonByIdAsync(id);
        }

        public async Task<bool> DeletePersonAsync(int id)
        {
            var person = await _context.People
                .Include(p => p.TradeCapeople)
                .Include(p => p.TradeParticipations)
                .Include(p => p.TradePmpeople)
                .FirstOrDefaultAsync(p => p.PeopleId == id);
            
            if (person == null)
                return false;
                
            // Check if the person is referenced by any trades as CA
            if (person.TradeCapeople.Any())
                throw new InvalidOperationException("Cannot delete person that is referenced as a Contract Administrator in trades");
                
            // Check if the person is referenced by any trades as PM
            if (person.TradePmpeople.Any())
                throw new InvalidOperationException("Cannot delete person that is referenced as a Project Manager in trades");
                
            // Check if the person is referenced by any trade participations
            if (person.TradeParticipations.Any())
                throw new InvalidOperationException("Cannot delete person that is referenced in trade participations");
                
            _context.People.Remove(person);
            await _context.SaveChangesAsync();
            
            return true;
        }
    }
}
