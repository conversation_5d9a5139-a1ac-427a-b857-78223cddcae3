using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class RFIsResponseAttachmentsController : ControllerBase
    {
        private readonly IRFIsResponseAttachmentService _rfisResponseAttachmentService;
        
        public RFIsResponseAttachmentsController(IRFIsResponseAttachmentService rfisResponseAttachmentService)
        {
            _rfisResponseAttachmentService = rfisResponseAttachmentService;
        }
        
        // GET: api/RFIsResponseAttachments
        [HttpGet]
        public async Task<ActionResult<IEnumerable<RFIsResponseAttachmentDTO>>> GetRFIsResponseAttachments()
        {
            var attachments = await _rfisResponseAttachmentService.GetAllRFIsResponseAttachmentsAsync();
            return Ok(attachments);
        }
        
        // GET: api/RFIsResponseAttachments/5
        [HttpGet("{id}")]
        public async Task<ActionResult<RFIsResponseAttachmentDTO>> GetRFIsResponseAttachment(int id)
        {
            try
            {
                var attachment = await _rfisResponseAttachmentService.GetRFIsResponseAttachmentByIdAsync(id);
                return Ok(attachment);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // GET: api/RFIsResponseAttachments/Response/5
        [HttpGet("Response/{responseId}")]
        public async Task<ActionResult<IEnumerable<RFIsResponseAttachmentDTO>>> GetRFIsResponseAttachmentsByResponse(int responseId)
        {
            try
            {
                var attachments = await _rfisResponseAttachmentService.GetRFIsResponseAttachmentsByResponseIdAsync(responseId);
                return Ok(attachments);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // GET: api/RFIsResponseAttachments/RFI/5
        [HttpGet("RFI/{rfiId}")]
        public async Task<ActionResult<IEnumerable<RFIsResponseAttachmentDTO>>> GetRFIsResponseAttachmentsByRFI(int rfiId)
        {
            try
            {
                var attachments = await _rfisResponseAttachmentService.GetRFIsResponseAttachmentsByRFIIdAsync(rfiId);
                return Ok(attachments);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // POST: api/RFIsResponseAttachments
        [HttpPost]
        public async Task<ActionResult<RFIsResponseAttachmentDTO>> CreateRFIsResponseAttachment(RFIsResponseAttachmentCreateDTO attachmentDto)
        {
            try
            {
                var createdAttachment = await _rfisResponseAttachmentService.CreateRFIsResponseAttachmentAsync(attachmentDto);
                return CreatedAtAction(nameof(GetRFIsResponseAttachment), new { id = createdAttachment.Id }, createdAttachment);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // PUT: api/RFIsResponseAttachments/5
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateRFIsResponseAttachment(int id, RFIsResponseAttachmentUpdateDTO attachmentDto)
        {
            try
            {
                var updatedAttachment = await _rfisResponseAttachmentService.UpdateRFIsResponseAttachmentAsync(id, attachmentDto);
                return Ok(updatedAttachment);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // DELETE: api/RFIsResponseAttachments/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteRFIsResponseAttachment(int id)
        {
            try
            {
                var result = await _rfisResponseAttachmentService.DeleteRFIsResponseAttachmentAsync(id);
                
                if (!result)
                    return NotFound(new { message = $"RFIsResponseAttachment with ID {id} not found" });
                    
                return NoContent();
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
    }
}
