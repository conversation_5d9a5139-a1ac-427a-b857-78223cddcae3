using System.ComponentModel.DataAnnotations;

namespace NOBLE.API.Models.DTOs
{
    public class ClientVariationsTradeDTO
    {
        public int ClientVariationTradeId { get; set; }
        public int? ClientVariationId { get; set; }
        public string? TradeCode { get; set; }
        public decimal? Amount { get; set; }
        
        // Navigation properties for display purposes
        public string? ClientVariationName { get; set; }
        public string? ProjectName { get; set; }
    }

    public class ClientVariationsTradeCreateDTO
    {
        [Required]
        public int ClientVariationId { get; set; }
        
        [StringLength(50)]
        public string? TradeCode { get; set; }
        
        public decimal? Amount { get; set; }
    }

    public class ClientVariationsTradeUpdateDTO
    {
        [Required]
        public int ClientVariationId { get; set; }
        
        [StringLength(50)]
        public string? TradeCode { get; set; }
        
        public decimal? Amount { get; set; }
    }
}
