using System.ComponentModel.DataAnnotations;

namespace NOBLE.API.Models.DTOs
{
    public class ProcessStepDTO
    {
        public int ProcessStepId { get; set; }
        public int? ProcessId { get; set; }
        public int? AssignedPeopleId { get; set; }
        public int? ApprovedPeopleId { get; set; }
        public string? Type { get; set; }
        public string? Name { get; set; }
        public string? UserType { get; set; }
        public int? NumDays { get; set; }
        public DateTime? TargetDate { get; set; }
        public DateTime? ActualDate { get; set; }
        public string? Comments { get; set; }
        public bool? Skip { get; set; }
        
        // Navigation properties for display purposes
        public string? ProcessName { get; set; }
        public string? AssignedPersonName { get; set; }
        public string? ApprovedPersonName { get; set; }
    }

    public class ProcessStepCreateDTO
    {
        [Required]
        public int ProcessId { get; set; }
        
        public int? AssignedPeopleId { get; set; }
        public int? ApprovedPeopleId { get; set; }
        
        [StringLength(50)]
        public string? Type { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(50)]
        public string? UserType { get; set; }
        
        public int? NumDays { get; set; }
        public DateTime? TargetDate { get; set; }
        public DateTime? ActualDate { get; set; }
        
        [StringLength(1000)]
        public string? Comments { get; set; }
        
        public bool? Skip { get; set; } = false;
    }

    public class ProcessStepUpdateDTO
    {
        public int? AssignedPeopleId { get; set; }
        public int? ApprovedPeopleId { get; set; }
        
        [StringLength(50)]
        public string? Type { get; set; }
        
        [StringLength(100)]
        public string? Name { get; set; }
        
        [StringLength(50)]
        public string? UserType { get; set; }
        
        public int? NumDays { get; set; }
        public DateTime? TargetDate { get; set; }
        public DateTime? ActualDate { get; set; }
        
        [StringLength(1000)]
        public string? Comments { get; set; }
        
        public bool? Skip { get; set; }
    }
}
