using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class TradeItemsController : ControllerBase
    {
        private readonly ITradeItemService _tradeItemService;
        
        public TradeItemsController(ITradeItemService tradeItemService)
        {
            _tradeItemService = tradeItemService;
        }
        
        // GET: api/TradeItems
        [HttpGet]
        public async Task<ActionResult<IEnumerable<TradeItemDTO>>> GetTradeItems()
        {
            var tradeItems = await _tradeItemService.GetAllTradeItemsAsync();
            return Ok(tradeItems);
        }
        
        // GET: api/TradeItems/5
        [HttpGet("{id}")]
        public async Task<ActionResult<TradeItemDTO>> GetTradeItem(int id)
        {
            try
            {
                var tradeItem = await _tradeItemService.GetTradeItemByIdAsync(id);
                return Ok(tradeItem);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // GET: api/TradeItems/Category/5
        [HttpGet("Category/{categoryId}")]
        public async Task<ActionResult<IEnumerable<TradeItemDTO>>> GetTradeItemsByCategory(int categoryId)
        {
            try
            {
                var tradeItems = await _tradeItemService.GetTradeItemsByCategoryIdAsync(categoryId);
                return Ok(tradeItems);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // GET: api/TradeItems/Trade/5
        [HttpGet("Trade/{tradeId}")]
        public async Task<ActionResult<IEnumerable<TradeItemDTO>>> GetTradeItemsByTrade(int tradeId)
        {
            try
            {
                var tradeItems = await _tradeItemService.GetTradeItemsByTradeIdAsync(tradeId);
                return Ok(tradeItems);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // POST: api/TradeItems
        [HttpPost]
        public async Task<ActionResult<TradeItemDTO>> CreateTradeItem(TradeItemCreateDTO tradeItemDto)
        {
            try
            {
                var createdTradeItem = await _tradeItemService.CreateTradeItemAsync(tradeItemDto);
                return CreatedAtAction(nameof(GetTradeItem), new { id = createdTradeItem.TradeItemId }, createdTradeItem);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // PUT: api/TradeItems/5
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateTradeItem(int id, TradeItemUpdateDTO tradeItemDto)
        {
            try
            {
                var updatedTradeItem = await _tradeItemService.UpdateTradeItemAsync(id, tradeItemDto);
                return Ok(updatedTradeItem);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // DELETE: api/TradeItems/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteTradeItem(int id)
        {
            try
            {
                var result = await _tradeItemService.DeleteTradeItemAsync(id);
                
                if (!result)
                    return NotFound(new { message = $"TradeItem with ID {id} not found" });
                    
                return NoContent();
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
    }
}
