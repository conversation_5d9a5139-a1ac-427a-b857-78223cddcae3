using Microsoft.EntityFrameworkCore;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Models.Generated;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Services.Implementations
{
    public class ClientVariationsTradeService : IClientVariationsTradeService
    {
        private readonly NobleDbContext _context;
        private readonly IUserContextService _userContextService;

        public ClientVariationsTradeService(NobleDbContext context, IUserContextService userContextService)
        {
            _context = context;
            _userContextService = userContextService;
        }

        public async Task<IEnumerable<ClientVariationsTradeDTO>> GetAllClientVariationsTradesAsync()
        {
            return await _context.ClientVariationsTrades
                .Include(cvt => cvt.ClientVariation)
                .Include(cvt => cvt.ClientVariation.Project)
                .Select(cvt => new ClientVariationsTradeDTO
                {
                    ClientVariationTradeId = cvt.ClientVariationTradeId,
                    ClientVariationId = cvt.ClientVariationId,
                    TradeCode = cvt.TradeCode,
                    Amount = cvt.Amount,
                    ClientVariationName = cvt.ClientVariation.Name,
                    ProjectName = cvt.ClientVariation.Project.Name
                })
                .ToListAsync();
        }

        public async Task<ClientVariationsTradeDTO> GetClientVariationsTradeByIdAsync(int id)
        {
            var clientVariationsTrade = await _context.ClientVariationsTrades
                .Include(cvt => cvt.ClientVariation)
                .Include(cvt => cvt.ClientVariation.Project)
                .FirstOrDefaultAsync(cvt => cvt.ClientVariationTradeId == id);
            
            if (clientVariationsTrade == null)
                throw new KeyNotFoundException($"ClientVariationsTrade with ID {id} not found");
                
            return new ClientVariationsTradeDTO
            {
                ClientVariationTradeId = clientVariationsTrade.ClientVariationTradeId,
                ClientVariationId = clientVariationsTrade.ClientVariationId,
                TradeCode = clientVariationsTrade.TradeCode,
                Amount = clientVariationsTrade.Amount,
                ClientVariationName = clientVariationsTrade.ClientVariation.Name,
                ProjectName = clientVariationsTrade.ClientVariation.Project.Name
            };
        }

        public async Task<IEnumerable<ClientVariationsTradeDTO>> GetClientVariationsTradesByClientVariationIdAsync(int clientVariationId)
        {
            // Check if the client variation exists
            var clientVariation = await _context.ClientVariations.FindAsync(clientVariationId);
            if (clientVariation == null)
                throw new KeyNotFoundException($"ClientVariation with ID {clientVariationId} not found");
                
            return await _context.ClientVariationsTrades
                .Include(cvt => cvt.ClientVariation)
                .Include(cvt => cvt.ClientVariation.Project)
                .Where(cvt => cvt.ClientVariationId == clientVariationId)
                .Select(cvt => new ClientVariationsTradeDTO
                {
                    ClientVariationTradeId = cvt.ClientVariationTradeId,
                    ClientVariationId = cvt.ClientVariationId,
                    TradeCode = cvt.TradeCode,
                    Amount = cvt.Amount,
                    ClientVariationName = cvt.ClientVariation.Name,
                    ProjectName = cvt.ClientVariation.Project.Name
                })
                .ToListAsync();
        }

        public async Task<ClientVariationsTradeDTO> CreateClientVariationsTradeAsync(ClientVariationsTradeCreateDTO clientVariationsTradeDto)
        {
            // Check if the client variation exists
            var clientVariation = await _context.ClientVariations.FindAsync(clientVariationsTradeDto.ClientVariationId);
            if (clientVariation == null)
                throw new KeyNotFoundException($"ClientVariation with ID {clientVariationsTradeDto.ClientVariationId} not found");
                
            var clientVariationsTrade = new ClientVariationsTrade
            {
                ClientVariationId = clientVariationsTradeDto.ClientVariationId,
                TradeCode = clientVariationsTradeDto.TradeCode,
                Amount = clientVariationsTradeDto.Amount,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow,
                CreatedPeopleId = _userContextService.GetCurrentUserId(),
                ModifiedPeopleId = _userContextService.GetCurrentUserId()
            };
            
            _context.ClientVariationsTrades.Add(clientVariationsTrade);
            await _context.SaveChangesAsync();
            
            return await GetClientVariationsTradeByIdAsync(clientVariationsTrade.ClientVariationTradeId);
        }

        public async Task<ClientVariationsTradeDTO> UpdateClientVariationsTradeAsync(int id, ClientVariationsTradeUpdateDTO clientVariationsTradeDto)
        {
            var clientVariationsTrade = await _context.ClientVariationsTrades.FindAsync(id);
            
            if (clientVariationsTrade == null)
                throw new KeyNotFoundException($"ClientVariationsTrade with ID {id} not found");
                
            // Check if the client variation exists
            var clientVariation = await _context.ClientVariations.FindAsync(clientVariationsTradeDto.ClientVariationId);
            if (clientVariation == null)
                throw new KeyNotFoundException($"ClientVariation with ID {clientVariationsTradeDto.ClientVariationId} not found");
                
            clientVariationsTrade.ClientVariationId = clientVariationsTradeDto.ClientVariationId;
            clientVariationsTrade.TradeCode = clientVariationsTradeDto.TradeCode;
            clientVariationsTrade.Amount = clientVariationsTradeDto.Amount;
            clientVariationsTrade.ModifiedDate = DateTime.UtcNow;
            clientVariationsTrade.ModifiedPeopleId = _userContextService.GetCurrentUserId();
            
            await _context.SaveChangesAsync();
            
            return await GetClientVariationsTradeByIdAsync(id);
        }

        public async Task<bool> DeleteClientVariationsTradeAsync(int id)
        {
            var clientVariationsTrade = await _context.ClientVariationsTrades.FindAsync(id);
            
            if (clientVariationsTrade == null)
                return false;
                
            _context.ClientVariationsTrades.Remove(clientVariationsTrade);
            await _context.SaveChangesAsync();
            
            return true;
        }
    }
}
