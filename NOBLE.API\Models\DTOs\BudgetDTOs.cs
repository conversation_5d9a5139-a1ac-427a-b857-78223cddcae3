using System.ComponentModel.DataAnnotations;

namespace NOBLE.API.Models.DTOs
{
    public class BudgetDTO
    {
        public int BudgetId { get; set; }
        public int ProjectId { get; set; }
        public string Code { get; set; } = string.Empty;
        public decimal? Amount { get; set; }
         
        public string? ProjectName { get; set; }
    }

    public class BudgetCreateDTO
    {
        [Required]
        public int ProjectId { get; set; }
        
        [Required]
        [StringLength(25)]
        public string Code { get; set; } = string.Empty;
        
        public decimal? Amount { get; set; }
    }

    public class BudgetUpdateDTO
    {
        [Required]
        public int ProjectId { get; set; }
        
        [Required]
        [StringLength(25)]
        public string Code { get; set; } = string.Empty;
        
        public decimal? Amount { get; set; }
    }
}
