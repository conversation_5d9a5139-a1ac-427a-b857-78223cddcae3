import React from 'react';
import styled, { css } from 'styled-components';
import ColorPalette from '../../styles/ColorPalette';
import Typography from '../../styles/Typography';

const ButtonVariants = {
  primary: css`
    background-color: ${ColorPalette.primary.main};
    color: ${ColorPalette.primary.contrastText};
    border: none;

    &:hover {
      background-color: ${ColorPalette.primary.dark};
    }

    &:disabled {
      background-color: ${ColorPalette.neutral.lightGrey};
      color: ${ColorPalette.neutral.grey};
      cursor: not-allowed;
    }
  `,

  secondary: css`
    background-color: ${ColorPalette.secondary.main};
    color: ${ColorPalette.secondary.contrastText};
    border: none;

    &:hover {
      background-color: ${ColorPalette.secondary.dark};
    }

    &:disabled {
      background-color: ${ColorPalette.neutral.lightGrey};
      color: ${ColorPalette.neutral.grey};
      cursor: not-allowed;
    }
  `,

  outline: css`
    background-color: transparent;
    color: ${ColorPalette.primary.main};
    border: 1px solid ${ColorPalette.primary.main};

    &:hover {
      background-color: ${ColorPalette.primary.main}10;
    }

    &:disabled {
      border-color: ${ColorPalette.neutral.lightGrey};
      color: ${ColorPalette.neutral.grey};
      cursor: not-allowed;
    }
  `,

  text: css`
    background-color: transparent;
    color: ${ColorPalette.primary.main};
    border: none;
    padding: 0.5rem 1rem;

    &:hover {
      background-color: ${ColorPalette.primary.main}10;
    }

    &:disabled {
      color: ${ColorPalette.neutral.grey};
      cursor: not-allowed;
    }
  `,

  social: css`
    background-color: ${ColorPalette.neutral.white};
    color: ${ColorPalette.text.primary};
    border: 1px solid ${ColorPalette.border.main};
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;

    &:hover {
      background-color: ${ColorPalette.neutral.offWhite};
    }

    &:disabled {
      background-color: ${ColorPalette.neutral.lightGrey};
      color: ${ColorPalette.neutral.grey};
      cursor: not-allowed;
    }
  `,
};

const ButtonSizes = {
  small: css`
    padding: 0.5rem 1rem;
    font-size: ${Typography.fontSize.sm};
  `,

  medium: css`
    padding: 0.75rem 1.5rem;
    font-size: ${Typography.fontSize.md};
  `,

  large: css`
    padding: 1rem 2rem;
    font-size: ${Typography.fontSize.lg};
  `,
};

const StyledButton = styled.button`
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  font-size: 14px;
  border-radius: 8px;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 12px 24px;
  border: none;
  background-color: #374151;
  color: #FFFFFF;
  gap: 8px;

  width: ${props => props.fullWidth ? '100%' : 'auto'};

  &:hover:not(:disabled) {
    background-color: #1F2937;
    transform: translateY(-1px);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(55, 65, 81, 0.2);
  }
`;

const Button = ({
  children,
  variant = 'primary',
  size = 'medium',
  fullWidth = false,
  icon,
  ...props
}) => {
  return (
    <StyledButton
      variant={variant}
      size={size}
      fullWidth={fullWidth}
      {...props}
    >
      {icon && <span className="button-icon">{icon}</span>}
      {children}
    </StyledButton>
  );
};

export default Button;
