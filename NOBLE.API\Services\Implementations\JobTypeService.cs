using Microsoft.EntityFrameworkCore;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Models.Generated;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Services.Implementations
{
    public class JobTypeService : IJobTypeService
    {
        private readonly NobleDbContext _context;
        private readonly IUserContextService _userContextService;

        public JobTypeService(NobleDbContext context, IUserContextService userContextService)
        {
            _context = context;
            _userContextService = userContextService;
        }

        public async Task<IEnumerable<JobTypeDTO>> GetAllJobTypesAsync()
        {
            return await _context.JobTypes
                .Select(jt => new JobTypeDTO
                {
                    JobTypeId = jt.JobTypeId,
                    Name = jt.Name
                })
                .ToListAsync();
        }

        public async Task<JobTypeDTO> GetJobTypeByIdAsync(int id)
        {
            var jobType = await _context.JobTypes.FindAsync(id);
            
            if (jobType == null)
                throw new KeyNotFoundException($"JobType with ID {id} not found");
                
            return new JobTypeDTO
            {
                JobTypeId = jobType.JobTypeId,
                Name = jobType.Name
            };
        }

        public async Task<JobTypeDTO> CreateJobTypeAsync(JobTypeCreateDTO jobTypeDto)
        {
            var jobType = new JobType
            {
                Name = jobTypeDto.Name,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow,
                CreatedPeopleId = _userContextService.GetCurrentUserId(),
                ModifiedPeopleId = _userContextService.GetCurrentUserId()
            };
            
            _context.JobTypes.Add(jobType);
            await _context.SaveChangesAsync();
            
            return new JobTypeDTO
            {
                JobTypeId = jobType.JobTypeId,
                Name = jobType.Name
            };
        }

        public async Task<JobTypeDTO> UpdateJobTypeAsync(int id, JobTypeUpdateDTO jobTypeDto)
        {
            var jobType = await _context.JobTypes.FindAsync(id);
            
            if (jobType == null)
                throw new KeyNotFoundException($"JobType with ID {id} not found");
                
            jobType.Name = jobTypeDto.Name;
            jobType.ModifiedDate = DateTime.UtcNow;
            jobType.ModifiedPeopleId = _userContextService.GetCurrentUserId();
            
            await _context.SaveChangesAsync();
            
            return new JobTypeDTO
            {
                JobTypeId = jobType.JobTypeId,
                Name = jobType.Name
            };
        }

        public async Task<bool> DeleteJobTypeAsync(int id)
        {
            var jobType = await _context.JobTypes.FindAsync(id);
            
            if (jobType == null)
                return false;
                 
            var hasTrades = await _context.Trades.AnyAsync(t => t.JobTypeId == id);
            
            if (hasTrades)
            {
                throw new InvalidOperationException("Cannot delete job type that is used by trades");
            }
            
            _context.JobTypes.Remove(jobType);
            await _context.SaveChangesAsync();
            
            return true;
        }
    }
}
