import React, { useState } from 'react';
import { useParams, useLocation, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import ResetPasswordForm from '../components/auth/ResetPasswordForm';
import ColorPalette from '../styles/ColorPalette';
import Typography from '../styles/Typography';
import { useAuth } from '../context/AuthContext';

const PageContainer = styled.div`
  display: flex;
  min-height: 100vh;
  background-color: ${ColorPalette.background.default};
`;

const LeftSection = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  background-color: ${ColorPalette.background.paper};
  
  @media (max-width: 768px) {
    display: none;
  }
`;

const RightSection = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  
  @media (max-width: 768px) {
    flex: 1;
  }
`;

const HeroTitle = styled.h1`
  font-family: ${Typography.fontFamily.secondary};
  font-size: ${Typography.fontSize['4xl']};
  font-weight: ${Typography.fontWeight.bold};
  color: ${ColorPalette.text.primary};
  margin-bottom: 1rem;
  text-align: center;
`;

const HeroSubtitle = styled.p`
  font-size: ${Typography.fontSize.xl};
  color: ${ColorPalette.text.secondary};
  margin-bottom: 2rem;
  text-align: center;
  max-width: 500px;
`;

const LearnMoreButton = styled.a`
  font-family: ${Typography.fontFamily.primary};
  font-size: ${Typography.fontSize.md};
  font-weight: ${Typography.fontWeight.medium};
  color: ${ColorPalette.primary.main};
  background-color: transparent;
  border: 1px solid ${ColorPalette.primary.main};
  border-radius: 4px;
  padding: 0.75rem 1.5rem;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  text-decoration: none;
  
  &:hover {
    background-color: ${ColorPalette.primary.main}10;
  }
`;

const ErrorMessage = styled.div`
  background-color: ${ColorPalette.status.error}20;
  color: ${ColorPalette.status.error};
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1.5rem;
  font-size: ${Typography.fontSize.sm};
  max-width: 400px;
  text-align: center;
`;

const ResetPasswordPage = () => {
  const { resetPassword } = useAuth();
  const navigate = useNavigate();
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  
  // In a real app, you would get the token from the URL params
  const { token } = useParams();
  
  // And the email from the query params
  const useQuery = () => new URLSearchParams(useLocation().search);
  const query = useQuery();
  const email = query.get('email');
  
  const isValidRequest = token && email;
  
  const handleResetPassword = async (formData) => {
    setIsLoading(true);
    setError('');
    
    try {
      await resetPassword(formData.token, formData.email, formData.password, formData.confirmPassword);
      // Success is handled in the form component
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to reset password.');
      console.error('Reset password error:', err);
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <PageContainer>
      <LeftSection>
        {/* Note: Add NOBLE logo image here */}
        <div style={{ width: '200px', height: '60px', backgroundColor: '#0056B3', color: 'white', display: 'flex', alignItems: 'center', justifyContent: 'center', marginBottom: '2rem', borderRadius: '4px' }}>
          NOBLE Platform Logo
        </div>
        
        <HeroTitle>Build Smarter, Manage Faster:</HeroTitle>
        <HeroSubtitle>
          Your Ultimate Construction Project Management Solution
        </HeroSubtitle>
        
        <LearnMoreButton href="#">Learn More</LearnMoreButton>
      </LeftSection>
      
      <RightSection>
        {/* Note: Add Stick Build logo image here */}
        <div style={{ width: '150px', height: '40px', backgroundColor: '#ddd', color: '#333', display: 'flex', alignItems: 'center', justifyContent: 'center', marginBottom: '2rem', borderRadius: '4px' }}>
          Stick Build Logo
        </div>
        
        {isValidRequest ? (
          <ResetPasswordForm 
            token={token} 
            email={email} 
            onSubmit={handleResetPassword}
            error={error}
            isLoading={isLoading}
          />
        ) : (
          <ErrorMessage>
            Invalid or expired password reset link. Please request a new password reset link.
          </ErrorMessage>
        )}
      </RightSection>
    </PageContainer>
  );
};

export default ResetPasswordPage;