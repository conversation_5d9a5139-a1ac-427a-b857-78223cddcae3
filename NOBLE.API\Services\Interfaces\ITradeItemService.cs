using NOBLE.API.Models.DTOs;

namespace NOBLE.API.Services.Interfaces
{
    public interface ITradeItemService
    {
        Task<IEnumerable<TradeItemDTO>> GetAllTradeItemsAsync();
        Task<TradeItemDTO> GetTradeItemByIdAsync(int id);
        Task<IEnumerable<TradeItemDTO>> GetTradeItemsByCategoryIdAsync(int categoryId);
        Task<IEnumerable<TradeItemDTO>> GetTradeItemsByTradeIdAsync(int tradeId);
        Task<TradeItemDTO> CreateTradeItemAsync(TradeItemCreateDTO tradeItemDto);
        Task<TradeItemDTO> UpdateTradeItemAsync(int id, TradeItemUpdateDTO tradeItemDto);
        Task<bool> DeleteTradeItemAsync(int id);
    }
}
