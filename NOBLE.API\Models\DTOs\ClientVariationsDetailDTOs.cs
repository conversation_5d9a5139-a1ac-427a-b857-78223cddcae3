using System.ComponentModel.DataAnnotations;

namespace NOBLE.API.Models.DTOs
{
    public class ClientVariationsDetailDTO
    {
        public int ClientVariationDetailId { get; set; }
        public int? ClientVariationId { get; set; }
        public decimal? Amount { get; set; }
        public string? Description { get; set; }
        public int? DisplayOrder { get; set; }
        
        // Navigation properties for display purposes
        public string? ClientVariationName { get; set; }
        public string? ProjectName { get; set; }
    }

    public class ClientVariationsDetailCreateDTO
    {
        [Required]
        public int ClientVariationId { get; set; }
        
        public decimal? Amount { get; set; }
        
        public string? Description { get; set; }
        
        public int? DisplayOrder { get; set; }
    }

    public class ClientVariationsDetailUpdateDTO
    {
        [Required]
        public int ClientVariationId { get; set; }
        
        public decimal? Amount { get; set; }
        
        public string? Description { get; set; }
        
        public int? DisplayOrder { get; set; }
    }
}
