﻿using System;
using System.Collections.Generic;

namespace NOBLE.API.Models.Generated;

public partial class Process
{
    public int ProcessId { get; set; }

    public string? TemplateType { get; set; }

    public string? Name { get; set; }

    public string? StepComparisonApproval { get; set; }

    public string? StepContractApproval { get; set; }

    public int? DisplayOrder { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public int CreatedPeopleId { get; set; }

    public int ModifiedPeopleId { get; set; }

    public bool? Hide { get; set; }

    public virtual ICollection<ProcessStep> ProcessSteps { get; set; } = new List<ProcessStep>();
}
