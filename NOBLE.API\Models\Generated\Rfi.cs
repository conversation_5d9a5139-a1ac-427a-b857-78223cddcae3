﻿using System;
using System.Collections.Generic;

namespace NOBLE.API.Models.Generated;

public partial class Rfi
{
    public int Rfiid { get; set; }

    public int ProjectId { get; set; }

    public int? Number { get; set; }

    public string? Subject { get; set; }

    public string? Description { get; set; }

    public string? Status { get; set; }

    public DateTime? RaiseDate { get; set; }

    public DateTime? TargetAnswerDate { get; set; }

    public DateTime? ActualAnswerDate { get; set; }

    public string? ReferenceFile { get; set; }

    public string? ClientResponseFile { get; set; }

    public string? ClientResponseSummary { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public int CreatedPeopleId { get; set; }

    public int ModifiedPeopleId { get; set; }

    public virtual Project Project { get; set; } = null!;

    public virtual ICollection<RfisResponse> RfisResponses { get; set; } = new List<RfisResponse>();
}
