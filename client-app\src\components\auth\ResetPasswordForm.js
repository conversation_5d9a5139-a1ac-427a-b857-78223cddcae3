import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import Input from '../common/Input';
import Button from '../common/Button';
import ColorPalette from '../../styles/ColorPalette';
import Typography from '../../styles/Typography';

const ResetPasswordContainer = styled.div`
  width: 100%;
  max-width: 400px;
  padding: 2rem;
  background-color: ${ColorPalette.background.paper};
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
`;

const FormTitle = styled.h3`
  font-family: ${Typography.fontFamily.secondary};
  font-size: ${Typography.fontSize['2xl']};
  font-weight: ${Typography.fontWeight.semiBold};
  color: ${ColorPalette.text.primary};
  margin-bottom: 0.5rem;
  text-align: center;
`;

const FormSubtitle = styled.p`
  font-size: ${Typography.fontSize.md};
  color: ${ColorPalette.text.secondary};
  margin-bottom: 2rem;
  text-align: center;
`;

const BackToLoginLink = styled(Link)`
  font-size: ${Typography.fontSize.sm};
  color: ${ColorPalette.primary.main};
  display: block;
  text-align: center;
  margin-top: 1.5rem;
  
  &:hover {
    text-decoration: underline;
  }
`;

const SuccessMessage = styled.div`
  background-color: ${ColorPalette.status.success}20;
  color: ${ColorPalette.status.success};
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1.5rem;
  font-size: ${Typography.fontSize.sm};
`;

const ErrorMessageContainer = styled.div`
  background-color: ${ColorPalette.status.error}20;
  color: ${ColorPalette.status.error};
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1.5rem;
  font-size: ${Typography.fontSize.sm};
`;

const ResetPasswordForm = ({ token, email, onSubmit, error: externalError, isLoading: externalLoading }) => {
  const [formData, setFormData] = useState({
    password: '',
    confirmPassword: ''
  });
  
  const [errors, setErrors] = useState({});
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [formError, setFormError] = useState('');
  
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user types
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
    
    if (formError) {
      setFormError('');
    }
  };
  
  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }
    
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password';
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (validateForm()) {
      setIsLoading(true);
      setFormError('');
      
      try {
        await onSubmit?.({
          token,
          email,
          password: formData.password,
          confirmPassword: formData.confirmPassword
        });
        setIsSubmitted(true);
      } catch (error) {
        setFormError(error.response?.data?.message || 'Failed to reset password. Please try again.');
      } finally {
        setIsLoading(false);
      }
    }
  };
  
  // Use external state if provided
  const loading = externalLoading || isLoading;
  const errorMessage = externalError || formError;
  
  return (
    <ResetPasswordContainer>
      <FormTitle>Reset Password</FormTitle>
      <FormSubtitle>
        Create a new password for your account
      </FormSubtitle>
      
      {errorMessage && (
        <ErrorMessageContainer>
          {errorMessage}
        </ErrorMessageContainer>
      )}
      
      {isSubmitted ? (
        <>
          <SuccessMessage>
            Your password has been reset successfully. You can now log in with your new password.
          </SuccessMessage>
          <BackToLoginLink to="/login">Back to Login</BackToLoginLink>
        </>
      ) : (
        <>
          <form onSubmit={handleSubmit}>
            <Input
              label="New Password"
              id="password"
              name="password"
              type="password"
              placeholder="Enter your new password"
              value={formData.password}
              onChange={handleChange}
              error={errors.password}
            />
            
            <Input
              label="Confirm Password"
              id="confirmPassword"
              name="confirmPassword"
              type="password"
              placeholder="Confirm your new password"
              value={formData.confirmPassword}
              onChange={handleChange}
              error={errors.confirmPassword}
            />
            
            <Button type="submit" fullWidth disabled={loading}>
              {loading ? 'Resetting Password...' : 'Reset Password'}
            </Button>
          </form>
          
          <BackToLoginLink to="/login">Back to Login</BackToLoginLink>
        </>
      )}
    </ResetPasswordContainer>
  );
};

export default ResetPasswordForm;