import React from 'react';
import styled from 'styled-components';
import ColorPalette from '../../styles/ColorPalette';
import Typography from '../../styles/Typography';

const CheckboxContainer = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
`;

const HiddenCheckbox = styled.input.attrs({ type: 'checkbox' })`
  position: absolute;
  opacity: 0;
  height: 0;
  width: 0;
`;

const StyledCheckbox = styled.div`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  background: ${props => props.checked ? '#EF4444' : '#FFFFFF'};
  border: 1px solid ${props => props.checked ? '#EF4444' : '#D1D5DB'};
  border-radius: 4px;
  transition: all 0.2s ease;
  cursor: pointer;

  &:hover {
    border-color: #EF4444;
  }

  ${HiddenCheckbox}:focus + & {
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2);
  }
`;

const CheckIcon = styled.svg`
  fill: none;
  stroke: ${ColorPalette.neutral.white};
  stroke-width: 2px;
  visibility: ${props => props.checked ? 'visible' : 'hidden'};
`;

const Label = styled.label`
  font-family: 'Poppins', sans-serif;
  font-size: 13px;
  color: #6B7280;
  margin-left: 8px;
  cursor: pointer;
`;

const Checkbox = ({ label, checked, onChange, id, ...props }) => {
  return (
    <CheckboxContainer>
      <HiddenCheckbox
        id={id}
        checked={checked}
        onChange={onChange}
        {...props}
      />
      <StyledCheckbox checked={checked}>
        <CheckIcon checked={checked} viewBox="0 0 24 24">
          <polyline points="20 6 9 17 4 12" />
        </CheckIcon>
      </StyledCheckbox>
      {label && <Label htmlFor={id}>{label}</Label>}
    </CheckboxContainer>
  );
};

export default Checkbox;
