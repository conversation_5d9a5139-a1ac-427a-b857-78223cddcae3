using NOBLE.API.Models.DTOs;

namespace NOBLE.API.Services.Interfaces
{
    public interface IContractService
    {
        Task<IEnumerable<ContractDTO>> GetAllContractsAsync();
        Task<ContractDTO> GetContractByIdAsync(int id);
        Task<IEnumerable<ContractDTO>> GetContractsByTradeIdAsync(int tradeId);
        Task<IEnumerable<ContractDTO>> GetChildContractsAsync(int parentId);
        Task<ContractDTO> CreateContractAsync(ContractCreateDTO contractDto);
        Task<ContractDTO> UpdateContractAsync(int id, ContractUpdateDTO contractDto);
        Task<bool> DeleteContractAsync(int id);
    }
}
