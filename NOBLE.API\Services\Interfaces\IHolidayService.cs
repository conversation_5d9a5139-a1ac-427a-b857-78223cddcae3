using NOBLE.API.Models.DTOs;

namespace NOBLE.API.Services.Interfaces
{
    public interface IHolidayService
    {
        Task<IEnumerable<HolidayDTO>> GetAllHolidaysAsync();
        Task<HolidayDTO> GetHolidayByDateAsync(DateTime date);
        Task<IEnumerable<HolidayDTO>> GetHolidaysByYearAsync(int year);
        Task<IEnumerable<HolidayDTO>> GetHolidaysByMonthAsync(int year, int month);
        Task<IEnumerable<HolidayDTO>> GetHolidaysByDateRangeAsync(DateTime startDate, DateTime endDate);
        Task<HolidayDTO> CreateHolidayAsync(HolidayCreateDTO holidayDto);
        Task<bool> DeleteHolidayAsync(DateTime date);
        Task<bool> IsHolidayAsync(DateTime date);
    }
}
