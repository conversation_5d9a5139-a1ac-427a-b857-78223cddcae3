﻿using System;
using System.Collections.Generic;

namespace NOBLE.API.Models.Generated;

public partial class ClientVariation
{
    public int ClientVariationId { get; set; }

    public int? ParentClientVariationId { get; set; }

    public int? ProjectId { get; set; }

    public int? ProcessId { get; set; }

    public int? VariationId { get; set; }

    public string? Type { get; set; }

    public string? Name { get; set; }

    public int? Number { get; set; }

    public decimal? GoodsServicesTax { get; set; }

    public DateTime? WriteDate { get; set; }

    public DateTime? VerbalApprovalDate { get; set; }

    public DateTime? InternalApprovalDate { get; set; }

    public DateTime? ApprovalDate { get; set; }

    public DateTime? CancelDate { get; set; }

    public string? QuotesFile { get; set; }

    public bool? HideCostDetails { get; set; }

    public string? BackupFile { get; set; }

    public string? ClientApprovalFile { get; set; }

    public string? Comments { get; set; }

    public int? InvoiceNumber { get; set; }

    public DateTime? InvoiceDate { get; set; }

    public DateTime? InvoiceSentDate { get; set; }

    public DateTime? InvoiceDueDate { get; set; }

    public DateTime? InvoicePaidDate { get; set; }

    public DateTime? WorksCompletedDate { get; set; }

    public bool? UseSecondPrincipal { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public int CreatedPeopleId { get; set; }

    public int ModifiedPeopleId { get; set; }

    public virtual ICollection<ClientVariationsDetail> ClientVariationsDetails { get; set; } = new List<ClientVariationsDetail>();

    public virtual ICollection<ClientVariationsTrade> ClientVariationsTrades { get; set; } = new List<ClientVariationsTrade>();

    public virtual ICollection<ClientVariation> InverseParentClientVariation { get; set; } = new List<ClientVariation>();

    public virtual ClientVariation? ParentClientVariation { get; set; }

    public virtual Project? Project { get; set; }
}
