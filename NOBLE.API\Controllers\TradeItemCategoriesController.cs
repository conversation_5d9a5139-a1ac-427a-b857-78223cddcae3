using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class TradeItemCategoriesController : ControllerBase
    {
        private readonly ITradeItemCategoryService _tradeItemCategoryService;
        
        public TradeItemCategoriesController(ITradeItemCategoryService tradeItemCategoryService)
        {
            _tradeItemCategoryService = tradeItemCategoryService;
        }
        
        // GET: api/TradeItemCategories
        [HttpGet]
        public async Task<ActionResult<IEnumerable<TradeItemCategoryDTO>>> GetTradeItemCategories()
        {
            var tradeItemCategories = await _tradeItemCategoryService.GetAllTradeItemCategoriesAsync();
            return Ok(tradeItemCategories);
        }
        
        // GET: api/TradeItemCategories/5
        [HttpGet("{id}")]
        public async Task<ActionResult<TradeItemCategoryDTO>> GetTradeItemCategory(int id)
        {
            try
            {
                var tradeItemCategory = await _tradeItemCategoryService.GetTradeItemCategoryByIdAsync(id);
                return Ok(tradeItemCategory);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // GET: api/TradeItemCategories/Trade/5
        [HttpGet("Trade/{tradeId}")]
        public async Task<ActionResult<IEnumerable<TradeItemCategoryDTO>>> GetTradeItemCategoriesByTrade(int tradeId)
        {
            try
            {
                var tradeItemCategories = await _tradeItemCategoryService.GetTradeItemCategoriesByTradeIdAsync(tradeId);
                return Ok(tradeItemCategories);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // POST: api/TradeItemCategories
        [HttpPost]
        public async Task<ActionResult<TradeItemCategoryDTO>> CreateTradeItemCategory(TradeItemCategoryCreateDTO tradeItemCategoryDto)
        {
            try
            {
                var createdTradeItemCategory = await _tradeItemCategoryService.CreateTradeItemCategoryAsync(tradeItemCategoryDto);
                return CreatedAtAction(nameof(GetTradeItemCategory), new { id = createdTradeItemCategory.TradeItemCategoryId }, createdTradeItemCategory);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // PUT: api/TradeItemCategories/5
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateTradeItemCategory(int id, TradeItemCategoryUpdateDTO tradeItemCategoryDto)
        {
            try
            {
                var updatedTradeItemCategory = await _tradeItemCategoryService.UpdateTradeItemCategoryAsync(id, tradeItemCategoryDto);
                return Ok(updatedTradeItemCategory);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // DELETE: api/TradeItemCategories/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteTradeItemCategory(int id)
        {
            try
            {
                var result = await _tradeItemCategoryService.DeleteTradeItemCategoryAsync(id);
                
                if (!result)
                    return NotFound(new { message = $"TradeItemCategory with ID {id} not found" });
                    
                return NoContent();
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
    }
}
