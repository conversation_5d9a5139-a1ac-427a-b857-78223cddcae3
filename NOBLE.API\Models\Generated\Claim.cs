﻿using System;
using System.Collections.Generic;

namespace NOBLE.API.Models.Generated;

public partial class Claim
{
    public int ClaimId { get; set; }

    public int? ProjectId { get; set; }

    public int? ProcessId { get; set; }

    public int? Number { get; set; }

    public DateTime? WriteDate { get; set; }

    public DateTime? DraftApprovalDate { get; set; }

    public DateTime? InternalApprovalDate { get; set; }

    public DateTime? ApprovalDate { get; set; }

    public DateTime? DueDate { get; set; }

    public DateTime? ClientDueDate { get; set; }

    public decimal? GoodsServicesTax { get; set; }

    public int ModifiedPeopleId { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public int CreatedPeopleId { get; set; }

    public decimal? AdjustmentNoteAmount { get; set; }

    public string? AdjustmentNoteName { get; set; }

    public string? BackupFile1 { get; set; }

    public string? BackupFile2 { get; set; }

    public virtual Project? Project { get; set; }
}
