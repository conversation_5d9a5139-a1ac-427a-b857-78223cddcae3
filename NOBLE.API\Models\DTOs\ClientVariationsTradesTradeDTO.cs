using System.ComponentModel.DataAnnotations;

namespace NOBLE.API.Models.DTOs
{
    public class ClientVariationsTradesTradeDTO
    {
        public int ClientVariationTradeId { get; set; }
        public int TradeId { get; set; }
        public decimal? BudgetAmount { get; set; }
        public decimal? BudgetAmountInitial { get; set; }
        public decimal? BudgetAmountAllowance { get; set; }
        public decimal? BudgetAmountTradeInitial { get; set; }
        public DateTime? BudgetDate { get; set; }
        
        // Navigation properties for display purposes
        public string? ClientVariationTradeName { get; set; }
        public string? TradeName { get; set; }
        public string? ProjectName { get; set; }
    }

    public class ClientVariationsTradesTradeCreateDTO
    {
        [Required]
        public int ClientVariationTradeId { get; set; }
        
        [Required]
        public int TradeId { get; set; }
        
        public decimal? BudgetAmount { get; set; }
        public decimal? BudgetAmountInitial { get; set; }
        public decimal? BudgetAmountAllowance { get; set; }
        public decimal? BudgetAmountTradeInitial { get; set; }
        public DateTime? BudgetDate { get; set; }
    }

    public class ClientVariationsTradesTradeUpdateDTO
    {
        public decimal? BudgetAmount { get; set; }
        public decimal? BudgetAmountInitial { get; set; }
        public decimal? BudgetAmountAllowance { get; set; }
        public decimal? BudgetAmountTradeInitial { get; set; }
        public DateTime? BudgetDate { get; set; }
    }
}
