using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class ClaimsController : ControllerBase
    {
        private readonly IClaimService _claimService;
        
        public ClaimsController(IClaimService claimService)
        {
            _claimService = claimService;
        }
        
        // GET: api/Claims
        [HttpGet]
        public async Task<ActionResult<IEnumerable<ClaimDTO>>> GetClaims()
        {
            var claims = await _claimService.GetAllClaimsAsync();
            return Ok(claims);
        }
        
        // GET: api/Claims/5
        [HttpGet("{id}")]
        public async Task<ActionResult<ClaimDTO>> GetClaim(int id)
        {
            try
            {
                var claim = await _claimService.GetClaimByIdAsync(id);
                return Ok(claim);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // GET: api/Claims/Project/5
        [HttpGet("Project/{projectId}")]
        public async Task<ActionResult<IEnumerable<ClaimDTO>>> GetClaimsByProject(int projectId)
        {
            try
            {
                var claims = await _claimService.GetClaimsByProjectIdAsync(projectId);
                return Ok(claims);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // POST: api/Claims
        [HttpPost]
        public async Task<ActionResult<ClaimDTO>> CreateClaim(ClaimCreateDTO claimDto)
        {
            try
            {
                var createdClaim = await _claimService.CreateClaimAsync(claimDto);
                return CreatedAtAction(nameof(GetClaim), new { id = createdClaim.ClaimId }, createdClaim);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // PUT: api/Claims/5
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateClaim(int id, ClaimUpdateDTO claimDto)
        {
            try
            {
                var updatedClaim = await _claimService.UpdateClaimAsync(id, claimDto);
                return Ok(updatedClaim);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // DELETE: api/Claims/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteClaim(int id)
        {
            try
            {
                var result = await _claimService.DeleteClaimAsync(id);
                
                if (!result)
                    return NotFound(new { message = $"Claim with ID {id} not found" });
                    
                return NoContent();
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
    }
}
