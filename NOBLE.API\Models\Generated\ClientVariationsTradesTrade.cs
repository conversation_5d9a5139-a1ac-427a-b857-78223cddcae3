﻿using System;
using System.Collections.Generic;

namespace NOBLE.API.Models.Generated;

public partial class ClientVariationsTradesTrade
{
    public int ClientVariationTradeId { get; set; }

    public int TradeId { get; set; }

    public decimal? BudgetAmount { get; set; }

    public decimal? BudgetAmountInitial { get; set; }

    public decimal? BudgetAmountAllowance { get; set; }

    public decimal? BudgetAmountTradeInitial { get; set; }

    public DateTime? BudgetDate { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public int CreatedPeopleId { get; set; }

    public int ModifiedPeopleId { get; set; }
}
