using Microsoft.EntityFrameworkCore;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Models.Generated;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Services.Implementations
{
    public class TradeItemService : ITradeItemService
    {
        private readonly NobleDbContext _context;
        private readonly IUserContextService _userContextService;

        public TradeItemService(NobleDbContext context, IUserContextService userContextService)
        {
            _context = context;
            _userContextService = userContextService;
        }

        public async Task<IEnumerable<TradeItemDTO>> GetAllTradeItemsAsync()
        {
            return await _context.TradeItems
                .Include(ti => ti.TradeItemCategory)
                .Include(ti => ti.TradeItemCategory.Trade)
                .Include(ti => ti.TradeItemCategory.Trade.Project)
                .Select(ti => new TradeItemDTO
                {
                    TradeItemId = ti.TradeItemId,
                    TradeItemCategoryId = ti.TradeItemCategoryId,
                    Name = ti.Name,
                    Units = ti.Units,
                    ScopeOfWorks = ti.ScopeOfWorks,
                    DisplayOrder = ti.DisplayOrder,
                    RequiresQuantityCheck = ti.RequiresQuantityCheck,
                    RequiredInProposal = ti.RequiredInProposal,
                    CategoryName = ti.TradeItemCategory.Name,
                    TradeName = ti.TradeItemCategory.Trade.Name,
                    ProjectName = ti.TradeItemCategory.Trade.Project.Name
                })
                .ToListAsync();
        }

        public async Task<TradeItemDTO> GetTradeItemByIdAsync(int id)
        {
            var tradeItem = await _context.TradeItems
                .Include(ti => ti.TradeItemCategory)
                .Include(ti => ti.TradeItemCategory.Trade)
                .Include(ti => ti.TradeItemCategory.Trade.Project)
                .FirstOrDefaultAsync(ti => ti.TradeItemId == id);
            
            if (tradeItem == null)
                throw new KeyNotFoundException($"TradeItem with ID {id} not found");
                
            return new TradeItemDTO
            {
                TradeItemId = tradeItem.TradeItemId,
                TradeItemCategoryId = tradeItem.TradeItemCategoryId,
                Name = tradeItem.Name,
                Units = tradeItem.Units,
                ScopeOfWorks = tradeItem.ScopeOfWorks,
                DisplayOrder = tradeItem.DisplayOrder,
                RequiresQuantityCheck = tradeItem.RequiresQuantityCheck,
                RequiredInProposal = tradeItem.RequiredInProposal,
                CategoryName = tradeItem.TradeItemCategory.Name,
                TradeName = tradeItem.TradeItemCategory.Trade.Name,
                ProjectName = tradeItem.TradeItemCategory.Trade.Project.Name
            };
        }

        public async Task<IEnumerable<TradeItemDTO>> GetTradeItemsByCategoryIdAsync(int categoryId)
        {
            // Check if the category exists
            var category = await _context.TradeItemCategories.FindAsync(categoryId);
            if (category == null)
                throw new KeyNotFoundException($"TradeItemCategory with ID {categoryId} not found");
                
            return await _context.TradeItems
                .Include(ti => ti.TradeItemCategory)
                .Include(ti => ti.TradeItemCategory.Trade)
                .Include(ti => ti.TradeItemCategory.Trade.Project)
                .Where(ti => ti.TradeItemCategoryId == categoryId)
                .Select(ti => new TradeItemDTO
                {
                    TradeItemId = ti.TradeItemId,
                    TradeItemCategoryId = ti.TradeItemCategoryId,
                    Name = ti.Name,
                    Units = ti.Units,
                    ScopeOfWorks = ti.ScopeOfWorks,
                    DisplayOrder = ti.DisplayOrder,
                    RequiresQuantityCheck = ti.RequiresQuantityCheck,
                    RequiredInProposal = ti.RequiredInProposal,
                    CategoryName = ti.TradeItemCategory.Name,
                    TradeName = ti.TradeItemCategory.Trade.Name,
                    ProjectName = ti.TradeItemCategory.Trade.Project.Name
                })
                .ToListAsync();
        }

        public async Task<IEnumerable<TradeItemDTO>> GetTradeItemsByTradeIdAsync(int tradeId)
        {
            // Check if the trade exists
            var trade = await _context.Trades.FindAsync(tradeId);
            if (trade == null)
                throw new KeyNotFoundException($"Trade with ID {tradeId} not found");
                
            return await _context.TradeItems
                .Include(ti => ti.TradeItemCategory)
                .Include(ti => ti.TradeItemCategory.Trade)
                .Include(ti => ti.TradeItemCategory.Trade.Project)
                .Where(ti => ti.TradeItemCategory.TradeId == tradeId)
                .Select(ti => new TradeItemDTO
                {
                    TradeItemId = ti.TradeItemId,
                    TradeItemCategoryId = ti.TradeItemCategoryId,
                    Name = ti.Name,
                    Units = ti.Units,
                    ScopeOfWorks = ti.ScopeOfWorks,
                    DisplayOrder = ti.DisplayOrder,
                    RequiresQuantityCheck = ti.RequiresQuantityCheck,
                    RequiredInProposal = ti.RequiredInProposal,
                    CategoryName = ti.TradeItemCategory.Name,
                    TradeName = ti.TradeItemCategory.Trade.Name,
                    ProjectName = ti.TradeItemCategory.Trade.Project.Name
                })
                .ToListAsync();
        }

        public async Task<TradeItemDTO> CreateTradeItemAsync(TradeItemCreateDTO tradeItemDto)
        {
            // Check if the category exists
            var category = await _context.TradeItemCategories.FindAsync(tradeItemDto.TradeItemCategoryId);
            if (category == null)
                throw new KeyNotFoundException($"TradeItemCategory with ID {tradeItemDto.TradeItemCategoryId} not found");
                
            var tradeItem = new TradeItem
            {
                TradeItemCategoryId = tradeItemDto.TradeItemCategoryId,
                Name = tradeItemDto.Name,
                Units = tradeItemDto.Units,
                ScopeOfWorks = tradeItemDto.ScopeOfWorks,
                DisplayOrder = tradeItemDto.DisplayOrder,
                RequiresQuantityCheck = tradeItemDto.RequiresQuantityCheck,
                RequiredInProposal = tradeItemDto.RequiredInProposal,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow,
                CreatedPeopleId = _userContextService.GetCurrentUserId(),
                ModifiedPeopleId = _userContextService.GetCurrentUserId()
            };
            
            _context.TradeItems.Add(tradeItem);
            await _context.SaveChangesAsync();
            
            return await GetTradeItemByIdAsync(tradeItem.TradeItemId);
        }

        public async Task<TradeItemDTO> UpdateTradeItemAsync(int id, TradeItemUpdateDTO tradeItemDto)
        {
            var tradeItem = await _context.TradeItems.FindAsync(id);
            
            if (tradeItem == null)
                throw new KeyNotFoundException($"TradeItem with ID {id} not found");
                
            if (tradeItemDto.Name != null)
                tradeItem.Name = tradeItemDto.Name;
                
            if (tradeItemDto.Units != null)
                tradeItem.Units = tradeItemDto.Units;
                
            if (tradeItemDto.ScopeOfWorks != null)
                tradeItem.ScopeOfWorks = tradeItemDto.ScopeOfWorks;
                
            if (tradeItemDto.DisplayOrder.HasValue)
                tradeItem.DisplayOrder = tradeItemDto.DisplayOrder;
                
            if (tradeItemDto.RequiresQuantityCheck.HasValue)
                tradeItem.RequiresQuantityCheck = tradeItemDto.RequiresQuantityCheck;
                
            if (tradeItemDto.RequiredInProposal.HasValue)
                tradeItem.RequiredInProposal = tradeItemDto.RequiredInProposal;
                
            tradeItem.ModifiedDate = DateTime.UtcNow;
            tradeItem.ModifiedPeopleId = _userContextService.GetCurrentUserId();
            
            await _context.SaveChangesAsync();
            
            return await GetTradeItemByIdAsync(id);
        }

        public async Task<bool> DeleteTradeItemAsync(int id)
        {
            var tradeItem = await _context.TradeItems.FindAsync(id);
            
            if (tradeItem == null)
                return false;
                
            _context.TradeItems.Remove(tradeItem);
            await _context.SaveChangesAsync();
            
            return true;
        }
    }
}
