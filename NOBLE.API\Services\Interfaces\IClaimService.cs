using NOBLE.API.Models.DTOs;

namespace NOBLE.API.Services.Interfaces
{
    public interface IClaimService
    {
        Task<IEnumerable<ClaimDTO>> GetAllClaimsAsync();
        Task<ClaimDTO> GetClaimByIdAsync(int id);
        Task<IEnumerable<ClaimDTO>> GetClaimsByProjectIdAsync(int projectId);
        Task<ClaimDTO> CreateClaimAsync(ClaimCreateDTO claimDto);
        Task<ClaimDTO> UpdateClaimAsync(int id, ClaimUpdateDTO claimDto);
        Task<bool> DeleteClaimAsync(int id);
    }
}
