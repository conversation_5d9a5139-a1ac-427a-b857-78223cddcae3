using NOBLE.API.Models.DTOs;

namespace NOBLE.API.Services.Interfaces
{
    public interface ISubContractorService
    {
        Task<IEnumerable<SubContractorDTO>> GetAllSubContractorsAsync();
        Task<SubContractorDTO> GetSubContractorByIdAsync(int id);
        Task<IEnumerable<SubContractorDTO>> GetSubContractorsByBusinessUnitIdAsync(int businessUnitId);
        Task<SubContractorDTO> CreateSubContractorAsync(SubContractorCreateDTO subContractorDto);
        Task<SubContractorDTO> UpdateSubContractorAsync(int id, SubContractorUpdateDTO subContractorDto);
        Task<bool> DeleteSubContractorAsync(int id);
    }
}
