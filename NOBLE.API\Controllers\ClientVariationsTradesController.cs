using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class ClientVariationsTradesController : ControllerBase
    {
        private readonly IClientVariationsTradeService _clientVariationsTradeService;
        
        public ClientVariationsTradesController(IClientVariationsTradeService clientVariationsTradeService)
        {
            _clientVariationsTradeService = clientVariationsTradeService;
        }
        
        // GET: api/ClientVariationsTrades
        [HttpGet]
        public async Task<ActionResult<IEnumerable<ClientVariationsTradeDTO>>> GetClientVariationsTrades()
        {
            var clientVariationsTrades = await _clientVariationsTradeService.GetAllClientVariationsTradesAsync();
            return Ok(clientVariationsTrades);
        }
        
        // GET: api/ClientVariationsTrades/5
        [HttpGet("{id}")]
        public async Task<ActionResult<ClientVariationsTradeDTO>> GetClientVariationsTrade(int id)
        {
            try
            {
                var clientVariationsTrade = await _clientVariationsTradeService.GetClientVariationsTradeByIdAsync(id);
                return Ok(clientVariationsTrade);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // GET: api/ClientVariationsTrades/ClientVariation/5
        [HttpGet("ClientVariation/{clientVariationId}")]
        public async Task<ActionResult<IEnumerable<ClientVariationsTradeDTO>>> GetClientVariationsTradesByClientVariation(int clientVariationId)
        {
            try
            {
                var clientVariationsTrades = await _clientVariationsTradeService.GetClientVariationsTradesByClientVariationIdAsync(clientVariationId);
                return Ok(clientVariationsTrades);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // POST: api/ClientVariationsTrades
        [HttpPost]
        public async Task<ActionResult<ClientVariationsTradeDTO>> CreateClientVariationsTrade(ClientVariationsTradeCreateDTO clientVariationsTradeDto)
        {
            try
            {
                var createdClientVariationsTrade = await _clientVariationsTradeService.CreateClientVariationsTradeAsync(clientVariationsTradeDto);
                return CreatedAtAction(nameof(GetClientVariationsTrade), new { id = createdClientVariationsTrade.ClientVariationTradeId }, createdClientVariationsTrade);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // PUT: api/ClientVariationsTrades/5
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateClientVariationsTrade(int id, ClientVariationsTradeUpdateDTO clientVariationsTradeDto)
        {
            try
            {
                var updatedClientVariationsTrade = await _clientVariationsTradeService.UpdateClientVariationsTradeAsync(id, clientVariationsTradeDto);
                return Ok(updatedClientVariationsTrade);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // DELETE: api/ClientVariationsTrades/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteClientVariationsTrade(int id)
        {
            try
            {
                var result = await _clientVariationsTradeService.DeleteClientVariationsTradeAsync(id);
                
                if (!result)
                    return NotFound(new { message = $"ClientVariationsTrade with ID {id} not found" });
                    
                return NoContent();
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
    }
}
