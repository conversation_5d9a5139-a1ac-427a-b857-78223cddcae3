﻿using System;
using System.Collections.Generic;

namespace NOBLE.API.Models.Generated;

public partial class SubContractor
{
    public int SubContractorId { get; set; }

    public int? BusinessUnitId { get; set; }

    public string? Name { get; set; }

    public string? ShortName { get; set; }

    public string? Street { get; set; }

    public string? Locality { get; set; }

    public string? State { get; set; }

    public string? PostalCode { get; set; }

    public string? Comments { get; set; }

    public string? Account { get; set; }

    public string? Abn { get; set; }

    public string? Phone { get; set; }

    public string? Fax { get; set; }

    public string? Website { get; set; }

    public string? Acn { get; set; }

    public string? LicenceNumber { get; set; }

    public string? PrequalifiedForm { get; set; }

    public string? PublicLiabilityInsurance { get; set; }

    public string? WorkCoverInsurance { get; set; }

    public string? ProfessionalIndemnityInsurance { get; set; }

    public bool? Dccontractor { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public int CreatedPeopleId { get; set; }

    public int ModifiedPeopleId { get; set; }

    public virtual BusinessUnit? BusinessUnit { get; set; }
}
