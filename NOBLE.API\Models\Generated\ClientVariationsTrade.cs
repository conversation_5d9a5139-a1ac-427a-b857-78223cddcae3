﻿using System;
using System.Collections.Generic;

namespace NOBLE.API.Models.Generated;

public partial class ClientVariationsTrade
{
    public int ClientVariationTradeId { get; set; }

    public int? ClientVariationId { get; set; }

    public string? TradeCode { get; set; }

    public decimal? Amount { get; set; }

    public int ModifiedPeopleId { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public int CreatedPeopleId { get; set; }

    public virtual ClientVariation? ClientVariation { get; set; }
}
