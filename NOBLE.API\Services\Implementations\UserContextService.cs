using Microsoft.EntityFrameworkCore;
using NOBLE.API.Models.Generated;
using NOBLE.API.Services.Interfaces;
using System.Security.Claims;

namespace NOBLE.API.Services.Implementations
{
    public class UserContextService : IUserContextService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly NobleDbContext _context;

        public UserContextService(IHttpContextAccessor httpContextAccessor, NobleDbContext context)
        {
            _httpContextAccessor = httpContextAccessor;
            _context = context;
        }

        public int GetCurrentUserId()
        {
            var userIdClaim = _httpContextAccessor.HttpContext?.User?.FindFirst(ClaimTypes.NameIdentifier);

            if (userIdClaim != null && int.TryParse(userIdClaim.Value, out int userId))
            {
                return userId;
            }
             
            var username = GetCurrentUserName();
            if (!string.IsNullOrEmpty(username))
            {
                var user = _context.People.FirstOrDefault(p => p.UserLogin == username);
                if (user != null)
                {
                    return user.PeopleId;
                }
            }

            throw new UnauthorizedAccessException("User is not authenticated or user ID could not be determined");
        }

        public string GetCurrentUserName()
        {
            return _httpContextAccessor.HttpContext?.User?.Identity?.Name ??
                   throw new UnauthorizedAccessException("User is not authenticated or username could not be determined");
        }
    }
}
