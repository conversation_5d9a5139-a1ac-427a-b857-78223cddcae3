using Microsoft.EntityFrameworkCore;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Models.Generated;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Services.Implementations
{
    public class EOTService : IEOTService
    {
        private readonly NobleDbContext _context;
        private readonly IUserContextService _userContextService;

        public EOTService(NobleDbContext context, IUserContextService userContextService)
        {
            _context = context;
            _userContextService = userContextService;
        }

        public async Task<IEnumerable<EOTDTO>> GetAllEOTsAsync()
        {
            return await _context.Eots
                .Include(e => e.Project)
                .Select(e => new EOTDTO
                {
                    EOTId = e.Eotid,
                    ProjectId = e.ProjectId,
                    Number = e.Number,
                    StartDate = e.StartDate,
                    EndDate = e.EndDate,
                    FirstNoticeDate = e.FirstNoticeDate,
                    WriteDate = e.WriteDate,
                    SendDate = e.SendDate,
                    ApprovalDate = e.ApprovalDate,
                    DaysClaimed = e.DaysClaimed,
                    DaysApproved = e.DaysApproved,
                    Cause = e.Cause,
                    Nature = e.Nature,
                    Period = e.Period,
                    Works = e.Works,
                    CostCode = e.CostCode,
                    Status = e.Status,
                    ClientApprovalFile = e.ClientApprovalFile,
                    ClientBackUpFile = e.ClientBackUpFile,
                    EOTType = e.EotType,
                    ProjectName = e.Project.Name
                })
                .ToListAsync();
        }

        public async Task<EOTDTO> GetEOTByIdAsync(int id)
        {
            var eot = await _context.Eots
                .Include(e => e.Project)
                .FirstOrDefaultAsync(e => e.Eotid == id);
            
            if (eot == null)
                throw new KeyNotFoundException($"EOT with ID {id} not found");
                
            return new EOTDTO
            {
                EOTId = eot.Eotid,
                ProjectId = eot.ProjectId,
                Number = eot.Number,
                StartDate = eot.StartDate,
                EndDate = eot.EndDate,
                FirstNoticeDate = eot.FirstNoticeDate,
                WriteDate = eot.WriteDate,
                SendDate = eot.SendDate,
                ApprovalDate = eot.ApprovalDate,
                DaysClaimed = eot.DaysClaimed,
                DaysApproved = eot.DaysApproved,
                Cause = eot.Cause,
                Nature = eot.Nature,
                Period = eot.Period,
                Works = eot.Works,
                CostCode = eot.CostCode,
                Status = eot.Status,
                ClientApprovalFile = eot.ClientApprovalFile,
                ClientBackUpFile = eot.ClientBackUpFile,
                EOTType = eot.EotType,
                ProjectName = eot.Project.Name
            };
        }

        public async Task<IEnumerable<EOTDTO>> GetEOTsByProjectIdAsync(int projectId)
        {
            // Check if the project exists
            var project = await _context.Projects.FindAsync(projectId);
            if (project == null)
                throw new KeyNotFoundException($"Project with ID {projectId} not found");
                
            return await _context.Eots
                .Include(e => e.Project)
                .Where(e => e.ProjectId == projectId)
                .Select(e => new EOTDTO
                {
                    EOTId = e.Eotid,
                    ProjectId = e.ProjectId,
                    Number = e.Number,
                    StartDate = e.StartDate,
                    EndDate = e.EndDate,
                    FirstNoticeDate = e.FirstNoticeDate,
                    WriteDate = e.WriteDate,
                    SendDate = e.SendDate,
                    ApprovalDate = e.ApprovalDate,
                    DaysClaimed = e.DaysClaimed,
                    DaysApproved = e.DaysApproved,
                    Cause = e.Cause,
                    Nature = e.Nature,
                    Period = e.Period,
                    Works = e.Works,
                    CostCode = e.CostCode,
                    Status = e.Status,
                    ClientApprovalFile = e.ClientApprovalFile,
                    ClientBackUpFile = e.ClientBackUpFile,
                    EOTType = e.EotType,
                    ProjectName = e.Project.Name
                })
                .ToListAsync();
        }

        public async Task<EOTDTO> CreateEOTAsync(EOTCreateDTO eotDto)
        {
            // Check if the project exists
            var project = await _context.Projects.FindAsync(eotDto.ProjectId);
            if (project == null)
                throw new KeyNotFoundException($"Project with ID {eotDto.ProjectId} not found");
                
            var eot = new Eot
            {
                ProjectId = eotDto.ProjectId,
                Number = eotDto.Number,
                StartDate = eotDto.StartDate,
                EndDate = eotDto.EndDate,
                FirstNoticeDate = eotDto.FirstNoticeDate,
                WriteDate = eotDto.WriteDate,
                SendDate = eotDto.SendDate,
                ApprovalDate = eotDto.ApprovalDate,
                DaysClaimed = eotDto.DaysClaimed,
                DaysApproved = eotDto.DaysApproved,
                Cause = eotDto.Cause,
                Nature = eotDto.Nature,
                Period = eotDto.Period,
                Works = eotDto.Works,
                CostCode = eotDto.CostCode,
                Status = eotDto.Status,
                ClientApprovalFile = eotDto.ClientApprovalFile,
                ClientBackUpFile = eotDto.ClientBackUpFile,
                EotType = eotDto.EOTType,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow,
                CreatedPeopleId = _userContextService.GetCurrentUserId(),
                ModifiedPeopleId = _userContextService.GetCurrentUserId()
            };
            
            _context.Eots.Add(eot);
            await _context.SaveChangesAsync();
            
            return await GetEOTByIdAsync(eot.Eotid);
        }

        public async Task<EOTDTO> UpdateEOTAsync(int id, EOTUpdateDTO eotDto)
        {
            var eot = await _context.Eots.FindAsync(id);
            
            if (eot == null)
                throw new KeyNotFoundException($"EOT with ID {id} not found");
                
            // Check if the project exists
            var project = await _context.Projects.FindAsync(eotDto.ProjectId);
            if (project == null)
                throw new KeyNotFoundException($"Project with ID {eotDto.ProjectId} not found");
                
            eot.ProjectId = eotDto.ProjectId;
            eot.Number = eotDto.Number;
            eot.StartDate = eotDto.StartDate;
            eot.EndDate = eotDto.EndDate;
            eot.FirstNoticeDate = eotDto.FirstNoticeDate;
            eot.WriteDate = eotDto.WriteDate;
            eot.SendDate = eotDto.SendDate;
            eot.ApprovalDate = eotDto.ApprovalDate;
            eot.DaysClaimed = eotDto.DaysClaimed;
            eot.DaysApproved = eotDto.DaysApproved;
            eot.Cause = eotDto.Cause;
            eot.Nature = eotDto.Nature;
            eot.Period = eotDto.Period;
            eot.Works = eotDto.Works;
            eot.CostCode = eotDto.CostCode;
            eot.Status = eotDto.Status;
            eot.ClientApprovalFile = eotDto.ClientApprovalFile;
            eot.ClientBackUpFile = eotDto.ClientBackUpFile;
            eot.EotType = eotDto.EOTType;
            eot.ModifiedDate = DateTime.UtcNow;
            eot.ModifiedPeopleId = _userContextService.GetCurrentUserId();
            
            await _context.SaveChangesAsync();
            
            return await GetEOTByIdAsync(id);
        }

        public async Task<bool> DeleteEOTAsync(int id)
        {
            var eot = await _context.Eots.FindAsync(id);
            
            if (eot == null)
                return false;
                
            _context.Eots.Remove(eot);
            await _context.SaveChangesAsync();
            
            return true;
        }
    }
}
