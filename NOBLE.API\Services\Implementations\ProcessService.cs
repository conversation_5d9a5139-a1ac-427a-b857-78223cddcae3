using Microsoft.EntityFrameworkCore;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Models.Generated;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Services.Implementations
{
    public class ProcessService : IProcessService
    {
        private readonly NobleDbContext _context;
        private readonly IUserContextService _userContextService;

        public ProcessService(NobleDbContext context, IUserContextService userContextService)
        {
            _context = context;
            _userContextService = userContextService;
        }

        public async Task<IEnumerable<ProcessDTO>> GetAllProcessesAsync()
        {
            return await _context.Processes
                .Include(p => p.ProcessSteps)
                .Select(p => new ProcessDTO
                {
                    ProcessId = p.ProcessId,
                    TemplateType = p.TemplateType,
                    Name = p.Name,
                    StepComparisonApproval = p.StepComparisonApproval,
                    StepContractApproval = p.StepContractApproval,
                    DisplayOrder = p.DisplayOrder,
                    Hide = p.Hide,
                    ProcessStepsCount = p.ProcessSteps.Count
                })
                .ToListAsync();
        }

        public async Task<ProcessDTO> GetProcessByIdAsync(int id)
        {
            var process = await _context.Processes
                .Include(p => p.ProcessSteps)
                .FirstOrDefaultAsync(p => p.ProcessId == id);
            
            if (process == null)
                throw new KeyNotFoundException($"Process with ID {id} not found");
                
            return new ProcessDTO
            {
                ProcessId = process.ProcessId,
                TemplateType = process.TemplateType,
                Name = process.Name,
                StepComparisonApproval = process.StepComparisonApproval,
                StepContractApproval = process.StepContractApproval,
                DisplayOrder = process.DisplayOrder,
                Hide = process.Hide,
                ProcessStepsCount = process.ProcessSteps.Count
            };
        }

        public async Task<IEnumerable<ProcessDTO>> GetProcessesByTemplateTypeAsync(string templateType)
        {
            return await _context.Processes
                .Include(p => p.ProcessSteps)
                .Where(p => p.TemplateType == templateType)
                .Select(p => new ProcessDTO
                {
                    ProcessId = p.ProcessId,
                    TemplateType = p.TemplateType,
                    Name = p.Name,
                    StepComparisonApproval = p.StepComparisonApproval,
                    StepContractApproval = p.StepContractApproval,
                    DisplayOrder = p.DisplayOrder,
                    Hide = p.Hide,
                    ProcessStepsCount = p.ProcessSteps.Count
                })
                .ToListAsync();
        }

        public async Task<IEnumerable<ProcessDTO>> GetVisibleProcessesAsync()
        {
            return await _context.Processes
                .Include(p => p.ProcessSteps)
                .Where(p => p.Hide != true)
                .Select(p => new ProcessDTO
                {
                    ProcessId = p.ProcessId,
                    TemplateType = p.TemplateType,
                    Name = p.Name,
                    StepComparisonApproval = p.StepComparisonApproval,
                    StepContractApproval = p.StepContractApproval,
                    DisplayOrder = p.DisplayOrder,
                    Hide = p.Hide,
                    ProcessStepsCount = p.ProcessSteps.Count
                })
                .ToListAsync();
        }

        public async Task<ProcessDTO> CreateProcessAsync(ProcessCreateDTO processDto)
        {
            var process = new Process
            {
                TemplateType = processDto.TemplateType,
                Name = processDto.Name,
                StepComparisonApproval = processDto.StepComparisonApproval,
                StepContractApproval = processDto.StepContractApproval,
                DisplayOrder = processDto.DisplayOrder,
                Hide = processDto.Hide,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow,
                CreatedPeopleId = _userContextService.GetCurrentUserId(),
                ModifiedPeopleId = _userContextService.GetCurrentUserId()
            };
            
            _context.Processes.Add(process);
            await _context.SaveChangesAsync();
            
            return await GetProcessByIdAsync(process.ProcessId);
        }

        public async Task<ProcessDTO> UpdateProcessAsync(int id, ProcessUpdateDTO processDto)
        {
            var process = await _context.Processes.FindAsync(id);
            
            if (process == null)
                throw new KeyNotFoundException($"Process with ID {id} not found");
                
            if (processDto.TemplateType != null)
                process.TemplateType = processDto.TemplateType;
                
            if (processDto.Name != null)
                process.Name = processDto.Name;
                
            if (processDto.StepComparisonApproval != null)
                process.StepComparisonApproval = processDto.StepComparisonApproval;
                
            if (processDto.StepContractApproval != null)
                process.StepContractApproval = processDto.StepContractApproval;
                
            if (processDto.DisplayOrder.HasValue)
                process.DisplayOrder = processDto.DisplayOrder;
                
            if (processDto.Hide.HasValue)
                process.Hide = processDto.Hide;
                
            process.ModifiedDate = DateTime.UtcNow;
            process.ModifiedPeopleId = _userContextService.GetCurrentUserId();
            
            await _context.SaveChangesAsync();
            
            return await GetProcessByIdAsync(id);
        }

        public async Task<bool> DeleteProcessAsync(int id)
        {
            var process = await _context.Processes
                .Include(p => p.ProcessSteps)
                .FirstOrDefaultAsync(p => p.ProcessId == id);
            
            if (process == null)
                return false;
                
            // Check if there are any process steps
            if (process.ProcessSteps.Any())
                throw new InvalidOperationException("Cannot delete process that has process steps. Delete the process steps first.");
                
            _context.Processes.Remove(process);
            await _context.SaveChangesAsync();
            
            return true;
        }
    }
}
