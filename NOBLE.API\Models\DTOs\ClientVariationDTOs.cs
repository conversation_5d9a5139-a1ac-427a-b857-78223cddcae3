using System.ComponentModel.DataAnnotations;

namespace NOBLE.API.Models.DTOs
{
    public class ClientVariationDTO
    {
        public int ClientVariationId { get; set; }
        public int? ParentClientVariationId { get; set; }
        public int? ProjectId { get; set; }
        public int? ProcessId { get; set; }
        public int? VariationId { get; set; }
        public string? Type { get; set; }
        public string? Name { get; set; }
        public int? Number { get; set; }
        public decimal? GoodsServicesTax { get; set; }
        public DateTime? WriteDate { get; set; }
        public DateTime? VerbalApprovalDate { get; set; }
        public DateTime? InternalApprovalDate { get; set; }
        public DateTime? ApprovalDate { get; set; }
        public DateTime? CancelDate { get; set; }
        public string? QuotesFile { get; set; }
        public bool? HideCostDetails { get; set; }
        public string? BackupFile { get; set; }
        public string? ClientApprovalFile { get; set; }
        public string? Comments { get; set; }
        public int? InvoiceNumber { get; set; }
        public DateTime? InvoiceDate { get; set; }
        public DateTime? InvoiceSentDate { get; set; }
        public DateTime? InvoiceDueDate { get; set; }
        public DateTime? InvoicePaidDate { get; set; }
        public DateTime? WorksCompletedDate { get; set; }
        public bool? UseSecondPrincipal { get; set; }
        
        // Navigation properties for display purposes
        public string? ProjectName { get; set; }
        public string? ParentClientVariationName { get; set; }
    }

    public class ClientVariationCreateDTO
    {
        public int? ParentClientVariationId { get; set; }
        
        [Required]
        public int ProjectId { get; set; }
        
        public int? ProcessId { get; set; }
        public int? VariationId { get; set; }
        
        [StringLength(3)]
        public string? Type { get; set; }
        
        [StringLength(100)]
        public string? Name { get; set; }
        
        public int? Number { get; set; }
        public decimal? GoodsServicesTax { get; set; }
        public DateTime? WriteDate { get; set; }
        public DateTime? VerbalApprovalDate { get; set; }
        public DateTime? InternalApprovalDate { get; set; }
        public DateTime? ApprovalDate { get; set; }
        public DateTime? CancelDate { get; set; }
        
        [StringLength(250)]
        public string? QuotesFile { get; set; }
        
        public bool? HideCostDetails { get; set; }
        
        [StringLength(250)]
        public string? BackupFile { get; set; }
        
        [StringLength(250)]
        public string? ClientApprovalFile { get; set; }
        
        [StringLength(1000)]
        public string? Comments { get; set; }
        
        public int? InvoiceNumber { get; set; }
        public DateTime? InvoiceDate { get; set; }
        public DateTime? InvoiceSentDate { get; set; }
        public DateTime? InvoiceDueDate { get; set; }
        public DateTime? InvoicePaidDate { get; set; }
        public DateTime? WorksCompletedDate { get; set; }
        public bool? UseSecondPrincipal { get; set; }
    }

    public class ClientVariationUpdateDTO
    {
        public int? ParentClientVariationId { get; set; }
        
        [Required]
        public int ProjectId { get; set; }
        
        public int? ProcessId { get; set; }
        public int? VariationId { get; set; }
        
        [StringLength(3)]
        public string? Type { get; set; }
        
        [StringLength(100)]
        public string? Name { get; set; }
        
        public int? Number { get; set; }
        public decimal? GoodsServicesTax { get; set; }
        public DateTime? WriteDate { get; set; }
        public DateTime? VerbalApprovalDate { get; set; }
        public DateTime? InternalApprovalDate { get; set; }
        public DateTime? ApprovalDate { get; set; }
        public DateTime? CancelDate { get; set; }
        
        [StringLength(250)]
        public string? QuotesFile { get; set; }
        
        public bool? HideCostDetails { get; set; }
        
        [StringLength(250)]
        public string? BackupFile { get; set; }
        
        [StringLength(250)]
        public string? ClientApprovalFile { get; set; }
        
        [StringLength(1000)]
        public string? Comments { get; set; }
        
        public int? InvoiceNumber { get; set; }
        public DateTime? InvoiceDate { get; set; }
        public DateTime? InvoiceSentDate { get; set; }
        public DateTime? InvoiceDueDate { get; set; }
        public DateTime? InvoicePaidDate { get; set; }
        public DateTime? WorksCompletedDate { get; set; }
        public bool? UseSecondPrincipal { get; set; }
    }
}
