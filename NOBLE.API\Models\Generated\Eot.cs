﻿using System;
using System.Collections.Generic;

namespace NOBLE.API.Models.Generated;

public partial class Eot
{
    public int Eotid { get; set; }

    public int ProjectId { get; set; }

    public int? Number { get; set; }

    public DateTime? StartDate { get; set; }

    public DateTime? EndDate { get; set; }

    public DateTime? FirstNoticeDate { get; set; }

    public DateTime? WriteDate { get; set; }

    public DateTime? SendDate { get; set; }

    public DateTime? ApprovalDate { get; set; }

    public double? DaysClaimed { get; set; }

    public double? DaysApproved { get; set; }

    public string? Cause { get; set; }

    public string? Nature { get; set; }

    public string? Period { get; set; }

    public string? Works { get; set; }

    public string? CostCode { get; set; }

    public string? Status { get; set; }

    public string? ClientApprovalFile { get; set; }

    public string? ClientBackUpFile { get; set; }

    public string? EotType { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public int CreatedPeopleId { get; set; }

    public int ModifiedPeopleId { get; set; }

    public virtual Project Project { get; set; } = null!;
}
