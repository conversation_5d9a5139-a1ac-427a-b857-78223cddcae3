using Microsoft.EntityFrameworkCore;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Models.Generated;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Services.Implementations
{
    public class TradeService : ITradeService
    {
        private readonly NobleDbContext _context;
        private readonly IUserContextService _userContextService;

        public TradeService(NobleDbContext context, IUserContextService userContextService)
        {
            _context = context;
            _userContextService = userContextService;
        }

        public async Task<IEnumerable<TradeDTO>> GetAllTradesAsync()
        {
            return await _context.Trades
                .Include(t => t.JobType)
                .Include(t => t.Project)
                .Include(t => t.Contract)
                .Include(t => t.Capeople)
                .Include(t => t.Pmpeople)
                .Select(t => MapToTradeDTO(t))
                .ToListAsync();
        }

        public async Task<TradeDTO> GetTradeByIdAsync(int id)
        {
            var trade = await _context.Trades
                .Include(t => t.JobType)
                .Include(t => t.Project)
                .Include(t => t.Contract)
                .Include(t => t.Capeople)
                .Include(t => t.Pmpeople)
                .FirstOrDefaultAsync(t => t.TradeId == id);

            if (trade == null)
                throw new KeyNotFoundException($"Trade with ID {id} not found");

            return MapToTradeDTO(trade);
        }

        public async Task<IEnumerable<TradeDTO>> GetTradesByProjectIdAsync(int projectId)
        { 
            var project = await _context.Projects.FindAsync(projectId);
            if (project == null)
                throw new KeyNotFoundException($"Project with ID {projectId} not found");

            return await _context.Trades
                .Include(t => t.JobType)
                .Include(t => t.Project)
                .Include(t => t.Contract)
                .Include(t => t.Capeople)
                .Include(t => t.Pmpeople)
                .Where(t => t.ProjectId == projectId)
                .Select(t => MapToTradeDTO(t))
                .ToListAsync();
        }

        public async Task<TradeDTO> CreateTradeAsync(TradeCreateDTO tradeDto)
        { 
            if (tradeDto.ProjectId > 0)
            {
                var project = await _context.Projects.FindAsync(tradeDto.ProjectId);
                if (project == null)
                    throw new KeyNotFoundException($"Project with ID {tradeDto.ProjectId} not found");
            }
             
            if (tradeDto.JobTypeId.HasValue)
            {
                var jobType = await _context.JobTypes.FindAsync(tradeDto.JobTypeId);
                if (jobType == null)
                    throw new KeyNotFoundException($"JobType with ID {tradeDto.JobTypeId} not found");
            }
             
            if (tradeDto.ContractId.HasValue)
            {
                var contract = await _context.Contracts.FindAsync(tradeDto.ContractId);
                if (contract == null)
                    throw new KeyNotFoundException($"Contract with ID {tradeDto.ContractId} not found");
            }
             
            if (tradeDto.CapeopleId.HasValue)
            {
                var caPeople = await _context.People.FindAsync(tradeDto.CapeopleId);
                if (caPeople == null)
                    throw new KeyNotFoundException($"Person with ID {tradeDto.CapeopleId} not found");
            }
             
            if (tradeDto.PmpeopleId.HasValue)
            {
                var pmPeople = await _context.People.FindAsync(tradeDto.PmpeopleId);
                if (pmPeople == null)
                    throw new KeyNotFoundException($"Person with ID {tradeDto.PmpeopleId} not found");
            }

            var trade = new Trade
            {
                JobTypeId = tradeDto.JobTypeId,
                ProjectId = tradeDto.ProjectId,
                ProcessId = tradeDto.ProcessId,
                ContractId = tradeDto.ContractId,
                Name = tradeDto.Name,
                Code = tradeDto.Code,
                TenderRequired = tradeDto.TenderRequired,
                Description = tradeDto.Description,
                ScopeHeader = tradeDto.ScopeHeader,
                ScopeFooter = tradeDto.ScopeFooter,
                DisplayOrder = tradeDto.DisplayOrder,
                DaysFromPcd = tradeDto.DaysFromPcd,
                InvitationDate = tradeDto.InvitationDate,
                DueDate = tradeDto.DueDate,
                ComparisonDueDate = tradeDto.ComparisonDueDate,
                ContractDueDate = tradeDto.ContractDueDate,
                ComparisonApprovalDate = tradeDto.ComparisonApprovalDate,
                ComparisonApprovalAmount = tradeDto.ComparisonApprovalAmount,
                CommencementDate = tradeDto.CommencementDate,
                CompletionDate = tradeDto.CompletionDate,
                WorkOrderNumber = tradeDto.WorkOrderNumber,
                QuotesFile = tradeDto.QuotesFile,
                PrelettingFile = tradeDto.PrelettingFile,
                SignedContractFile = tradeDto.SignedContractFile,
                CapeopleId = tradeDto.CapeopleId,
                PmpeopleId = tradeDto.PmpeopleId,
                Flag = tradeDto.Flag,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow,
                CreatedPeopleId = _userContextService.GetCurrentUserId(),
                ModifiedPeopleId = _userContextService.GetCurrentUserId()
            };

            _context.Trades.Add(trade);
            await _context.SaveChangesAsync();

            return await GetTradeByIdAsync(trade.TradeId);
        }

        public async Task<TradeDTO> UpdateTradeAsync(int id, TradeUpdateDTO tradeDto)
        {
            var trade = await _context.Trades.FindAsync(id);

            if (trade == null)
                throw new KeyNotFoundException($"Trade with ID {id} not found");
             
            if (tradeDto.ProjectId.HasValue)
            {
                var project = await _context.Projects.FindAsync(tradeDto.ProjectId);
                if (project == null)
                    throw new KeyNotFoundException($"Project with ID {tradeDto.ProjectId} not found");
            }
             
            if (tradeDto.JobTypeId.HasValue)
            {
                var jobType = await _context.JobTypes.FindAsync(tradeDto.JobTypeId);
                if (jobType == null)
                    throw new KeyNotFoundException($"JobType with ID {tradeDto.JobTypeId} not found");
            }
             
            if (tradeDto.ContractId.HasValue)
            {
                var contract = await _context.Contracts.FindAsync(tradeDto.ContractId);
                if (contract == null)
                    throw new KeyNotFoundException($"Contract with ID {tradeDto.ContractId} not found");
            }
             
            if (tradeDto.CapeopleId.HasValue)
            {
                var caPeople = await _context.People.FindAsync(tradeDto.CapeopleId);
                if (caPeople == null)
                    throw new KeyNotFoundException($"Person with ID {tradeDto.CapeopleId} not found");
            }
             
            if (tradeDto.PmpeopleId.HasValue)
            {
                var pmPeople = await _context.People.FindAsync(tradeDto.PmpeopleId);
                if (pmPeople == null)
                    throw new KeyNotFoundException($"Person with ID {tradeDto.PmpeopleId} not found");
            }

            trade.JobTypeId = tradeDto.JobTypeId ?? trade.JobTypeId;
            trade.ProjectId = tradeDto.ProjectId ?? trade.ProjectId;
            trade.ProcessId = tradeDto.ProcessId ?? trade.ProcessId;
            trade.ContractId = tradeDto.ContractId ?? trade.ContractId;
            trade.Name = tradeDto.Name;
            trade.Code = tradeDto.Code;
            trade.TenderRequired = tradeDto.TenderRequired ?? trade.TenderRequired;
            trade.Description = tradeDto.Description;
            trade.ScopeHeader = tradeDto.ScopeHeader;
            trade.ScopeFooter = tradeDto.ScopeFooter;
            trade.DisplayOrder = tradeDto.DisplayOrder ?? trade.DisplayOrder;
            trade.DaysFromPcd = tradeDto.DaysFromPcd ?? trade.DaysFromPcd;
            trade.InvitationDate = tradeDto.InvitationDate ?? trade.InvitationDate;
            trade.DueDate = tradeDto.DueDate ?? trade.DueDate;
            trade.ComparisonDueDate = tradeDto.ComparisonDueDate ?? trade.ComparisonDueDate;
            trade.ContractDueDate = tradeDto.ContractDueDate ?? trade.ContractDueDate;
            trade.ComparisonApprovalDate = tradeDto.ComparisonApprovalDate ?? trade.ComparisonApprovalDate;
            trade.ComparisonApprovalAmount = tradeDto.ComparisonApprovalAmount ?? trade.ComparisonApprovalAmount;
            trade.CommencementDate = tradeDto.CommencementDate ?? trade.CommencementDate;
            trade.CompletionDate = tradeDto.CompletionDate ?? trade.CompletionDate;
            trade.WorkOrderNumber = tradeDto.WorkOrderNumber;
            trade.QuotesFile = tradeDto.QuotesFile;
            trade.PrelettingFile = tradeDto.PrelettingFile;
            trade.SignedContractFile = tradeDto.SignedContractFile;
            trade.CapeopleId = tradeDto.CapeopleId ?? trade.CapeopleId;
            trade.PmpeopleId = tradeDto.PmpeopleId ?? trade.PmpeopleId;
            trade.Flag = tradeDto.Flag ?? trade.Flag;
            trade.ModifiedDate = DateTime.UtcNow;
            trade.ModifiedPeopleId = _userContextService.GetCurrentUserId();

            await _context.SaveChangesAsync();

            return await GetTradeByIdAsync(id);
        }

        public async Task<bool> DeleteTradeAsync(int id)
        {
            var trade = await _context.Trades.FindAsync(id);

            if (trade == null)
                return false;
             
            var hasBudgetsTrades = await _context.BudgetsTrades.AnyAsync(bt => bt.TradeId == id);
            var hasTradeItemCategories = await _context.TradeItemCategories.AnyAsync(tic => tic.TradeId == id);
            var hasTradeParticipations = await _context.TradeParticipations.AnyAsync(tp => tp.TradeId == id);
            var hasContracts = await _context.Contracts.AnyAsync(c => c.TradeId == id);

            if (hasBudgetsTrades || hasTradeItemCategories || hasTradeParticipations || hasContracts)
            {
                throw new InvalidOperationException("Cannot delete trade with related entities");
            }

            _context.Trades.Remove(trade);
            await _context.SaveChangesAsync();

            return true;
        }

        private static TradeDTO MapToTradeDTO(Trade trade)
        {
            return new TradeDTO
            {
                TradeId = trade.TradeId,
                JobTypeId = trade.JobTypeId,
                ProjectId = trade.ProjectId,
                ProcessId = trade.ProcessId,
                ContractId = trade.ContractId,
                Name = trade.Name,
                Code = trade.Code,
                TenderRequired = trade.TenderRequired,
                Description = trade.Description,
                ScopeHeader = trade.ScopeHeader,
                ScopeFooter = trade.ScopeFooter,
                DisplayOrder = trade.DisplayOrder,
                DaysFromPcd = trade.DaysFromPcd,
                InvitationDate = trade.InvitationDate,
                DueDate = trade.DueDate,
                ComparisonDueDate = trade.ComparisonDueDate,
                ContractDueDate = trade.ContractDueDate,
                ComparisonApprovalDate = trade.ComparisonApprovalDate,
                ComparisonApprovalAmount = trade.ComparisonApprovalAmount,
                CommencementDate = trade.CommencementDate,
                CompletionDate = trade.CompletionDate,
                WorkOrderNumber = trade.WorkOrderNumber,
                QuotesFile = trade.QuotesFile,
                PrelettingFile = trade.PrelettingFile,
                SignedContractFile = trade.SignedContractFile,
                CapeopleId = trade.CapeopleId,
                PmpeopleId = trade.PmpeopleId,
                Flag = trade.Flag,
                JobTypeName = trade.JobType?.Name,
                ProjectName = trade.Project?.Name,
                ContractNumber = trade.Contract?.Number,
                CapeopleName = trade.Capeople != null ? $"{trade.Capeople.FirstName} {trade.Capeople.LastName}" : null,
                PmpeopleName = trade.Pmpeople != null ? $"{trade.Pmpeople.FirstName} {trade.Pmpeople.LastName}" : null
            };
        }


    }
}
