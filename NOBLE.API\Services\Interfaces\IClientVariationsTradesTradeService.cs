using NOBLE.API.Models.DTOs;

namespace NOBLE.API.Services.Interfaces
{
    public interface IClientVariationsTradesTradeService
    {
        Task<IEnumerable<ClientVariationsTradesTradeDTO>> GetAllClientVariationsTradesTradesAsync();
        Task<ClientVariationsTradesTradeDTO> GetClientVariationsTradesTradeByIdsAsync(int clientVariationTradeId, int tradeId);
        Task<IEnumerable<ClientVariationsTradesTradeDTO>> GetClientVariationsTradesTradesByClientVariationTradeIdAsync(int clientVariationTradeId);
        Task<IEnumerable<ClientVariationsTradesTradeDTO>> GetClientVariationsTradesTradesByTradeIdAsync(int tradeId);
        Task<ClientVariationsTradesTradeDTO> CreateClientVariationsTradesTradeAsync(ClientVariationsTradesTradeCreateDTO clientVariationsTradesTradeDto);
        Task<ClientVariationsTradesTradeDTO> UpdateClientVariationsTradesTradeAsync(int clientVariationTradeId, int tradeId, ClientVariationsTradesTradeUpdateDTO clientVariationsTradesTradeDto);
        Task<bool> DeleteClientVariationsTradesTradeAsync(int clientVariationTradeId, int tradeId);
    }
}
