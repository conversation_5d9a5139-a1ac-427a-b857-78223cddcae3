using Microsoft.EntityFrameworkCore;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Models.Generated;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Services.Implementations
{
    public class ClaimService : IClaimService
    {
        private readonly NobleDbContext _context;
        private readonly IUserContextService _userContextService;

        public ClaimService(NobleDbContext context, IUserContextService userContextService)
        {
            _context = context;
            _userContextService = userContextService;
        }

        public async Task<IEnumerable<ClaimDTO>> GetAllClaimsAsync()
        {
            return await _context.Claims
                .Include(c => c.Project)
                .Select(c => new ClaimDTO
                {
                    ClaimId = c.ClaimId,
                    ProjectId = c.ProjectId,
                    ProcessId = c.ProcessId,
                    Number = c.Number,
                    WriteDate = c.WriteDate,
                    DraftApprovalDate = c.DraftApprovalDate,
                    InternalApprovalDate = c.InternalApprovalDate,
                    ApprovalDate = c.ApprovalDate,
                    DueDate = c.DueDate,
                    ClientDueDate = c.ClientDueDate,
                    GoodsServicesTax = c.GoodsServicesTax,
                    AdjustmentNoteAmount = c.AdjustmentNoteAmount,
                    AdjustmentNoteName = c.AdjustmentNoteName,
                    BackupFile1 = c.BackupFile1,
                    BackupFile2 = c.BackupFile2,
                    ProjectName = c.Project != null ? c.Project.Name : null
                })
                .ToListAsync();
        }

        public async Task<ClaimDTO> GetClaimByIdAsync(int id)
        {
            var claim = await _context.Claims
                .Include(c => c.Project)
                .FirstOrDefaultAsync(c => c.ClaimId == id);
            
            if (claim == null)
                throw new KeyNotFoundException($"Claim with ID {id} not found");
                
            return new ClaimDTO
            {
                ClaimId = claim.ClaimId,
                ProjectId = claim.ProjectId,
                ProcessId = claim.ProcessId,
                Number = claim.Number,
                WriteDate = claim.WriteDate,
                DraftApprovalDate = claim.DraftApprovalDate,
                InternalApprovalDate = claim.InternalApprovalDate,
                ApprovalDate = claim.ApprovalDate,
                DueDate = claim.DueDate,
                ClientDueDate = claim.ClientDueDate,
                GoodsServicesTax = claim.GoodsServicesTax,
                AdjustmentNoteAmount = claim.AdjustmentNoteAmount,
                AdjustmentNoteName = claim.AdjustmentNoteName,
                BackupFile1 = claim.BackupFile1,
                BackupFile2 = claim.BackupFile2,
                ProjectName = claim.Project != null ? claim.Project.Name : null
            };
        }

        public async Task<IEnumerable<ClaimDTO>> GetClaimsByProjectIdAsync(int projectId)
        {
            // Check if the project exists
            var project = await _context.Projects.FindAsync(projectId);
            if (project == null)
                throw new KeyNotFoundException($"Project with ID {projectId} not found");
                
            return await _context.Claims
                .Include(c => c.Project)
                .Where(c => c.ProjectId == projectId)
                .Select(c => new ClaimDTO
                {
                    ClaimId = c.ClaimId,
                    ProjectId = c.ProjectId,
                    ProcessId = c.ProcessId,
                    Number = c.Number,
                    WriteDate = c.WriteDate,
                    DraftApprovalDate = c.DraftApprovalDate,
                    InternalApprovalDate = c.InternalApprovalDate,
                    ApprovalDate = c.ApprovalDate,
                    DueDate = c.DueDate,
                    ClientDueDate = c.ClientDueDate,
                    GoodsServicesTax = c.GoodsServicesTax,
                    AdjustmentNoteAmount = c.AdjustmentNoteAmount,
                    AdjustmentNoteName = c.AdjustmentNoteName,
                    BackupFile1 = c.BackupFile1,
                    BackupFile2 = c.BackupFile2,
                    ProjectName = c.Project != null ? c.Project.Name : null
                })
                .ToListAsync();
        }

        public async Task<ClaimDTO> CreateClaimAsync(ClaimCreateDTO claimDto)
        {
            // Check if the project exists
            var project = await _context.Projects.FindAsync(claimDto.ProjectId);
            if (project == null)
                throw new KeyNotFoundException($"Project with ID {claimDto.ProjectId} not found");
                
            var claim = new Claim
            {
                ProjectId = claimDto.ProjectId,
                ProcessId = claimDto.ProcessId,
                Number = claimDto.Number,
                WriteDate = claimDto.WriteDate,
                DraftApprovalDate = claimDto.DraftApprovalDate,
                InternalApprovalDate = claimDto.InternalApprovalDate,
                ApprovalDate = claimDto.ApprovalDate,
                DueDate = claimDto.DueDate,
                ClientDueDate = claimDto.ClientDueDate,
                GoodsServicesTax = claimDto.GoodsServicesTax,
                AdjustmentNoteAmount = claimDto.AdjustmentNoteAmount,
                AdjustmentNoteName = claimDto.AdjustmentNoteName,
                BackupFile1 = claimDto.BackupFile1,
                BackupFile2 = claimDto.BackupFile2,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow,
                CreatedPeopleId = _userContextService.GetCurrentUserId(),
                ModifiedPeopleId = _userContextService.GetCurrentUserId()
            };
            
            _context.Claims.Add(claim);
            await _context.SaveChangesAsync();
            
            return await GetClaimByIdAsync(claim.ClaimId);
        }

        public async Task<ClaimDTO> UpdateClaimAsync(int id, ClaimUpdateDTO claimDto)
        {
            var claim = await _context.Claims.FindAsync(id);
            
            if (claim == null)
                throw new KeyNotFoundException($"Claim with ID {id} not found");
                
            // Check if the project exists
            var project = await _context.Projects.FindAsync(claimDto.ProjectId);
            if (project == null)
                throw new KeyNotFoundException($"Project with ID {claimDto.ProjectId} not found");
                
            claim.ProjectId = claimDto.ProjectId;
            claim.ProcessId = claimDto.ProcessId;
            claim.Number = claimDto.Number;
            claim.WriteDate = claimDto.WriteDate;
            claim.DraftApprovalDate = claimDto.DraftApprovalDate;
            claim.InternalApprovalDate = claimDto.InternalApprovalDate;
            claim.ApprovalDate = claimDto.ApprovalDate;
            claim.DueDate = claimDto.DueDate;
            claim.ClientDueDate = claimDto.ClientDueDate;
            claim.GoodsServicesTax = claimDto.GoodsServicesTax;
            claim.AdjustmentNoteAmount = claimDto.AdjustmentNoteAmount;
            claim.AdjustmentNoteName = claimDto.AdjustmentNoteName;
            claim.BackupFile1 = claimDto.BackupFile1;
            claim.BackupFile2 = claimDto.BackupFile2;
            claim.ModifiedDate = DateTime.UtcNow;
            claim.ModifiedPeopleId = _userContextService.GetCurrentUserId();
            
            await _context.SaveChangesAsync();
            
            return await GetClaimByIdAsync(id);
        }

        public async Task<bool> DeleteClaimAsync(int id)
        {
            var claim = await _context.Claims.FindAsync(id);
            
            if (claim == null)
                return false;
                
            _context.Claims.Remove(claim);
            await _context.SaveChangesAsync();
            
            return true;
        }
    }
}
