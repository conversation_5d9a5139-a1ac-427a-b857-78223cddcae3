using NOBLE.API.Models.DTOs;

namespace NOBLE.API.Services.Interfaces
{
    public interface IProjectService
    {
        Task<IEnumerable<ProjectDTO>> GetAllProjectsAsync();
        Task<ProjectDTO> GetProjectByIdAsync(int id);
        Task<IEnumerable<ProjectDTO>> GetProjectsByBusinessUnitAsync(int businessUnitId);
        Task<ProjectDTO> CreateProjectAsync(ProjectCreateDTO projectDto);
        Task<ProjectDTO> UpdateProjectAsync(int id, ProjectUpdateDTO projectDto);
        Task<bool> DeleteProjectAsync(int id);
    }
}
