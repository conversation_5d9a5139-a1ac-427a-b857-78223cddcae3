using NOBLE.API.Models.DTOs;

namespace NOBLE.API.Services.Interfaces
{
    public interface ITradeItemCategoryService
    {
        Task<IEnumerable<TradeItemCategoryDTO>> GetAllTradeItemCategoriesAsync();
        Task<TradeItemCategoryDTO> GetTradeItemCategoryByIdAsync(int id);
        Task<IEnumerable<TradeItemCategoryDTO>> GetTradeItemCategoriesByTradeIdAsync(int tradeId);
        Task<TradeItemCategoryDTO> CreateTradeItemCategoryAsync(TradeItemCategoryCreateDTO tradeItemCategoryDto);
        Task<TradeItemCategoryDTO> UpdateTradeItemCategoryAsync(int id, TradeItemCategoryUpdateDTO tradeItemCategoryDto);
        Task<bool> DeleteTradeItemCategoryAsync(int id);
    }
}
