using Microsoft.EntityFrameworkCore;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Models.Generated;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Services.Implementations
{
    public class InvitationTemplateService : IInvitationTemplateService
    {
        private readonly NobleDbContext _context;
        private readonly IUserContextService _userContextService;

        public InvitationTemplateService(NobleDbContext context, IUserContextService userContextService)
        {
            _context = context;
            _userContextService = userContextService;
        }

        public async Task<IEnumerable<InvitationTemplateDTO>> GetAllInvitationTemplatesAsync()
        {
            var templates = await _context.InvitationTemplates.ToListAsync();
            var result = new List<InvitationTemplateDTO>();
            
            foreach (var template in templates)
            {
                var dto = new InvitationTemplateDTO
                {
                    InvitationTemplateId = template.InvitationTemplateId,
                    Template = template.Template,
                    CreatedDate = template.CreatedDate,
                    ModifiedDate = template.ModifiedDate
                };
                
                // Get created by person name
                var createdByPerson = await _context.People.FindAsync(template.CreatedPeopleId);
                if (createdByPerson != null)
                {
                    dto.CreatedByName = $"{createdByPerson.FirstName} {createdByPerson.LastName}".Trim();
                }
                
                // Get modified by person name
                var modifiedByPerson = await _context.People.FindAsync(template.ModifiedPeopleId);
                if (modifiedByPerson != null)
                {
                    dto.ModifiedByName = $"{modifiedByPerson.FirstName} {modifiedByPerson.LastName}".Trim();
                }
                
                result.Add(dto);
            }
            
            return result;
        }

        public async Task<InvitationTemplateDTO> GetInvitationTemplateByIdAsync(int id)
        {
            var template = await _context.InvitationTemplates.FindAsync(id);
            
            if (template == null)
                throw new KeyNotFoundException($"InvitationTemplate with ID {id} not found");
                
            var dto = new InvitationTemplateDTO
            {
                InvitationTemplateId = template.InvitationTemplateId,
                Template = template.Template,
                CreatedDate = template.CreatedDate,
                ModifiedDate = template.ModifiedDate
            };
            
            // Get created by person name
            var createdByPerson = await _context.People.FindAsync(template.CreatedPeopleId);
            if (createdByPerson != null)
            {
                dto.CreatedByName = $"{createdByPerson.FirstName} {createdByPerson.LastName}".Trim();
            }
            
            // Get modified by person name
            var modifiedByPerson = await _context.People.FindAsync(template.ModifiedPeopleId);
            if (modifiedByPerson != null)
            {
                dto.ModifiedByName = $"{modifiedByPerson.FirstName} {modifiedByPerson.LastName}".Trim();
            }
            
            return dto;
        }

        public async Task<InvitationTemplateDTO> CreateInvitationTemplateAsync(InvitationTemplateCreateDTO invitationTemplateDto)
        {
            var currentUserId = _userContextService.GetCurrentUserId();
            
            var template = new InvitationTemplate
            {
                Template = invitationTemplateDto.Template,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow,
                CreatedPeopleId = currentUserId,
                ModifiedPeopleId = currentUserId
            };
            
            _context.InvitationTemplates.Add(template);
            await _context.SaveChangesAsync();
            
            return await GetInvitationTemplateByIdAsync(template.InvitationTemplateId);
        }

        public async Task<InvitationTemplateDTO> UpdateInvitationTemplateAsync(int id, InvitationTemplateUpdateDTO invitationTemplateDto)
        {
            var template = await _context.InvitationTemplates.FindAsync(id);
            
            if (template == null)
                throw new KeyNotFoundException($"InvitationTemplate with ID {id} not found");
                
            template.Template = invitationTemplateDto.Template;
            template.ModifiedDate = DateTime.UtcNow;
            template.ModifiedPeopleId = _userContextService.GetCurrentUserId();
            
            await _context.SaveChangesAsync();
            
            return await GetInvitationTemplateByIdAsync(id);
        }

        public async Task<bool> DeleteInvitationTemplateAsync(int id)
        {
            var template = await _context.InvitationTemplates.FindAsync(id);
            
            if (template == null)
                return false;
                
            _context.InvitationTemplates.Remove(template);
            await _context.SaveChangesAsync();
            
            return true;
        }
    }
}
