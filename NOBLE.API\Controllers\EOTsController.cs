using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class EOTsController : ControllerBase
    {
        private readonly IEOTService _eotService;
        
        public EOTsController(IEOTService eotService)
        {
            _eotService = eotService;
        }
        
        // GET: api/EOTs
        [HttpGet]
        public async Task<ActionResult<IEnumerable<EOTDTO>>> GetEOTs()
        {
            var eots = await _eotService.GetAllEOTsAsync();
            return Ok(eots);
        }
        
        // GET: api/EOTs/5
        [HttpGet("{id}")]
        public async Task<ActionResult<EOTDTO>> GetEOT(int id)
        {
            try
            {
                var eot = await _eotService.GetEOTByIdAsync(id);
                return Ok(eot);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // GET: api/EOTs/Project/5
        [HttpGet("Project/{projectId}")]
        public async Task<ActionResult<IEnumerable<EOTDTO>>> GetEOTsByProject(int projectId)
        {
            try
            {
                var eots = await _eotService.GetEOTsByProjectIdAsync(projectId);
                return Ok(eots);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // POST: api/EOTs
        [HttpPost]
        public async Task<ActionResult<EOTDTO>> CreateEOT(EOTCreateDTO eotDto)
        {
            try
            {
                var createdEOT = await _eotService.CreateEOTAsync(eotDto);
                return CreatedAtAction(nameof(GetEOT), new { id = createdEOT.EOTId }, createdEOT);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // PUT: api/EOTs/5
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateEOT(int id, EOTUpdateDTO eotDto)
        {
            try
            {
                var updatedEOT = await _eotService.UpdateEOTAsync(id, eotDto);
                return Ok(updatedEOT);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // DELETE: api/EOTs/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteEOT(int id)
        {
            try
            {
                var result = await _eotService.DeleteEOTAsync(id);
                
                if (!result)
                    return NotFound(new { message = $"EOT with ID {id} not found" });
                    
                return NoContent();
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
    }
}
