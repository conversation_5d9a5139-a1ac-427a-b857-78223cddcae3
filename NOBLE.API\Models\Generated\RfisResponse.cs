﻿using System;
using System.Collections.Generic;

namespace NOBLE.API.Models.Generated;

public partial class RfisResponse
{
    public int ResponseId { get; set; }

    public int Rfiid { get; set; }

    public int? Rfinumber { get; set; }

    public int? ResponseNumber { get; set; }

    public string? ResponseFrom { get; set; }

    public string? ResponseMessage { get; set; }

    public DateTime? ResponseDate { get; set; }

    public string? ResponseFolderPath { get; set; }

    public DateTime? CreatedDate { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public string? ModifiedBy { get; set; }

    public virtual Rfi Rfi { get; set; } = null!;
}
