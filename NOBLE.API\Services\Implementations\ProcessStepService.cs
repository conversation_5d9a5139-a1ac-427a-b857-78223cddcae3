using Microsoft.EntityFrameworkCore;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Models.Generated;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Services.Implementations
{
    public class ProcessStepService : IProcessStepService
    {
        private readonly NobleDbContext _context;
        private readonly IUserContextService _userContextService;

        public ProcessStepService(NobleDbContext context, IUserContextService userContextService)
        {
            _context = context;
            _userContextService = userContextService;
        }

        public async Task<IEnumerable<ProcessStepDTO>> GetAllProcessStepsAsync()
        {
            var processSteps = await _context.ProcessSteps
                .Include(ps => ps.Process)
                .ToListAsync();
                
            var result = new List<ProcessStepDTO>();
            
            foreach (var step in processSteps)
            {
                var dto = new ProcessStepDTO
                {
                    ProcessStepId = step.ProcessStepId,
                    ProcessId = step.ProcessId,
                    AssignedPeopleId = step.AssignedPeopleId,
                    ApprovedPeopleId = step.ApprovedPeopleId,
                    Type = step.Type,
                    Name = step.Name,
                    UserType = step.UserType,
                    NumDays = step.NumDays,
                    TargetDate = step.TargetDate,
                    ActualDate = step.ActualDate,
                    Comments = step.Comments,
                    Skip = step.Skip,
                    ProcessName = step.Process?.Name
                };
                
                // Get assigned person name
                if (step.AssignedPeopleId.HasValue)
                {
                    var assignedPerson = await _context.People.FindAsync(step.AssignedPeopleId.Value);
                    if (assignedPerson != null)
                    {
                        dto.AssignedPersonName = $"{assignedPerson.FirstName} {assignedPerson.LastName}".Trim();
                    }
                }
                
                // Get approved person name
                if (step.ApprovedPeopleId.HasValue)
                {
                    var approvedPerson = await _context.People.FindAsync(step.ApprovedPeopleId.Value);
                    if (approvedPerson != null)
                    {
                        dto.ApprovedPersonName = $"{approvedPerson.FirstName} {approvedPerson.LastName}".Trim();
                    }
                }
                
                result.Add(dto);
            }
            
            return result;
        }

        public async Task<ProcessStepDTO> GetProcessStepByIdAsync(int id)
        {
            var processStep = await _context.ProcessSteps
                .Include(ps => ps.Process)
                .FirstOrDefaultAsync(ps => ps.ProcessStepId == id);
            
            if (processStep == null)
                throw new KeyNotFoundException($"ProcessStep with ID {id} not found");
                
            var dto = new ProcessStepDTO
            {
                ProcessStepId = processStep.ProcessStepId,
                ProcessId = processStep.ProcessId,
                AssignedPeopleId = processStep.AssignedPeopleId,
                ApprovedPeopleId = processStep.ApprovedPeopleId,
                Type = processStep.Type,
                Name = processStep.Name,
                UserType = processStep.UserType,
                NumDays = processStep.NumDays,
                TargetDate = processStep.TargetDate,
                ActualDate = processStep.ActualDate,
                Comments = processStep.Comments,
                Skip = processStep.Skip,
                ProcessName = processStep.Process?.Name
            };
            
            // Get assigned person name
            if (processStep.AssignedPeopleId.HasValue)
            {
                var assignedPerson = await _context.People.FindAsync(processStep.AssignedPeopleId.Value);
                if (assignedPerson != null)
                {
                    dto.AssignedPersonName = $"{assignedPerson.FirstName} {assignedPerson.LastName}".Trim();
                }
            }
            
            // Get approved person name
            if (processStep.ApprovedPeopleId.HasValue)
            {
                var approvedPerson = await _context.People.FindAsync(processStep.ApprovedPeopleId.Value);
                if (approvedPerson != null)
                {
                    dto.ApprovedPersonName = $"{approvedPerson.FirstName} {approvedPerson.LastName}".Trim();
                }
            }
            
            return dto;
        }

        public async Task<IEnumerable<ProcessStepDTO>> GetProcessStepsByProcessIdAsync(int processId)
        {
            // Check if the process exists
            var process = await _context.Processes.FindAsync(processId);
            if (process == null)
                throw new KeyNotFoundException($"Process with ID {processId} not found");
                
            var processSteps = await _context.ProcessSteps
                .Where(ps => ps.ProcessId == processId)
                .ToListAsync();
                
            var result = new List<ProcessStepDTO>();
            
            foreach (var step in processSteps)
            {
                var dto = new ProcessStepDTO
                {
                    ProcessStepId = step.ProcessStepId,
                    ProcessId = step.ProcessId,
                    AssignedPeopleId = step.AssignedPeopleId,
                    ApprovedPeopleId = step.ApprovedPeopleId,
                    Type = step.Type,
                    Name = step.Name,
                    UserType = step.UserType,
                    NumDays = step.NumDays,
                    TargetDate = step.TargetDate,
                    ActualDate = step.ActualDate,
                    Comments = step.Comments,
                    Skip = step.Skip,
                    ProcessName = process.Name
                };
                
                // Get assigned person name
                if (step.AssignedPeopleId.HasValue)
                {
                    var assignedPerson = await _context.People.FindAsync(step.AssignedPeopleId.Value);
                    if (assignedPerson != null)
                    {
                        dto.AssignedPersonName = $"{assignedPerson.FirstName} {assignedPerson.LastName}".Trim();
                    }
                }
                
                // Get approved person name
                if (step.ApprovedPeopleId.HasValue)
                {
                    var approvedPerson = await _context.People.FindAsync(step.ApprovedPeopleId.Value);
                    if (approvedPerson != null)
                    {
                        dto.ApprovedPersonName = $"{approvedPerson.FirstName} {approvedPerson.LastName}".Trim();
                    }
                }
                
                result.Add(dto);
            }
            
            return result;
        }

        public async Task<IEnumerable<ProcessStepDTO>> GetProcessStepsByAssignedPersonIdAsync(int assignedPersonId)
        {
            // Check if the person exists
            var person = await _context.People.FindAsync(assignedPersonId);
            if (person == null)
                throw new KeyNotFoundException($"Person with ID {assignedPersonId} not found");
                
            var processSteps = await _context.ProcessSteps
                .Include(ps => ps.Process)
                .Where(ps => ps.AssignedPeopleId == assignedPersonId)
                .ToListAsync();
                
            var result = new List<ProcessStepDTO>();
            
            foreach (var step in processSteps)
            {
                var dto = new ProcessStepDTO
                {
                    ProcessStepId = step.ProcessStepId,
                    ProcessId = step.ProcessId,
                    AssignedPeopleId = step.AssignedPeopleId,
                    ApprovedPeopleId = step.ApprovedPeopleId,
                    Type = step.Type,
                    Name = step.Name,
                    UserType = step.UserType,
                    NumDays = step.NumDays,
                    TargetDate = step.TargetDate,
                    ActualDate = step.ActualDate,
                    Comments = step.Comments,
                    Skip = step.Skip,
                    ProcessName = step.Process?.Name,
                    AssignedPersonName = $"{person.FirstName} {person.LastName}".Trim()
                };
                
                // Get approved person name
                if (step.ApprovedPeopleId.HasValue)
                {
                    var approvedPerson = await _context.People.FindAsync(step.ApprovedPeopleId.Value);
                    if (approvedPerson != null)
                    {
                        dto.ApprovedPersonName = $"{approvedPerson.FirstName} {approvedPerson.LastName}".Trim();
                    }
                }
                
                result.Add(dto);
            }
            
            return result;
        }

        public async Task<IEnumerable<ProcessStepDTO>> GetProcessStepsByTypeAsync(string type)
        {
            var processSteps = await _context.ProcessSteps
                .Include(ps => ps.Process)
                .Where(ps => ps.Type == type)
                .ToListAsync();
                
            var result = new List<ProcessStepDTO>();
            
            foreach (var step in processSteps)
            {
                var dto = new ProcessStepDTO
                {
                    ProcessStepId = step.ProcessStepId,
                    ProcessId = step.ProcessId,
                    AssignedPeopleId = step.AssignedPeopleId,
                    ApprovedPeopleId = step.ApprovedPeopleId,
                    Type = step.Type,
                    Name = step.Name,
                    UserType = step.UserType,
                    NumDays = step.NumDays,
                    TargetDate = step.TargetDate,
                    ActualDate = step.ActualDate,
                    Comments = step.Comments,
                    Skip = step.Skip,
                    ProcessName = step.Process?.Name
                };
                
                // Get assigned person name
                if (step.AssignedPeopleId.HasValue)
                {
                    var assignedPerson = await _context.People.FindAsync(step.AssignedPeopleId.Value);
                    if (assignedPerson != null)
                    {
                        dto.AssignedPersonName = $"{assignedPerson.FirstName} {assignedPerson.LastName}".Trim();
                    }
                }
                
                // Get approved person name
                if (step.ApprovedPeopleId.HasValue)
                {
                    var approvedPerson = await _context.People.FindAsync(step.ApprovedPeopleId.Value);
                    if (approvedPerson != null)
                    {
                        dto.ApprovedPersonName = $"{approvedPerson.FirstName} {approvedPerson.LastName}".Trim();
                    }
                }
                
                result.Add(dto);
            }
            
            return result;
        }

        public async Task<ProcessStepDTO> CreateProcessStepAsync(ProcessStepCreateDTO processStepDto)
        {
            // Check if the process exists
            var process = await _context.Processes.FindAsync(processStepDto.ProcessId);
            if (process == null)
                throw new KeyNotFoundException($"Process with ID {processStepDto.ProcessId} not found");
                
            // Check if the assigned person exists (if provided)
            if (processStepDto.AssignedPeopleId.HasValue)
            {
                var assignedPerson = await _context.People.FindAsync(processStepDto.AssignedPeopleId.Value);
                if (assignedPerson == null)
                    throw new KeyNotFoundException($"Assigned Person with ID {processStepDto.AssignedPeopleId.Value} not found");
            }
            
            // Check if the approved person exists (if provided)
            if (processStepDto.ApprovedPeopleId.HasValue)
            {
                var approvedPerson = await _context.People.FindAsync(processStepDto.ApprovedPeopleId.Value);
                if (approvedPerson == null)
                    throw new KeyNotFoundException($"Approved Person with ID {processStepDto.ApprovedPeopleId.Value} not found");
            }
                
            var processStep = new ProcessStep
            {
                ProcessId = processStepDto.ProcessId,
                AssignedPeopleId = processStepDto.AssignedPeopleId,
                ApprovedPeopleId = processStepDto.ApprovedPeopleId,
                Type = processStepDto.Type,
                Name = processStepDto.Name,
                UserType = processStepDto.UserType,
                NumDays = processStepDto.NumDays,
                TargetDate = processStepDto.TargetDate,
                ActualDate = processStepDto.ActualDate,
                Comments = processStepDto.Comments,
                Skip = processStepDto.Skip,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow,
                CreatedPeopleId = _userContextService.GetCurrentUserId(),
                ModifiedPeopleId = _userContextService.GetCurrentUserId()
            };
            
            _context.ProcessSteps.Add(processStep);
            await _context.SaveChangesAsync();
            
            return await GetProcessStepByIdAsync(processStep.ProcessStepId);
        }

        public async Task<ProcessStepDTO> UpdateProcessStepAsync(int id, ProcessStepUpdateDTO processStepDto)
        {
            var processStep = await _context.ProcessSteps.FindAsync(id);
            
            if (processStep == null)
                throw new KeyNotFoundException($"ProcessStep with ID {id} not found");
                
            // Check if the assigned person exists (if provided)
            if (processStepDto.AssignedPeopleId.HasValue)
            {
                var assignedPerson = await _context.People.FindAsync(processStepDto.AssignedPeopleId.Value);
                if (assignedPerson == null)
                    throw new KeyNotFoundException($"Assigned Person with ID {processStepDto.AssignedPeopleId.Value} not found");
                    
                processStep.AssignedPeopleId = processStepDto.AssignedPeopleId;
            }
            
            // Check if the approved person exists (if provided)
            if (processStepDto.ApprovedPeopleId.HasValue)
            {
                var approvedPerson = await _context.People.FindAsync(processStepDto.ApprovedPeopleId.Value);
                if (approvedPerson == null)
                    throw new KeyNotFoundException($"Approved Person with ID {processStepDto.ApprovedPeopleId.Value} not found");
                    
                processStep.ApprovedPeopleId = processStepDto.ApprovedPeopleId;
            }
                
            if (processStepDto.Type != null)
                processStep.Type = processStepDto.Type;
                
            if (processStepDto.Name != null)
                processStep.Name = processStepDto.Name;
                
            if (processStepDto.UserType != null)
                processStep.UserType = processStepDto.UserType;
                
            if (processStepDto.NumDays.HasValue)
                processStep.NumDays = processStepDto.NumDays;
                
            if (processStepDto.TargetDate.HasValue)
                processStep.TargetDate = processStepDto.TargetDate;
                
            if (processStepDto.ActualDate.HasValue)
                processStep.ActualDate = processStepDto.ActualDate;
                
            if (processStepDto.Comments != null)
                processStep.Comments = processStepDto.Comments;
                
            if (processStepDto.Skip.HasValue)
                processStep.Skip = processStepDto.Skip;
                
            processStep.ModifiedDate = DateTime.UtcNow;
            processStep.ModifiedPeopleId = _userContextService.GetCurrentUserId();
            
            await _context.SaveChangesAsync();
            
            return await GetProcessStepByIdAsync(id);
        }

        public async Task<bool> DeleteProcessStepAsync(int id)
        {
            var processStep = await _context.ProcessSteps.FindAsync(id);
            
            if (processStep == null)
                return false;
                
            _context.ProcessSteps.Remove(processStep);
            await _context.SaveChangesAsync();
            
            return true;
        }
    }
}
