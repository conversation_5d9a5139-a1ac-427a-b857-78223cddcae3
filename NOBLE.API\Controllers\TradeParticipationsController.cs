using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NOBLE.API.Models.DTOs;
using NOBLE.API.Services.Interfaces;

namespace NOBLE.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class TradeParticipationsController : ControllerBase
    {
        private readonly ITradeParticipationService _tradeParticipationService;
        
        public TradeParticipationsController(ITradeParticipationService tradeParticipationService)
        {
            _tradeParticipationService = tradeParticipationService;
        }
        
        // GET: api/TradeParticipations
        [HttpGet]
        public async Task<ActionResult<IEnumerable<TradeParticipationDTO>>> GetTradeParticipations()
        {
            var tradeParticipations = await _tradeParticipationService.GetAllTradeParticipationsAsync();
            return Ok(tradeParticipations);
        }
        
        // GET: api/TradeParticipations/5
        [HttpGet("{id}")]
        public async Task<ActionResult<TradeParticipationDTO>> GetTradeParticipation(int id)
        {
            try
            {
                var tradeParticipation = await _tradeParticipationService.GetTradeParticipationByIdAsync(id);
                return Ok(tradeParticipation);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // GET: api/TradeParticipations/Trade/5
        [HttpGet("Trade/{tradeId}")]
        public async Task<ActionResult<IEnumerable<TradeParticipationDTO>>> GetTradeParticipationsByTrade(int tradeId)
        {
            try
            {
                var tradeParticipations = await _tradeParticipationService.GetTradeParticipationsByTradeIdAsync(tradeId);
                return Ok(tradeParticipations);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // GET: api/TradeParticipations/SubContractor/5
        [HttpGet("SubContractor/{subContractorId}")]
        public async Task<ActionResult<IEnumerable<TradeParticipationDTO>>> GetTradeParticipationsBySubContractor(int subContractorId)
        {
            try
            {
                var tradeParticipations = await _tradeParticipationService.GetTradeParticipationsBySubContractorIdAsync(subContractorId);
                return Ok(tradeParticipations);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // GET: api/TradeParticipations/Children/5
        [HttpGet("Children/{parentId}")]
        public async Task<ActionResult<IEnumerable<TradeParticipationDTO>>> GetChildTradeParticipations(int parentId)
        {
            try
            {
                var childTradeParticipations = await _tradeParticipationService.GetChildTradeParticipationsAsync(parentId);
                return Ok(childTradeParticipations);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        
        // POST: api/TradeParticipations
        [HttpPost]
        public async Task<ActionResult<TradeParticipationDTO>> CreateTradeParticipation(TradeParticipationCreateDTO tradeParticipationDto)
        {
            try
            {
                var createdTradeParticipation = await _tradeParticipationService.CreateTradeParticipationAsync(tradeParticipationDto);
                return CreatedAtAction(nameof(GetTradeParticipation), new { id = createdTradeParticipation.TradeParticipationId }, createdTradeParticipation);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // PUT: api/TradeParticipations/5
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateTradeParticipation(int id, TradeParticipationUpdateDTO tradeParticipationDto)
        {
            try
            {
                var updatedTradeParticipation = await _tradeParticipationService.UpdateTradeParticipationAsync(id, tradeParticipationDto);
                return Ok(updatedTradeParticipation);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
        
        // DELETE: api/TradeParticipations/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteTradeParticipation(int id)
        {
            try
            {
                var result = await _tradeParticipationService.DeleteTradeParticipationAsync(id);
                
                if (!result)
                    return NotFound(new { message = $"TradeParticipation with ID {id} not found" });
                    
                return NoContent();
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
    }
}
